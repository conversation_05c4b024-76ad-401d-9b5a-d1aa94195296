# FastAPI and ASGI server
fastapi==0.115.12
uvicorn[standard]==0.34.0

# Database
sqlalchemy[asyncio]==2.0.36
asyncpg==0.30.0
alembic==1.15.0

# MongoDB - Compatible versions
motor==3.6.0
pymongo==4.9.0

# Redis
redis[hiredis]==5.3.0
aioredis==2.0.1

# Message Queue
aio-pika==9.5.5
celery==5.5.0

# Authentication & Security
python-jose[cryptography]==3.4.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.20

# Configuration & Environment
pydantic==2.11.6
pydantic-settings==2.7.0
python-dotenv==1.1.0

# HTTP Client
httpx==0.28.1
aiohttp==3.12.0

# Validation & Serialization
email-validator==2.2.0
python-dateutil==2.9.0.post0
jsonschema==4.23.0

# Logging & Monitoring
structlog==25.4.0
rich==14.0.0

# Background Tasks & Events
# inngest==0.5.0  # Optional - uncomment if using Inngest

# Utilities
typer==0.16.0
click==8.2.0

# Temporal SDK for Python
temporalio==1.5.1


# Fuzzy Search
fuzzywuzzy==0.18.0

# Credentials system
cryptography==45.0.4

simpleeval==1.0.3
