[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "cerebro"
version = "1.0.0"
description = "Modern FastAPI application with clean architecture"
readme = "README.md"
license = "MIT"
requires-python = ">=3.11"
authors = [
    {name = "Cerebro Team", email = "<EMAIL>"},
]
keywords = ["fastapi", "async", "api", "microservice", "clean-architecture"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Framework :: FastAPI",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi>=0.115.12",
    "uvicorn[standard]>=0.34.0",
    "sqlalchemy[asyncio]>=2.0.36",
    "asyncpg>=0.30.0",
    "alembic>=1.15.0",
    "motor>=3.7.0",
    "pymongo>=4.10.0",
    "redis[hiredis]>=5.3.0",
    "aioredis>=2.1.0",
    "aio-pika>=9.6.0",
    "celery>=5.5.0",
    "python-jose[cryptography]>=3.4.0",
    "passlib[bcrypt]>=1.8.0",
    "python-multipart>=0.0.20",
    "pydantic>=2.12.0",
    "pydantic-settings>=2.7.0",
    "python-dotenv>=1.1.0",
    "httpx>=0.30.0",
    "aiohttp>=3.12.0",
    "email-validator>=2.3.0",
    "python-dateutil>=2.10.0",
    "structlog>=25.0.0",
    "rich>=14.0.0",
    # "inngest>=0.5.0",  # Optional - uncomment if using Inngest
    "typer>=0.16.0",
    "click>=8.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.0",
    "pytest-asyncio>=0.25.0",
    "pytest-cov>=6.1.0",
    "pytest-mock>=3.15.0",
    "pytest-xdist>=3.7.0",
    "faker>=34.0.0",
    "black>=25.0.0",
    "isort>=5.14.0",
    "flake8>=7.2.0",
    "mypy>=1.14.0",
    "pre-commit>=4.1.0",
    "watchdog>=6.1.0",
    "ipython>=8.31.0",
    "ipdb>=0.14.0",
]
docs = [
    "mkdocs>=1.7.0",
    "mkdocs-material>=9.6.0",
]
test = [
    "pytest-postgresql>=6.2.0",
    "pytest-mongodb>=2.5.0",
    "locust>=2.33.0",
]

[project.urls]
Homepage = "https://github.com/cerebro-team/cerebro"
Documentation = "https://cerebro.readthedocs.io"
Repository = "https://github.com/cerebro-team/cerebro"
"Bug Tracker" = "https://github.com/cerebro-team/cerebro/issues"

[project.scripts]
cerebro = "app.main:main"

[tool.hatch.version]
path = "app/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["app"]

# Black configuration
[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100
known_first_party = ["app", "tests"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]

# mypy configuration
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "jose.*",
    "passlib.*",
    "motor.*",
    "pymongo.*",
    "redis.*",
    "aioredis.*",
    "aio_pika.*",
    "celery.*",
    "inngest.*",
    "faker.*",
]
ignore_missing_imports = true

# pytest configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "-ra",
    "-q",
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "api: marks tests as API tests",
    "auth: marks tests as authentication tests",
    "db: marks tests as database tests",
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Ruff configuration (alternative to flake8)
[tool.ruff]
target-version = "py311"
line-length = 100
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]

[tool.ruff.isort]
known-first-party = ["app", "tests"]
