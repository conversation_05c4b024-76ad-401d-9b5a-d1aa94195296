"""
Tests for credential endpoints
"""

import pytest
import uuid
from unittest.mock import patch, MagicMock

from fastapi import status
from httpx import AsyncClient

from app.core.security import encrypt_credential_data
from app.models.credential import Credential


@pytest.fixture
def mock_credential_registry():
    """Mock for the credential registry"""
    with patch("app.credential.utils.credential_registry.CredentialRegistry") as mock:
        credential_provider = MagicMock()
        credential_provider.authentication.return_value.method.value = "api_key"
        credential_provider.version = 1.0
        credential_provider.parameters = [
            MagicMock(name="api_key", sensitive=True)
        ]
        credential_provider.allowed_nodes = []
        
        mock.get.return_value = credential_provider
        mock.get_all_credentials.return_value = [
            {
                "name": "test_api",
                "display_name": "Test API",
                "description": "Test API credential"
            }
        ]
        yield mock


@pytest.fixture
def mock_credential_manager():
    """Mock for the credential manager"""
    with patch("app.credential.utils.credential_manager.CredentialManager") as mock:
        manager_instance = MagicMock()
        manager_instance.validate_credential.return_value = True
        manager_instance.test_credential.return_value = True
        
        mock.return_value = manager_instance
        yield mock


@pytest.fixture
async def test_credential(db_session):
    """Create a test credential in the database"""
    credential_data = {
        "api_key": {
            "value": "test-api-key-123",
            "is_sensitive": True
        }
    }
    
    encrypted_data = encrypt_credential_data(credential_data)
    
    credential = Credential(
        id=uuid.uuid4(),
        name="Test API Credential",
        type="test_api",
        auth_method="api_key",
        data=encrypted_data,
        status="active",
        created_by="test_user",
        is_deleted=False
    )
    
    db_session.add(credential)
    await db_session.commit()
    await db_session.refresh(credential)
    
    yield credential


class TestCredentialEndpoints:
    """Test suite for credential endpoints"""
    
    async def test_get_all_credentials(
        self, client: AsyncClient, mock_credential_registry
    ):
        """Test get all credentials endpoint"""
        response = await client.get("/api/v1/credentials/")
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        assert "name" in data[0]
        assert "display_name" in data[0]
    
    async def test_create_credential(
        self, client: AsyncClient, mock_credential_registry, mock_credential_manager
    ):
        """Test create credential endpoint"""
        credential_data = {
            "name": "test_api",
            "display_name": "Test API Credential",
            "version": 1.0,
            "parameters": {
                "api_key": "test-api-key-123"
            }
        }
        
        response = await client.post(
            "/api/v1/credentials/", 
            json=credential_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["status"] == "success"
        assert "credential" in data
        assert "id" in data["credential"]
        assert data["credential"]["type"] == "test_api"
    
    async def test_test_credential_request(
        self, client: AsyncClient, mock_credential_manager
    ):
        """Test the endpoint to test a credential request"""
        credential_data = {
            "name": "test_api",
            "display_name": "Test API Credential",
            "version": 1.0,
            "parameters": {
                "api_key": "test-api-key-123"
            }
        }
        
        response = await client.post(
            "/api/v1/credentials/test", 
            json=credential_data
        )
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["status"] == "success"
        assert "message" in data
    
    async def test_test_credential_by_id(
        self, client: AsyncClient, test_credential, mock_credential_registry, mock_credential_manager
    ):
        """Test the endpoint to test a credential by ID"""
        credential_id = str(test_credential.id)
        
        response = await client.post(f"/api/v1/credentials/test/{credential_id}")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["status"] == "success"
        assert "message" in data
        assert "credential_id" in data
        assert data["credential_id"] == credential_id
    
    async def test_test_credential_by_id_not_found(
        self, client: AsyncClient
    ):
        """Test the endpoint to test a credential by ID when the credential is not found"""
        non_existent_id = str(uuid.uuid4())
        
        response = await client.post(f"/api/v1/credentials/test/{non_existent_id}")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        
    async def test_test_credential_by_id_invalid_id(
        self, client: AsyncClient
    ):
        """Test the endpoint to test a credential by ID with an invalid ID format"""
        invalid_id = "not-a-uuid"
        
        response = await client.post(f"/api/v1/credentials/test/{invalid_id}")
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    async def test_get_credential_by_id(
        self, client: AsyncClient, test_credential
    ):
        """Test get credential by ID endpoint"""
        credential_id = str(test_credential.id)
        
        response = await client.get(f"/api/v1/credentials/{credential_id}")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert data["id"] == credential_id
        assert data["type"] == "test_api"
        assert "data" in data
        
        # Check that sensitive data is redacted
        credential_data = data["data"]
        assert "api_key" in credential_data
        assert credential_data["api_key"]["value"] == "***SENSITIVE_VALUE_HIDDEN***"
    
    async def test_get_credentials_by_type(
        self, client: AsyncClient, test_credential
    ):
        """Test get credentials by type endpoint"""
        response = await client.get(f"/api/v1/credentials/type/test_api")
        
        assert response.status_code == status.HTTP_200_OK
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0
        assert data[0]["type"] == "test_api"
