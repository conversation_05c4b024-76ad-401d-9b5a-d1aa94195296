"""
API tests for authentication endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import Async<PERSON>lient

from app.models.user import User
from tests.conftest import create_auth_headers, create_test_token


@pytest.mark.api
class TestAuthEndpoints:
    """Test authentication API endpoints."""
    
    @pytest.mark.asyncio
    async def test_register_success(self, async_client: AsyncClient):
        """Test successful user registration."""
        user_data = {
            "email": "<EMAIL>",
            "full_name": "New User",
            "password": "SecurePass123!",
            "is_active": True,
            "is_verified": False
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["full_name"] == "New User"
        assert data["is_active"] is True
        assert data["is_verified"] is False
        assert "id" in data
        assert "created_at" in data
    
    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, async_client: AsyncClient, test_user: User):
        """Test registration with duplicate email."""
        user_data = {
            "email": test_user.email,
            "full_name": "Duplicate User",
            "password": "SecurePass123!",
            "is_active": True
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 409
        data = response.json()
        assert "already exists" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_register_invalid_password(self, async_client: AsyncClient):
        """Test registration with invalid password."""
        user_data = {
            "email": "<EMAIL>",
            "full_name": "Weak Password User",
            "password": "weak",  # Too weak
            "is_active": True
        }
        
        response = await async_client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient, test_user: User):
        """Test successful login."""
        login_data = {
            "email": test_user.email,
            "password": "TestPassword123!"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, async_client: AsyncClient, test_user: User):
        """Test login with invalid credentials."""
        login_data = {
            "email": test_user.email,
            "password": "WrongPassword123!"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_login_nonexistent_user(self, async_client: AsyncClient):
        """Test login with non-existent user."""
        login_data = {
            "email": "<EMAIL>",
            "password": "SomePassword123!"
        }
        
        response = await async_client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid email or password" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, async_client: AsyncClient, test_user: User):
        """Test successful token refresh."""
        # First login to get tokens
        login_data = {
            "email": test_user.email,
            "password": "TestPassword123!"
        }
        
        login_response = await async_client.post("/api/v1/auth/login", json=login_data)
        login_data = login_response.json()
        refresh_token = login_data["refresh_token"]
        
        # Refresh token
        refresh_data = {"refresh_token": refresh_token}
        response = await async_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
    
    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, async_client: AsyncClient):
        """Test token refresh with invalid token."""
        refresh_data = {"refresh_token": "invalid_token"}
        response = await async_client.post("/api/v1/auth/refresh", json=refresh_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Invalid refresh token" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_get_current_user_profile(self, async_client: AsyncClient, test_user: User):
        """Test getting current user profile."""
        token = await create_test_token(test_user)
        headers = create_auth_headers(token)
        
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_user.id
        assert data["email"] == test_user.email
        assert data["full_name"] == test_user.full_name
        assert "roles" in data
        assert "permissions" in data
    
    @pytest.mark.asyncio
    async def test_get_current_user_profile_unauthorized(self, async_client: AsyncClient):
        """Test getting current user profile without authentication."""
        response = await async_client.get("/api/v1/auth/me")
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_get_current_user_profile_invalid_token(self, async_client: AsyncClient):
        """Test getting current user profile with invalid token."""
        headers = create_auth_headers("invalid_token")
        
        response = await async_client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_change_password_success(self, async_client: AsyncClient, test_user: User):
        """Test successful password change."""
        token = await create_test_token(test_user)
        headers = create_auth_headers(token)
        
        change_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewSecurePass123!"
        }
        
        response = await async_client.post("/api/v1/auth/change-password", json=change_data, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "changed successfully" in data["message"]
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, async_client: AsyncClient, test_user: User):
        """Test password change with wrong current password."""
        token = await create_test_token(test_user)
        headers = create_auth_headers(token)
        
        change_data = {
            "current_password": "WrongPassword123!",
            "new_password": "NewSecurePass123!"
        }
        
        response = await async_client.post("/api/v1/auth/change-password", json=change_data, headers=headers)
        
        assert response.status_code == 400
        data = response.json()
        assert "Current password is incorrect" in data["detail"]
    
    @pytest.mark.asyncio
    async def test_change_password_unauthorized(self, async_client: AsyncClient):
        """Test password change without authentication."""
        change_data = {
            "current_password": "TestPassword123!",
            "new_password": "NewSecurePass123!"
        }
        
        response = await async_client.post("/api/v1/auth/change-password", json=change_data)
        
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_request_password_reset(self, async_client: AsyncClient, test_user: User):
        """Test password reset request."""
        reset_data = {"email": test_user.email}
        
        response = await async_client.post("/api/v1/auth/password-reset/request", json=reset_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "reset link has been sent" in data["message"]
    
    @pytest.mark.asyncio
    async def test_request_password_reset_nonexistent_user(self, async_client: AsyncClient):
        """Test password reset request for non-existent user."""
        reset_data = {"email": "<EMAIL>"}
        
        response = await async_client.post("/api/v1/auth/password-reset/request", json=reset_data)
        
        # Should still return success to prevent email enumeration
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "reset link has been sent" in data["message"]
    
    @pytest.mark.asyncio
    async def test_logout_success(self, async_client: AsyncClient, test_user: User):
        """Test successful logout."""
        token = await create_test_token(test_user)
        headers = create_auth_headers(token)
        
        response = await async_client.post("/api/v1/auth/logout", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "Logged out successfully" in data["message"]
    
    @pytest.mark.asyncio
    async def test_logout_unauthorized(self, async_client: AsyncClient):
        """Test logout without authentication."""
        response = await async_client.post("/api/v1/auth/logout")
        
        assert response.status_code == 403
