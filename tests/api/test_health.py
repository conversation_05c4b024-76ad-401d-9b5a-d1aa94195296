"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import AsyncClient


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check(self, client: TestClient):
        """Test main health check endpoint."""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "services" in data
        
        # Check services
        services = data["services"]
        assert "postgresql" in services
        # Note: Other services might be unavailable in test environment
    
    def test_liveness_probe(self, client: TestClient):
        """Test liveness probe endpoint."""
        response = client.get("/api/v1/health/live")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "alive"
    
    def test_readiness_probe(self, client: TestClient):
        """Test readiness probe endpoint."""
        response = client.get("/api/v1/health/ready")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert data["status"] in ["ready", "not ready"]
    
    def test_startup_probe(self, client: TestClient):
        """Test startup probe endpoint."""
        response = client.get("/api/v1/health/startup")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "started"
    
    @pytest.mark.asyncio
    async def test_health_check_async(self, async_client: AsyncClient):
        """Test health check with async client."""
        response = await async_client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "services" in data
