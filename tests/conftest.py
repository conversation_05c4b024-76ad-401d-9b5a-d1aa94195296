"""
Pytest configuration and fixtures for comprehensive testing.
"""

import asyncio
import os
from typing import Async<PERSON>enerator, Generator

import pytest
import pytest_asyncio
from faker import Faker
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import Static<PERSON>ool

from app.core.config import get_test_settings
from app.core.database import Base, get_db
from app.main import app
from app.models.user import User
from app.models.role import Role, Permission
from app.repositories.user_repository import UserRepository
from app.repositories.role_repository import RoleRepository, PermissionRepository
from app.schemas.user import UserCreate
from app.core.security import get_password_hash

# Set test environment
os.environ["TESTING"] = "true"

# Test settings
test_settings = get_test_settings()

# Test database engine
test_engine = create_async_engine(
    str(test_settings.DATABASE_URL),
    poolclass=StaticPool,
    connect_args={"check_same_thread": False},
    echo=False,
)

# Test session factory
TestSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
)

# Faker instance for generating test data
fake = Faker()


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Create a test database session.
    
    Yields:
        AsyncSession: Test database session
    """
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestSessionLocal() as session:
        yield session
    
    # Drop tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """Override the get_db dependency for testing."""
    async def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


@pytest.fixture
def client(override_get_db) -> TestClient:
    """
    Create a test client.
    
    Returns:
        TestClient: FastAPI test client
    """
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(override_get_db) -> AsyncGenerator[AsyncClient, None]:
    """
    Create an async test client.
    
    Yields:
        AsyncClient: Async HTTP client for testing
    """
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture
async def test_permissions(db_session: AsyncSession) -> list[Permission]:
    """
    Create test permissions.
    
    Args:
        db_session: Test database session
        
    Returns:
        list[Permission]: List of test permissions
    """
    permission_repo = PermissionRepository(db_session)
    
    permissions_data = [
        {"name": "user:read", "resource": "user", "action": "read", "description": "Read user data"},
        {"name": "user:write", "resource": "user", "action": "write", "description": "Write user data"},
        {"name": "user:delete", "resource": "user", "action": "delete", "description": "Delete user data"},
        {"name": "admin:all", "resource": "admin", "action": "all", "description": "All admin actions"},
    ]
    
    permissions = []
    for perm_data in permissions_data:
        permission = await permission_repo.create_permission(**perm_data)
        permissions.append(permission)
    
    await db_session.commit()
    return permissions


@pytest_asyncio.fixture
async def test_roles(db_session: AsyncSession, test_permissions: list[Permission]) -> list[Role]:
    """
    Create test roles with permissions.
    
    Args:
        db_session: Test database session
        test_permissions: Test permissions
        
    Returns:
        list[Role]: List of test roles
    """
    role_repo = RoleRepository(db_session)
    
    # Create viewer role
    viewer_role = Role(name="viewer", description="Can view data", is_default=True)
    viewer_role.permissions = [test_permissions[0]]  # user:read
    db_session.add(viewer_role)
    
    # Create editor role
    editor_role = Role(name="editor", description="Can edit data")
    editor_role.permissions = test_permissions[:2]  # user:read, user:write
    db_session.add(editor_role)
    
    # Create admin role
    admin_role = Role(name="admin", description="Can do everything")
    admin_role.permissions = test_permissions  # All permissions
    db_session.add(admin_role)
    
    await db_session.commit()
    
    return [viewer_role, editor_role, admin_role]


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession, test_roles: list[Role]) -> User:
    """
    Create a test user.
    
    Args:
        db_session: Test database session
        test_roles: Test roles
        
    Returns:
        User: Test user
    """
    user_repo = UserRepository(db_session)
    
    user_data = UserCreate(
        email=fake.email(),
        full_name=fake.name(),
        password="TestPassword123!",
        is_active=True,
        is_verified=True,
    )
    
    user = await user_repo.create(user_data)
    user.hashed_password = get_password_hash("TestPassword123!")
    user.roles = [test_roles[0]]  # viewer role
    
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession, test_roles: list[Role]) -> User:
    """
    Create a test admin user.
    
    Args:
        db_session: Test database session
        test_roles: Test roles
        
    Returns:
        User: Test admin user
    """
    user_repo = UserRepository(db_session)
    
    user_data = UserCreate(
        email="<EMAIL>",
        full_name="Test Admin",
        password="AdminPassword123!",
        is_active=True,
        is_verified=True,
        is_superuser=True,
    )
    
    user = await user_repo.create(user_data)
    user.hashed_password = get_password_hash("AdminPassword123!")
    user.roles = [test_roles[2]]  # admin role
    
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest.fixture
def test_user_data() -> dict:
    """
    Generate test user data.
    
    Returns:
        dict: Test user data
    """
    return {
        "email": fake.email(),
        "full_name": fake.name(),
        "password": "TestPassword123!",
        "is_active": True,
        "is_verified": False,
    }


@pytest.fixture
def multiple_test_users_data() -> list[dict]:
    """
    Generate multiple test users data.
    
    Returns:
        list[dict]: List of test user data
    """
    return [
        {
            "email": fake.email(),
            "full_name": fake.name(),
            "password": "TestPassword123!",
            "is_active": True,
            "is_verified": fake.boolean(),
        }
        for _ in range(5)
    ]


# Utility functions for tests
def create_auth_headers(token: str) -> dict:
    """
    Create authorization headers for testing.
    
    Args:
        token: JWT token
        
    Returns:
        dict: Authorization headers
    """
    return {"Authorization": f"Bearer {token}"}


async def create_test_token(user: User) -> str:
    """
    Create a test JWT token for user.
    
    Args:
        user: User instance
        
    Returns:
        str: JWT token
    """
    from app.core.security import create_access_token
    return create_access_token(subject=str(user.id))
