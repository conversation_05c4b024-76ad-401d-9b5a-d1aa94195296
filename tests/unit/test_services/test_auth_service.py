"""
Unit tests for AuthService.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from app.models.user import User
from app.schemas.auth import LoginRequest, PasswordResetRequest, PasswordResetConfirm, ChangePasswordRequest
from app.schemas.user import UserCreate
from app.services.auth_service import AuthService
from app.utils.exceptions import Authentication<PERSON>rror, ConflictError, NotFoundError, ValidationError


class TestAuthService:
    """Test AuthService functionality."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock()
    
    @pytest.fixture
    def mock_user_repo(self):
        """Mock user repository."""
        return AsyncMock()
    
    @pytest.fixture
    def mock_role_repo(self):
        """Mock role repository."""
        return AsyncMock()
    
    @pytest.fixture
    def auth_service(self, mock_db, mock_user_repo, mock_role_repo):
        """Create AuthService instance with mocked dependencies."""
        service = AuthService(mock_db)
        service.user_repo = mock_user_repo
        service.role_repo = mock_role_repo
        return service
    
    @pytest.fixture
    def sample_user(self):
        """Create a sample user for testing."""
        user = User(
            id=1,
            email="<EMAIL>",
            full_name="Test User",
            hashed_password="$2b$12$hashed_password",
            is_active=True,
            is_verified=True,
            failed_login_attempts=0,
            locked_until=None
        )
        return user
    
    @pytest.mark.asyncio
    async def test_authenticate_user_success(self, auth_service, mock_user_repo, sample_user):
        """Test successful user authentication."""
        mock_user_repo.get_by_email.return_value = sample_user
        mock_user_repo.update_last_login = AsyncMock()
        
        with patch('app.services.auth_service.verify_password', return_value=True):
            result = await auth_service.authenticate_user("<EMAIL>", "password")
        
        assert result == sample_user
        mock_user_repo.get_by_email.assert_called_once_with("<EMAIL>")
        mock_user_repo.update_last_login.assert_called_once_with(sample_user.id)
    
    @pytest.mark.asyncio
    async def test_authenticate_user_not_found(self, auth_service, mock_user_repo):
        """Test authentication with non-existent user."""
        mock_user_repo.get_by_email.return_value = None
        
        result = await auth_service.authenticate_user("<EMAIL>", "password")
        
        assert result is None
        mock_user_repo.get_by_email.assert_called_once_with("<EMAIL>")
    
    @pytest.mark.asyncio
    async def test_authenticate_user_inactive(self, auth_service, mock_user_repo, sample_user):
        """Test authentication with inactive user."""
        sample_user.is_active = False
        mock_user_repo.get_by_email.return_value = sample_user
        
        result = await auth_service.authenticate_user("<EMAIL>", "password")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_locked(self, auth_service, mock_user_repo, sample_user):
        """Test authentication with locked user."""
        from datetime import datetime, timedelta
        sample_user.locked_until = datetime.utcnow() + timedelta(minutes=30)
        mock_user_repo.get_by_email.return_value = sample_user
        
        result = await auth_service.authenticate_user("<EMAIL>", "password")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_authenticate_user_wrong_password(self, auth_service, mock_user_repo, sample_user):
        """Test authentication with wrong password."""
        mock_user_repo.get_by_email.return_value = sample_user
        mock_user_repo.increment_failed_login = AsyncMock()
        
        with patch('app.services.auth_service.verify_password', return_value=False):
            result = await auth_service.authenticate_user("<EMAIL>", "wrong_password")
        
        assert result is None
        mock_user_repo.increment_failed_login.assert_called_once_with(sample_user.id)
    
    @pytest.mark.asyncio
    async def test_login_success(self, auth_service, sample_user):
        """Test successful login."""
        login_data = LoginRequest(email="<EMAIL>", password="password")
        
        with patch.object(auth_service, 'authenticate_user', return_value=sample_user), \
             patch('app.services.auth_service.create_access_token', return_value="access_token"), \
             patch('app.services.auth_service.create_refresh_token', return_value="refresh_token"):
            
            result = await auth_service.login(login_data)
        
        assert result.access_token == "access_token"
        assert result.refresh_token == "refresh_token"
        assert result.token_type == "bearer"
    
    @pytest.mark.asyncio
    async def test_login_failure(self, auth_service):
        """Test login failure."""
        login_data = LoginRequest(email="<EMAIL>", password="wrong_password")
        
        with patch.object(auth_service, 'authenticate_user', return_value=None):
            with pytest.raises(AuthenticationError, match="Invalid email or password"):
                await auth_service.login(login_data)
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, auth_service, mock_user_repo, sample_user):
        """Test successful token refresh."""
        mock_user_repo.get.return_value = sample_user
        
        with patch('app.services.auth_service.verify_token', return_value="1"), \
             patch('app.services.auth_service.create_access_token', return_value="new_access_token"), \
             patch('app.services.auth_service.create_refresh_token', return_value="new_refresh_token"):
            
            result = await auth_service.refresh_token("valid_refresh_token")
        
        assert result.access_token == "new_access_token"
        assert result.refresh_token == "new_refresh_token"
    
    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, auth_service):
        """Test token refresh with invalid token."""
        with patch('app.services.auth_service.verify_token', return_value=None):
            with pytest.raises(AuthenticationError, match="Invalid refresh token"):
                await auth_service.refresh_token("invalid_token")
    
    @pytest.mark.asyncio
    async def test_register_success(self, auth_service, mock_user_repo, mock_role_repo, mock_db):
        """Test successful user registration."""
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="New User",
            password="SecurePass123!",
            is_active=True
        )
        
        mock_user_repo.get_by_email.return_value = None
        mock_role_repo.get_default_role.return_value = MagicMock(id=1, name="viewer")
        
        with patch('app.services.auth_service.get_password_hash', return_value="hashed_password"):
            result = await auth_service.register(user_data)
        
        mock_db.add.assert_called_once()
        mock_db.flush.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_register_user_exists(self, auth_service, mock_user_repo, sample_user):
        """Test registration with existing user."""
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Test User",
            password="SecurePass123!"
        )
        
        mock_user_repo.get_by_email.return_value = sample_user
        
        with pytest.raises(ConflictError, match="User with this email already exists"):
            await auth_service.register(user_data)
    
    @pytest.mark.asyncio
    async def test_request_password_reset_success(self, auth_service, mock_user_repo, sample_user):
        """Test successful password reset request."""
        reset_data = PasswordResetRequest(email="<EMAIL>")
        mock_user_repo.get_by_email.return_value = sample_user
        
        with patch('app.services.auth_service.generate_password_reset_token', return_value="reset_token"):
            result = await auth_service.request_password_reset(reset_data)
        
        assert result == "reset_token"
    
    @pytest.mark.asyncio
    async def test_request_password_reset_user_not_found(self, auth_service, mock_user_repo):
        """Test password reset request for non-existent user."""
        reset_data = PasswordResetRequest(email="<EMAIL>")
        mock_user_repo.get_by_email.return_value = None
        
        with patch('app.services.auth_service.generate_password_reset_token', return_value="reset_token"):
            result = await auth_service.request_password_reset(reset_data)
        
        # Should still return a token to prevent email enumeration
        assert result == "reset_token"
    
    @pytest.mark.asyncio
    async def test_reset_password_success(self, auth_service, mock_user_repo, sample_user, mock_db):
        """Test successful password reset."""
        reset_data = PasswordResetConfirm(token="valid_token", new_password="NewPass123!")
        mock_user_repo.get_by_email.return_value = sample_user
        
        with patch('app.services.auth_service.verify_password_reset_token', return_value="<EMAIL>"), \
             patch('app.services.auth_service.get_password_hash', return_value="new_hashed_password"):
            
            result = await auth_service.reset_password(reset_data)
        
        assert result is True
        assert sample_user.hashed_password == "new_hashed_password"
        assert sample_user.failed_login_attempts == 0
        assert sample_user.locked_until is None
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_reset_password_invalid_token(self, auth_service):
        """Test password reset with invalid token."""
        reset_data = PasswordResetConfirm(token="invalid_token", new_password="NewPass123!")
        
        with patch('app.services.auth_service.verify_password_reset_token', return_value=None):
            with pytest.raises(AuthenticationError, match="Invalid or expired reset token"):
                await auth_service.reset_password(reset_data)
    
    @pytest.mark.asyncio
    async def test_change_password_success(self, auth_service, sample_user, mock_db):
        """Test successful password change."""
        change_data = ChangePasswordRequest(
            current_password="current_password",
            new_password="NewPass123!"
        )
        
        with patch('app.services.auth_service.verify_password', return_value=True), \
             patch('app.services.auth_service.get_password_hash', return_value="new_hashed_password"):
            
            result = await auth_service.change_password(sample_user, change_data)
        
        assert result is True
        assert sample_user.hashed_password == "new_hashed_password"
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_change_password_wrong_current(self, auth_service, sample_user):
        """Test password change with wrong current password."""
        change_data = ChangePasswordRequest(
            current_password="wrong_password",
            new_password="NewPass123!"
        )
        
        with patch('app.services.auth_service.verify_password', return_value=False):
            with pytest.raises(AuthenticationError, match="Current password is incorrect"):
                await auth_service.change_password(sample_user, change_data)
    
    @pytest.mark.asyncio
    async def test_logout_success(self, auth_service, sample_user):
        """Test successful logout."""
        result = await auth_service.logout(sample_user)
        assert result is True
