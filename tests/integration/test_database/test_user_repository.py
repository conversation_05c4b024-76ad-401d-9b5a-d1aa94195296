"""
Integration tests for UserRepository.
"""

import pytest
from datetime import datetime, timedelta

from app.models.user import User
from app.repositories.user_repository import UserRepository
from app.schemas.user import UserCreate, UserUpdate


@pytest.mark.integration
class TestUserRepositoryIntegration:
    """Integration tests for UserRepository with real database."""
    
    @pytest.mark.asyncio
    async def test_create_user(self, db_session):
        """Test creating a user in the database."""
        user_repo = UserRepository(db_session)
        
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Integration Test User",
            password="TestPass123!",
            is_active=True,
            is_verified=False
        )
        
        user = await user_repo.create(user_data)
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
        assert user.full_name == "Integration Test User"
        assert user.is_active is True
        assert user.is_verified is False
        assert user.created_at is not None
        assert user.updated_at is not None
    
    @pytest.mark.asyncio
    async def test_get_user_by_email(self, db_session):
        """Test getting user by email."""
        user_repo = UserRepository(db_session)
        
        # Create a user first
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Email Test User",
            password="TestPass123!",
            is_active=True
        )
        created_user = await user_repo.create(user_data)
        await db_session.commit()
        
        # Get user by email
        found_user = await user_repo.get_by_email("<EMAIL>")
        
        assert found_user is not None
        assert found_user.id == created_user.id
        assert found_user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_get_user_by_email_not_found(self, db_session):
        """Test getting non-existent user by email."""
        user_repo = UserRepository(db_session)
        
        found_user = await user_repo.get_by_email("<EMAIL>")
        
        assert found_user is None
    
    @pytest.mark.asyncio
    async def test_update_user(self, db_session):
        """Test updating a user."""
        user_repo = UserRepository(db_session)
        
        # Create a user first
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Update Test User",
            password="TestPass123!",
            is_active=True
        )
        user = await user_repo.create(user_data)
        await db_session.commit()
        
        # Update the user
        update_data = UserUpdate(
            full_name="Updated Test User",
            is_verified=True
        )
        updated_user = await user_repo.update(user, update_data)
        await db_session.commit()
        
        assert updated_user.full_name == "Updated Test User"
        assert updated_user.is_verified is True
        assert updated_user.email == "<EMAIL>"  # Unchanged
    
    @pytest.mark.asyncio
    async def test_delete_user(self, db_session):
        """Test deleting a user."""
        user_repo = UserRepository(db_session)
        
        # Create a user first
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Delete Test User",
            password="TestPass123!",
            is_active=True
        )
        user = await user_repo.create(user_data)
        await db_session.commit()
        user_id = user.id
        
        # Delete the user
        deleted_user = await user_repo.delete(user_id)
        await db_session.commit()
        
        assert deleted_user is not None
        assert deleted_user.id == user_id
        
        # Verify user is deleted
        found_user = await user_repo.get(user_id)
        assert found_user is None
    
    @pytest.mark.asyncio
    async def test_get_active_users(self, db_session):
        """Test getting active users."""
        user_repo = UserRepository(db_session)
        
        # Create active and inactive users
        active_user_data = UserCreate(
            email="<EMAIL>",
            full_name="Active User",
            password="TestPass123!",
            is_active=True
        )
        inactive_user_data = UserCreate(
            email="<EMAIL>",
            full_name="Inactive User",
            password="TestPass123!",
            is_active=False
        )
        
        await user_repo.create(active_user_data)
        await user_repo.create(inactive_user_data)
        await db_session.commit()
        
        # Get active users
        active_users = await user_repo.get_active_users()
        
        # Should only return active users
        active_emails = [user.email for user in active_users]
        assert "<EMAIL>" in active_emails
        assert "<EMAIL>" not in active_emails
    
    @pytest.mark.asyncio
    async def test_search_users(self, db_session):
        """Test searching users by email or name."""
        user_repo = UserRepository(db_session)
        
        # Create test users
        users_data = [
            UserCreate(
                email="<EMAIL>",
                full_name="John Doe",
                password="TestPass123!",
                is_active=True
            ),
            UserCreate(
                email="<EMAIL>",
                full_name="Jane Smith",
                password="TestPass123!",
                is_active=True
            ),
            UserCreate(
                email="<EMAIL>",
                full_name="Bob Johnson",
                password="TestPass123!",
                is_active=True
            )
        ]
        
        for user_data in users_data:
            await user_repo.create(user_data)
        await db_session.commit()
        
        # Search by name
        results = await user_repo.search_users("John")
        result_names = [user.full_name for user in results]
        assert "John Doe" in result_names
        assert "Bob Johnson" in result_names
        assert "Jane Smith" not in result_names
        
        # Search by email
        results = await user_repo.search_users("jane.smith")
        result_emails = [user.email for user in results]
        assert "<EMAIL>" in result_emails
        assert len(results) == 1
    
    @pytest.mark.asyncio
    async def test_update_last_login(self, db_session):
        """Test updating user's last login timestamp."""
        user_repo = UserRepository(db_session)
        
        # Create a user
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Login Test User",
            password="TestPass123!",
            is_active=True
        )
        user = await user_repo.create(user_data)
        await db_session.commit()
        
        # Update last login
        await user_repo.update_last_login(user.id)
        await db_session.commit()
        
        # Refresh user from database
        await db_session.refresh(user)
        
        assert user.last_login is not None
        assert user.failed_login_attempts == 0
        assert user.locked_until is None
    
    @pytest.mark.asyncio
    async def test_increment_failed_login(self, db_session):
        """Test incrementing failed login attempts."""
        user_repo = UserRepository(db_session)
        
        # Create a user
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Failed Login Test User",
            password="TestPass123!",
            is_active=True
        )
        user = await user_repo.create(user_data)
        await db_session.commit()
        
        # Increment failed login attempts multiple times
        for i in range(6):  # Should lock after 5 attempts
            await user_repo.increment_failed_login(user.id)
            await db_session.commit()
            await db_session.refresh(user)
            
            if i < 4:
                assert user.failed_login_attempts == i + 1
                assert user.locked_until is None
            else:
                # Should be locked after 5 failed attempts
                assert user.failed_login_attempts == 5
                assert user.locked_until is not None
    
    @pytest.mark.asyncio
    async def test_unlock_user(self, db_session):
        """Test unlocking a user account."""
        user_repo = UserRepository(db_session)
        
        # Create a user
        user_data = UserCreate(
            email="<EMAIL>",
            full_name="Unlock Test User",
            password="TestPass123!",
            is_active=True
        )
        user = await user_repo.create(user_data)
        
        # Simulate locked user
        user.failed_login_attempts = 5
        user.locked_until = datetime.utcnow() + timedelta(minutes=30)
        await db_session.commit()
        
        # Unlock user
        await user_repo.unlock_user(user.id)
        await db_session.commit()
        await db_session.refresh(user)
        
        assert user.failed_login_attempts == 0
        assert user.locked_until is None
    
    @pytest.mark.asyncio
    async def test_get_user_stats(self, db_session):
        """Test getting user statistics."""
        user_repo = UserRepository(db_session)
        
        # Create various types of users
        users_data = [
            UserCreate(
                email="<EMAIL>",
                full_name="Active User 1",
                password="TestPass123!",
                is_active=True,
                is_verified=True,
                is_superuser=False
            ),
            UserCreate(
                email="<EMAIL>",
                full_name="Active User 2",
                password="TestPass123!",
                is_active=True,
                is_verified=False,
                is_superuser=False
            ),
            UserCreate(
                email="<EMAIL>",
                full_name="Inactive User",
                password="TestPass123!",
                is_active=False,
                is_verified=False,
                is_superuser=False
            ),
            UserCreate(
                email="<EMAIL>",
                full_name="Super User",
                password="TestPass123!",
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
        ]
        
        for user_data in users_data:
            await user_repo.create(user_data)
        await db_session.commit()
        
        # Get statistics
        stats = await user_repo.get_user_stats()
        
        assert stats["total_users"] >= 4
        assert stats["active_users"] >= 3
        assert stats["verified_users"] >= 2
        assert stats["superusers"] >= 1
        assert stats["locked_users"] >= 0
        assert stats["recent_registrations"] >= 4
