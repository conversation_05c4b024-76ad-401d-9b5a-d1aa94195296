import json
import pika
import os

def handler(event, context):
    try:
        rabbitmq_url = os.environ.get('RABBITMQ_URL')
        connection = pika.BlockingConnection(pika.URLParameters(rabbitmq_url))
        channel = connection.channel()
        # Ensure queue declaration parameters match what's in the app (must be durable=True)
        channel.queue_declare(queue='events', durable=True)
        message = json.dumps(event)
        channel.basic_publish(exchange='', routing_key='events', body=message)
        connection.close()
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'message': 'Event processed and sent to RabbitMQ',
                'data': event
            })
        }
    except Exception as e:
        print(f"Error processing event: {str(e)}")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'error': str(e),
                'message': 'Failed to process event'
            })
        }
