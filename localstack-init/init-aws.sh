#!/bin/bash
echo "Configuring LocalStack..."

# Create S3 bucket
awslocal s3 mb s3://my-bucket

# Install dependencies for the Lambda function in a temp directory
mkdir -p /tmp/lambda_package
cp /etc/localstack/init/ready.d/lambda_function.py /tmp/lambda_package/lambda_function.py
pip install -r /etc/localstack/init/ready.d/lambda_requirements.txt -t /tmp/lambda_package

# Create zip file for Lambda
cd /tmp/lambda_package
zip -r /tmp/lambda.zip .

# Create Lambda function
awslocal lambda create-function \
  --function-name my-lambda \
  --runtime python3.8 \
  --handler lambda_function.handler \
  --memory-size 128 \
  --zip-file fileb:///tmp/lambda.zip \
  --role arn:aws:iam::000000000000:role/lambda-role \
  --environment "Variables={RABBITMQ_URL=amqp://cerebro_user:cerebro_password@rabbitmq:5672/cerebro}"

# Create IAM role
awslocal iam create-role \
  --role-name lambda-role \
  --assume-role-policy-document '{"Version":"2012-10-17","Statement":[{"Effect":"Allow","Principal":{"Service":"lambda.amazonaws.com"},"Action":"sts:AssumeRole"}]}'

# Create API Gateway
rest_api_id=$(awslocal apigateway create-rest-api --name 'My API' --query 'id' --output text)
parent_resource_id=$(awslocal apigateway get-resources --rest-api-id $rest_api_id --query 'items[?path==`/`].id' --output text)

# Create /backend resource
backend_resource_id=$(awslocal apigateway create-resource \
  --rest-api-id $rest_api_id \
  --parent-id $parent_resource_id \
  --path-part backend \
  --query 'id' --output text)

# Create a proxy resource under /backend
proxy_resource_id=$(awslocal apigateway create-resource \
  --rest-api-id $rest_api_id \
  --parent-id $backend_resource_id \
  --path-part "{proxy+}" \
  --query 'id' --output text)

awslocal apigateway put-method \
  --rest-api-id $rest_api_id \
  --resource-id $proxy_resource_id \
  --http-method ANY \
  --authorization-type "NONE" \
  --request-parameters "method.request.path.proxy=true"

awslocal apigateway put-integration \
  --rest-api-id $rest_api_id \
  --resource-id $proxy_resource_id \
  --http-method ANY \
  --type HTTP_PROXY \
  --integration-http-method ANY \
  --uri "http://app:8000/{proxy}" \
  --request-parameters "integration.request.path.proxy=method.request.path.proxy"

# Create /event resource
event_resource_id=$(awslocal apigateway create-resource \
  --rest-api-id $rest_api_id \
  --parent-id $parent_resource_id \
  --path-part event \
  --query 'id' --output text)

awslocal apigateway put-method \
  --rest-api-id $rest_api_id \
  --resource-id $event_resource_id \
  --http-method POST \
  --authorization-type "NONE"

awslocal apigateway put-integration \
  --rest-api-id $rest_api_id \
  --resource-id $event_resource_id \
  --http-method POST \
  --type AWS_PROXY \
  --integration-http-method POST \
  --uri "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:000000000000:function:my-lambda/invocations"

awslocal apigateway create-deployment \
  --rest-api-id $rest_api_id \
  --stage-name dev

# Enable CORS
awslocal apigateway enable-cors --rest-api-id $rest_api_id --resource-id $proxy_resource_id --allow-methods "GET,POST,PUT,DELETE,OPTIONS"
awslocal apigateway enable-cors --rest-api-id $rest_api_id --resource-id $event_resource_id --allow-methods "GET,POST,OPTIONS"

# Print API information
echo "API Gateway URL: http://localhost:4566/restapis/$rest_api_id/dev/_user_request_"
echo "Backend URL: http://localhost:4566/restapis/$rest_api_id/dev/_user_request_/backend/{path}"
echo "Event URL: http://localhost:4566/restapis/$rest_api_id/dev/_user_request_/event"

echo "LocalStack configured."
