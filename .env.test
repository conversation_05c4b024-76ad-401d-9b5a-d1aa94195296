# Test Environment Configuration
APP_NAME=Cerebro API Test
APP_VERSION=1.0.0
DEBUG=true
API_V1_STR=/api/v1
TESTING=true

# Security
SECRET_KEY=test-secret-key-not-for-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# Database - PostgreSQL (Test)
POSTGRES_SERVER=localhost
POSTGRES_USER=test_user
POSTGRES_PASSWORD=test_password
POSTGRES_DB=test_cerebro
POSTGRES_PORT=5432
DATABASE_URL=postgresql+asyncpg://test_user:test_password@localhost:5432/test_cerebro

# MongoDB (Test)
MONGODB_URL=**************************************************************
MONGODB_DB_NAME=test_cerebro

# Redis (Test)
REDIS_URL=redis://:test_password@localhost:6379/1
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=test_password
REDIS_DB=1

# RabbitMQ (Test)
RABBITMQ_URL=amqp://test_user:test_password@localhost:5672/test_cerebro
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=test_user
RABBITMQ_PASSWORD=test_password
RABBITMQ_VHOST=test_cerebro

# Inngest (Test)
INNGEST_URL=http://localhost:8288
INNGEST_EVENT_KEY=test
INNGEST_SIGNING_KEY=signkey-test-12345678901234567890123456789012

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=console

# Disable rate limiting in tests
RATE_LIMIT_ENABLED=false

# Test Superuser
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_PASSWORD=testpassword123
