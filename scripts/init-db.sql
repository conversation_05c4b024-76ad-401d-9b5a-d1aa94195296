-- PostgreSQL initialization script
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE cerebro'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'cerebro')\gexec

-- Create test database
SELECT 'CREATE DATABASE test_cerebro'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'test_cerebro')\gexec

-- Connect to cerebro database
\c cerebro;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Connect to test database
\c test_cerebro;

-- Create extensions for test database
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
