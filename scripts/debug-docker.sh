#!/bin/bash

# Debug script for Docker Compose issues

echo "🔍 Cerebro Docker Debug Script"
echo "================================"

# Function to check if Docker is running
check_docker() {
    echo "📋 Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    echo "✅ Docker is running"
}

# Function to check available ports
check_ports() {
    echo "📋 Checking port availability..."
    ports=(5432 6379 8000 15672 27017)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo "⚠️  Port $port is already in use"
            lsof -Pi :$port -sTCP:LISTEN
        else
            echo "✅ Port $port is available"
        fi
    done
}

# Function to clean up Docker resources
cleanup_docker() {
    echo "🧹 Cleaning up Docker resources..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    docker volume prune -f
    echo "✅ Cleanup completed"
}

# Function to check Docker Compose file syntax
check_compose_syntax() {
    echo "📋 Checking docker-compose.yml syntax..."
    if docker-compose config > /dev/null 2>&1; then
        echo "✅ docker-compose.yml syntax is valid"
    else
        echo "❌ docker-compose.yml has syntax errors:"
        docker-compose config
        exit 1
    fi
}

# Function to start services one by one
start_services_incrementally() {
    echo "🚀 Starting services incrementally..."
    
    echo "Starting PostgreSQL..."
    docker-compose up -d postgres
    sleep 10
    
    echo "Checking PostgreSQL logs..."
    docker-compose logs postgres
    
    if docker-compose ps postgres | grep -q "healthy\|Up"; then
        echo "✅ PostgreSQL started successfully"
    else
        echo "❌ PostgreSQL failed to start"
        docker-compose logs postgres
        return 1
    fi
    
    echo "Starting Redis..."
    docker-compose up -d redis
    sleep 5
    
    if docker-compose ps redis | grep -q "healthy\|Up"; then
        echo "✅ Redis started successfully"
    else
        echo "❌ Redis failed to start"
        docker-compose logs redis
        return 1
    fi
    
    echo "Starting RabbitMQ..."
    docker-compose up -d rabbitmq
    sleep 15
    
    if docker-compose ps rabbitmq | grep -q "healthy\|Up"; then
        echo "✅ RabbitMQ started successfully"
    else
        echo "❌ RabbitMQ failed to start"
        docker-compose logs rabbitmq
        return 1
    fi
    
    echo "All services started successfully!"
}

# Function to test database connections
test_connections() {
    echo "🔌 Testing database connections..."
    
    # Test PostgreSQL
    if docker-compose exec -T postgres pg_isready -U cerebro_user -d cerebro > /dev/null 2>&1; then
        echo "✅ PostgreSQL connection successful"
    else
        echo "❌ PostgreSQL connection failed"
    fi
    
    # Test Redis
    if docker-compose exec -T redis redis-cli -a cerebro_password ping > /dev/null 2>&1; then
        echo "✅ Redis connection successful"
    else
        echo "❌ Redis connection failed"
    fi
}

# Function to show service status
show_status() {
    echo "📊 Service Status:"
    docker-compose ps
    echo ""
    echo "📊 Container Health:"
    docker-compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
}

# Function to show logs
show_logs() {
    echo "📝 Recent logs:"
    docker-compose logs --tail=20
}

# Main menu
case "${1:-menu}" in
    "check")
        check_docker
        check_ports
        check_compose_syntax
        ;;
    "cleanup")
        cleanup_docker
        ;;
    "start")
        check_docker
        check_compose_syntax
        start_services_incrementally
        ;;
    "test")
        test_connections
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "dev")
        echo "🚀 Starting development environment..."
        docker-compose -f docker-compose.dev.yml up -d
        ;;
    "menu"|*)
        echo "Usage: $0 {check|cleanup|start|test|status|logs|dev}"
        echo ""
        echo "Commands:"
        echo "  check   - Check Docker status, ports, and compose file"
        echo "  cleanup - Clean up Docker resources"
        echo "  start   - Start services incrementally"
        echo "  test    - Test database connections"
        echo "  status  - Show service status"
        echo "  logs    - Show recent logs"
        echo "  dev     - Start minimal development environment"
        echo ""
        echo "Example: $0 check"
        ;;
esac
