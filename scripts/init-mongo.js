// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to cerebro database
db = db.getSiblingDB('cerebro');

// Create collections with validation
db.createCollection('events', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['event_type', 'timestamp', 'data'],
      properties: {
        event_type: {
          bsonType: 'string',
          description: 'Event type is required and must be a string'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Timestamp is required and must be a date'
        },
        data: {
          bsonType: 'object',
          description: 'Event data is required and must be an object'
        },
        user_id: {
          bsonType: 'int',
          description: 'User ID must be an integer'
        }
      }
    }
  }
});

db.createCollection('logs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['level', 'message', 'timestamp'],
      properties: {
        level: {
          bsonType: 'string',
          enum: ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
          description: 'Log level must be one of the enum values'
        },
        message: {
          bsonType: 'string',
          description: 'Message is required and must be a string'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Timestamp is required and must be a date'
        }
      }
    }
  }
});

// Create indexes
db.events.createIndex({ 'event_type': 1, 'timestamp': -1 });
db.events.createIndex({ 'user_id': 1, 'timestamp': -1 });
db.logs.createIndex({ 'level': 1, 'timestamp': -1 });
db.logs.createIndex({ 'timestamp': -1 });

// Create test database
db = db.getSiblingDB('test_cerebro');

// Create test collections
db.createCollection('test_events');
db.createCollection('test_logs');

print('MongoDB initialization completed successfully');
