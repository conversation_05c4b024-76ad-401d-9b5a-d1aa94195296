#!/usr/bin/env python3
"""
Script to test dependency compatibility before Docker build.
"""

import subprocess
import sys
import tempfile
import os


def test_dependencies():
    """Test if dependencies can be installed together."""
    print("Testing dependency compatibility...")
    
    # Create a temporary virtual environment
    with tempfile.TemporaryDirectory() as temp_dir:
        venv_path = os.path.join(temp_dir, "test_venv")
        
        # Create virtual environment
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        
        # Get pip path
        if os.name == 'nt':  # Windows
            pip_path = os.path.join(venv_path, "Scripts", "pip")
        else:  # Unix/Linux/macOS
            pip_path = os.path.join(venv_path, "bin", "pip")
        
        try:
            # Upgrade pip
            subprocess.run([pip_path, "install", "--upgrade", "pip"], check=True)
            
            # Test core dependencies first
            core_deps = [
                "fastapi>=0.115.0,<0.116.0",
                "uvicorn[standard]>=0.32.0,<0.33.0",
                "sqlalchemy[asyncio]>=2.0.30,<2.1.0",
                "asyncpg>=0.29.0,<0.30.0",
                "alembic>=1.14.0,<1.15.0",
                "pydantic>=2.10.0,<2.11.0",
                "pydantic-settings>=2.6.0,<2.7.0",
            ]
            
            print("Testing core dependencies...")
            subprocess.run([pip_path, "install"] + core_deps, check=True)
            
            # Test MongoDB dependencies
            mongo_deps = [
                "motor>=3.6.0,<3.7.0",
                "pymongo>=4.9.0,<4.10.0",
            ]
            
            print("Testing MongoDB dependencies...")
            subprocess.run([pip_path, "install"] + mongo_deps, check=True)
            
            # Test remaining dependencies
            other_deps = [
                "redis[hiredis]>=5.2.0,<5.3.0",
                "python-jose[cryptography]>=3.3.0,<3.4.0",
                "passlib[bcrypt]>=1.7.4,<1.8.0",
                "httpx>=0.28.0,<0.29.0",
                "structlog>=24.4.0,<24.5.0",
                "rich>=13.9.0,<13.10.0",
            ]
            
            print("Testing other dependencies...")
            subprocess.run([pip_path, "install"] + other_deps, check=True)
            
            print("✅ All dependencies are compatible!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Dependency conflict detected: {e}")
            return False


if __name__ == "__main__":
    success = test_dependencies()
    sys.exit(0 if success else 1)
