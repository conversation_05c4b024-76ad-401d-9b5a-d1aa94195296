#!/bin/bash

# Log file for URLs
URL_LOG_FILE="api_gateway_urls.txt"

# Function to check if a container is healthy
check_container_health() {
  local container_name=$1
  local max_attempts=$2
  local attempt=0
  
  echo "Waiting for $container_name to be healthy..."
  
  while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    
    # Check if container exists and is running
    if [ "$(docker ps -q -f name=$container_name)" ]; then
      # For containers with health checks
      health_status=$(docker inspect --format='{{.State.Health.Status}}' $container_name 2>/dev/null)
      
      if [ "$health_status" = "healthy" ]; then
        echo "$container_name is healthy!"
        return 0
      elif [ -z "$health_status" ]; then
        # For containers without health checks, just check if they're running
        status=$(docker inspect --format='{{.State.Status}}' $container_name)
        if [ "$status" = "running" ]; then
          echo "$container_name is running (no health check defined)"
          return 0
        fi
      fi
    fi
    
    echo "Waiting for $container_name to be healthy (attempt $attempt/$max_attempts)..."
    sleep 5
  done
  
  echo "ERROR: $container_name did not become healthy within the timeout period."
  return 1
}

# Start Docker Compose
echo "Starting Docker Compose stack..."
docker-compose down
docker-compose up -d

# Wait for essential services
check_container_health cerebro_localstack 60
check_container_health cerebro_app 30
check_container_health cerebro_rabbitmq 30

# Sleep a bit to ensure LocalStack initialization scripts have completed
echo "Waiting for LocalStack initialization to complete..."
sleep 10

# Update Lambda function to ensure correct queue declaration with durable=True
echo "Updating Lambda function with correct queue configuration..."
docker-compose exec -T localstack bash -c "cd /tmp && mkdir -p lambda_update && echo \"import json
import pika
import os

def handler(event, context):
    print(\\\"Event Lambda function invoked\\\")
    
    try:
        rabbitmq_url = os.environ.get('RABBITMQ_URL')
        print(f\\\"Connecting to RabbitMQ at {rabbitmq_url}\\\")
        
        connection = pika.BlockingConnection(pika.URLParameters(rabbitmq_url))
        channel = connection.channel()
        
        # Ensure queue declaration parameters match what's in the app (must be durable=True)
        channel.queue_declare(queue='events', durable=True)
        
        message = json.dumps(event)
        channel.basic_publish(exchange='', routing_key='events', body=message)
        
        connection.close()
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'message': 'Event processed and sent to RabbitMQ',
                'data': event
            })
        }
    except Exception as e:
        print(f\\\"Error processing event: {str(e)}\\\")
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json'
            },
            'body': json.dumps({
                'error': str(e),
                'message': 'Failed to process event'
            })
        }
\" > /tmp/updated_lambda.py && cp /tmp/updated_lambda.py /tmp/lambda_update/lambda_function.py && cd /tmp/lambda_update && pip install pika -t . && zip -r /tmp/lambda_update.zip . && awslocal lambda update-function-code --function-name my-lambda --zip-file fileb:///tmp/lambda_update.zip"

# Get the API Gateway REST API ID
echo "Extracting API Gateway information..."
REST_API_ID=$(docker exec cerebro_localstack awslocal apigateway get-rest-apis --query 'items[?name==`My API`].id' --output text)

if [ -z "$REST_API_ID" ]; then
  echo "ERROR: Could not retrieve REST API ID from LocalStack"
  exit 1
fi

# Create URLs
BASE_URL="http://localhost:4566/restapis/$REST_API_ID/dev/_user_request_"
BACKEND_URL="$BASE_URL/backend"
EVENT_URL="$BASE_URL/event"

# Log URLs to file
echo "Logging API Gateway URLs to $URL_LOG_FILE..."
cat > "$URL_LOG_FILE" << EOF
# API Gateway URLs (proxied through LocalStack)
API Gateway Base URL: $BASE_URL
Backend API URL: $BACKEND_URL
Event Endpoint URL: $EVENT_URL

# Direct URLs (bypass API Gateway)
FastAPI Direct URL: http://localhost:8008
API Documentation: http://localhost:8008/api/v1/docs
OpenAPI Schema: http://localhost:8008/api/v1/openapi.json

# IMPORTANT NOTES:
# 1. When accessing the API docs via API Gateway ($BACKEND_URL/api/v1/docs),
#    you may see "Fetch error NOT FOUND /api/v1/openapi.json".
# 2. This is because API Gateway doesn't correctly proxy the OpenAPI schema request.
# 3. For API documentation, always use the direct FastAPI URL: http://localhost:8008/api/v1/docs
EOF

# Display URLs in terminal
echo "===== API URLs ====="
cat "$URL_LOG_FILE"
echo "===================="

# Check if the OpenAPI docs are accessible via API Gateway
echo "Testing API endpoints..."

# Test the health endpoint via API Gateway
echo "1. Testing health endpoint via API Gateway..."
if curl -s "$BACKEND_URL/api/v1/health" | grep -q "healthy"; then
  echo "✅ Health endpoint is accessible via API Gateway"
else
  echo "❌ Health endpoint is NOT accessible via API Gateway"
fi

# Test direct access to health endpoint
echo "2. Testing health endpoint via direct FastAPI URL..."
if curl -s "http://localhost:8008/api/v1/health" | grep -q "healthy"; then
  echo "✅ Health endpoint is accessible via direct FastAPI URL"
else
  echo "❌ Health endpoint is NOT accessible via direct FastAPI URL"
fi

# Test OpenAPI schema via API Gateway
echo "3. Testing OpenAPI schema via API Gateway..."
if curl -s -I "$BACKEND_URL/api/v1/openapi.json" | grep -q "200 OK"; then
  echo "✅ OpenAPI schema is accessible via API Gateway"
else
  echo "❌ OpenAPI schema is NOT accessible via API Gateway"
  echo "   This is the root cause of the Swagger UI not working via API Gateway"
fi

# Test OpenAPI schema via direct URL
echo "4. Testing OpenAPI schema via direct FastAPI URL..."
if curl -s -I "http://localhost:8008/api/v1/openapi.json" | grep -q "200 OK"; then
  echo "✅ OpenAPI schema is accessible via direct FastAPI URL"
else
  echo "❌ OpenAPI schema is NOT accessible via direct FastAPI URL"
fi

# Test docs UI via API Gateway 
echo "5. Testing API docs UI via API Gateway..."
if curl -s -I "$BACKEND_URL/api/v1/docs" | grep -q "200 OK"; then
  echo "✅ API docs UI is accessible via API Gateway, but the Swagger UI might not work correctly"
  echo "   because it cannot fetch the OpenAPI schema from $BACKEND_URL/api/v1/openapi.json"
else
  echo "❌ API docs UI is NOT accessible via API Gateway"
fi

# Test docs UI via direct URL
echo "6. Testing API docs UI via direct FastAPI URL..."
if curl -s -I "http://localhost:8008/api/v1/docs" | grep -q "200 OK"; then
  echo "✅ API docs UI is accessible via direct FastAPI URL"
else
  echo "❌ API docs UI is NOT accessible via direct FastAPI URL"
fi

echo
echo "===================== SUMMARY ====================="
echo "✨ API Gateway proxying works for regular API endpoints"
echo "⚠️ API Gateway cannot properly proxy the OpenAPI schema request" 
echo "   which is required for the Swagger UI docs to function."
echo
echo "SOLUTION:"
echo "👉 For API documentation, always use the direct FastAPI URL:"
echo "   http://localhost:8008/api/v1/docs"
echo "===================================================="
echo

# Test Lambda function via event endpoint
echo "Testing Event endpoint with a sample event..."
curl -s -X POST "$EVENT_URL" \
  -H "Content-Type: application/json" \
  -d '{"event_type": "startup_test", "data": {"message": "Testing event flow"}}' | jq .

echo "Startup complete! All services are running and API Gateway is configured."
echo "Check Docker logs or the FastAPI logs to verify event processing:"
echo "  docker logs cerebro_app -f"
