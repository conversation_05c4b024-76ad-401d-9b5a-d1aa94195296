"""
Example usage of the Dynamic Schema Management System.

This script demonstrates how to use the dynamic schema management API
to create schemas, manage records, and perform various operations.
"""

import asyncio
import httpx
from typing import Dict, Any, List


class DynamicSchemaClient:
    """Client for interacting with the Dynamic Schema Management API."""
    
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {auth_token}"}
    
    async def create_schema(self, schema_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new schema."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/schemas",
                json=schema_data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def get_schemas(self, include_system: bool = True) -> Dict[str, Any]:
        """Get list of schemas."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/schemas",
                params={"include_system": include_system},
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def get_schema_by_name(self, schema_name: str) -> Dict[str, Any]:
        """Get schema by name."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/schemas/name/{schema_name}",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def create_record(self, schema_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new record."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/records/{schema_name}",
                json=data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def get_records(self, schema_name: str, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """Get records from a schema."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/records/{schema_name}",
                params={"page": page, "page_size": page_size},
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def query_records(self, schema_name: str, query: Dict[str, Any]) -> Dict[str, Any]:
        """Query records with filters."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/records/{schema_name}/query",
                json=query,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def update_record(self, schema_name: str, record_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a record."""
        async with httpx.AsyncClient() as client:
            response = await client.put(
                f"{self.base_url}/api/v1/records/{schema_name}/{record_id}",
                json=data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def bulk_create_records(self, schema_name: str, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create multiple records in bulk."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/records/{schema_name}/bulk",
                json=records,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()


async def main():
    """Main example function."""
    # Configuration
    BASE_URL = "http://localhost:8000"
    AUTH_TOKEN = "your-jwt-token-here"  # Replace with actual token
    
    client = DynamicSchemaClient(BASE_URL, AUTH_TOKEN)
    
    print("🚀 Dynamic Schema Management System Example")
    print("=" * 50)
    
    try:
        # 1. Check existing schemas
        print("\n1. Getting existing schemas...")
        schemas = await client.get_schemas()
        print(f"Found {schemas['total']} schemas:")
        for schema in schemas['schemas']:
            print(f"  - {schema['name']}: {schema['display_name']}")
        
        # 2. Create a product catalog schema
        print("\n2. Creating product catalog schema...")
        product_schema = {
            "name": "product_catalog",
            "display_name": "Product Catalog",
            "description": "Schema for managing product information",
            "fields": [
                {
                    "name": "name",
                    "display_name": "Product Name",
                    "description": "Name of the product",
                    "field_type": "string",
                    "validation_rules": {
                        "required": True,
                        "min_length": 1,
                        "max_length": 200
                    },
                    "order": 1
                },
                {
                    "name": "sku",
                    "display_name": "SKU",
                    "description": "Stock Keeping Unit",
                    "field_type": "string",
                    "validation_rules": {
                        "required": True,
                        "unique": True,
                        "min_length": 3,
                        "max_length": 50
                    },
                    "order": 2
                },
                {
                    "name": "price",
                    "display_name": "Price",
                    "description": "Product price in USD",
                    "field_type": "number",
                    "validation_rules": {
                        "required": True,
                        "min_value": 0
                    },
                    "order": 3
                },
                {
                    "name": "category",
                    "display_name": "Category",
                    "description": "Product category",
                    "field_type": "string",
                    "validation_rules": {
                        "required": True,
                        "enum_values": ["electronics", "clothing", "books", "home", "sports"]
                    },
                    "order": 4
                },
                {
                    "name": "in_stock",
                    "display_name": "In Stock",
                    "description": "Whether product is in stock",
                    "field_type": "boolean",
                    "validation_rules": {
                        "required": True
                    },
                    "default_value": True,
                    "order": 5
                },
                {
                    "name": "description",
                    "display_name": "Description",
                    "description": "Product description",
                    "field_type": "string",
                    "validation_rules": {
                        "required": False,
                        "max_length": 1000
                    },
                    "order": 6
                }
            ]
        }
        
        created_schema = await client.create_schema(product_schema)
        print(f"✅ Created schema: {created_schema['name']} (ID: {created_schema['id']})")
        
        # 3. Create some product records
        print("\n3. Creating product records...")
        products = [
            {
                "name": "iPhone 15 Pro",
                "sku": "IPHONE15PRO",
                "price": 999.99,
                "category": "electronics",
                "in_stock": True,
                "description": "Latest iPhone with advanced camera system"
            },
            {
                "name": "MacBook Air M2",
                "sku": "MACBOOKAIRM2",
                "price": 1199.99,
                "category": "electronics",
                "in_stock": True,
                "description": "Lightweight laptop with M2 chip"
            },
            {
                "name": "Running Shoes",
                "sku": "RUNSHOES001",
                "price": 129.99,
                "category": "sports",
                "in_stock": False,
                "description": "Comfortable running shoes for daily training"
            }
        ]
        
        # Create records individually
        created_records = []
        for product in products:
            record = await client.create_record("product_catalog", product)
            created_records.append(record)
            print(f"✅ Created product: {record['data']['name']} (ID: {record['id']})")
        
        # 4. Query records with filters
        print("\n4. Querying electronics products...")
        query = {
            "filters": [
                {
                    "field": "category",
                    "operator": "eq",
                    "value": "electronics"
                },
                {
                    "field": "in_stock",
                    "operator": "eq",
                    "value": True
                }
            ],
            "sort_by": "price",
            "sort_order": "desc",
            "page": 1,
            "page_size": 10
        }
        
        electronics = await client.query_records("product_catalog", query)
        print(f"Found {electronics['total']} electronics products in stock:")
        for record in electronics['records']:
            data = record['data']
            print(f"  - {data['name']}: ${data['price']} (SKU: {data['sku']})")
        
        # 5. Update a record
        print("\n5. Updating product price...")
        if created_records:
            first_record = created_records[0]
            updated_record = await client.update_record(
                "product_catalog",
                first_record['id'],
                {"price": 899.99}
            )
            print(f"✅ Updated {updated_record['data']['name']} price to ${updated_record['data']['price']}")
        
        # 6. Work with Contact schema
        print("\n6. Working with Contact schema...")
        contact_schema = await client.get_schema_by_name("contact")
        print(f"Contact schema has {len(contact_schema['fields'])} fields:")
        for field in contact_schema['fields']:
            required = "✓" if field['validation_rules']['required'] else "○"
            print(f"  {required} {field['display_name']} ({field['field_type']})")
        
        # Create a contact
        contact_data = {
            "first_name": "Alice",
            "last_name": "Johnson",
            "email": "<EMAIL>",
            "phone": "******-234-5678"
        }
        
        contact_record = await client.create_record("contact", contact_data)
        print(f"✅ Created contact: {contact_record['data']['first_name']} {contact_record['data']['last_name']}")
        
        # 7. Bulk create more contacts
        print("\n7. Bulk creating contacts...")
        bulk_contacts = [
            {
                "first_name": "Bob",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "phone": "******-345-6789"
            },
            {
                "first_name": "Carol",
                "last_name": "Davis",
                "email": "<EMAIL>",
                "phone": "******-456-7890"
            }
        ]
        
        bulk_result = await client.bulk_create_records("contact", bulk_contacts)
        print(f"✅ Bulk created {bulk_result['success_count']} contacts")
        if bulk_result['failure_count'] > 0:
            print(f"❌ Failed to create {bulk_result['failure_count']} contacts")
        
        print("\n🎉 Example completed successfully!")
        
    except httpx.HTTPStatusError as e:
        print(f"❌ HTTP Error: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("Note: Make sure to replace AUTH_TOKEN with a valid JWT token")
    print("You can get a token by logging in through the /api/v1/auth/login endpoint")
    print()
    
    # Uncomment the line below to run the example
    # asyncio.run(main())
