repos:
  # Pre-commit hooks for code quality
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-json
      - id: check-toml
      - id: debug-statements
      - id: detect-private-key

  # Python code formatting with Black
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3.11
        args: [--line-length=100]

  # Import sorting with isort
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=100]

  # Linting with flake8
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args: [--max-line-length=100, --extend-ignore=E203,W503]
        additional_dependencies:
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify

  # Type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.13.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-python-dateutil
          - types-redis
          - types-requests
        args: [--ignore-missing-imports]

  # Security linting with bandit
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: [-r, ., -f, json, -o, bandit-report.json]
        exclude: ^tests/

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        types: [yaml]
        exclude: ^(docker-compose.*\.yml|\.github/.*)$

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.10.0.1
    hooks:
      - id: shellcheck

  # Commit message linting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.29.1
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # Python dependency security check
  - repo: https://github.com/PyCQA/safety
    rev: 3.2.11
    hooks:
      - id: safety
        args: [--ignore=70612]  # Ignore specific vulnerabilities if needed

  # Documentation linting
  - repo: https://github.com/pycqa/doc8
    rev: v1.1.2
    hooks:
      - id: doc8
        args: [--max-line-length=100]

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
