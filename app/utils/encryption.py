import base64
import json
import os
import sys
import traceback
from typing import Any, Dict, Optional
from app.core.config import settings

from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

class EncryptionError(Exception):
    """Exception raised when encryption/decryption operations fail"""
    pass

class Encryption:
    def __init__(self, master_key: Optional[str] = None):
        """
        Initialize the encryptor with a master key
        
        Args:
            master_key: The master key for encryption. If not provided,
                        will look for CREDENTIAL_MASTER_KEY environment variable
        """
        self._master_key = master_key or settings.CREDENTIAL_MASTER_KEY
        if not self._master_key:
            raise EncryptionError("No encryption master key provided")
        
    def _derive_key(self, salt: bytes) -> bytes:
        """Derive an encryption key from the master key and salt"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return kdf.derive(self._master_key.encode())
    
    def encrypt(self, data: Dict[str, Any]) -> str:
        """
        Encrypt credential data
        
        Args:
            data: Dictionary of credential data to encrypt
            
        Returns:
            EncryptedData object with the encrypted content and metadata
        """
        try:
            # Generate a random salt for key derivation
            salt = os.urandom(16)
            
            # Derive the encryption key
            key = self._derive_key(salt)
            
            # Generate a random nonce for AES-GCM
            nonce = os.urandom(12)
            
            # Convert data to JSON string
            data_str = json.dumps(data)
            
            # Encrypt the data
            aesgcm = AESGCM(key)
            ciphertext = aesgcm.encrypt(nonce, data_str.encode(), None)
            
            # Combine salt, nonce, and ciphertext for storage
            result = base64.b64encode(
                salt + nonce + ciphertext
            ).decode('utf-8')
            
            return result
            
        except Exception as e:
            raise EncryptionError(f"Failed to encrypt credential data: {str(e)}")
        
    def decrypt(self, encrypted_data: str) -> Dict[str, Any]:
        """
        Decrypt encrypted credential data
        
        Args:
            encrypted_data: Either an EncryptedData object or an encrypted string
            
        Returns:
            Dictionary containing the decrypted credential data
        """
        try:
            # Get the encrypted content
            content = encrypted_data
            
            # Decode the base64 content
            decoded = base64.b64decode(content)
            
            # Extract salt, nonce, and ciphertext
            salt = decoded[:16]
            nonce = decoded[16:28]
            ciphertext = decoded[28:]
            
            # Derive the key
            key = self._derive_key(salt)
            
            # Decrypt the data
            aesgcm = AESGCM(key)
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)
            
            # Parse the JSON data
            return json.loads(plaintext.decode('utf-8'))
            
        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            traceback.print_exception(exc_type, exc_value, exc_tb)
            raise EncryptionError(f"Failed to decrypt credential data: {str(e)}")
