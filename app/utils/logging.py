"""
Logging configuration using structlog for structured logging.
"""

import logging
import sys
import time
from typing import Any, Dict, Optional

import structlog
from rich.console import Console
from rich.logging import <PERSON>Hand<PERSON>

from app.core.config import settings


def configure_logging() -> None:
    """Configure structured logging with rich output for development."""
    
    # Configure standard library logging
    if settings.DEBUG:
        logging.basicConfig(
            format="%(message)s",
            level=getattr(logging, settings.LOG_LEVEL.upper()),
            handlers=[
                RichHandler(
                    console=Console(stderr=True),
                    show_time=True,
                    show_path=True,
                    markup=True,
                    rich_tracebacks=True,
                )
            ],
        )
    else:
        logging.basicConfig(
            format="%(message)s",
            stream=sys.stdout,
            level=getattr(logging, settings.LOG_LEVEL.upper()),
        )
    
    # Configure structlog
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    
    if settings.LOG_FORMAT == "json":
        # Production JSON logging
        processors.extend([
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer()
        ])
    else:
        # Development console logging
        processors.extend([
            structlog.dev.ConsoleRenderer(colors=True)
        ])
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.LOG_LEVEL.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: Optional[str] = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        structlog.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


class LoggingMiddleware:
    """Middleware for request/response logging."""
    
    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware.logging")
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # Extract request information
        request_info = {
            "method": scope["method"],
            "path": scope["path"],
            "query_string": scope.get("query_string", b"").decode(),
            "client": scope.get("client"),
            "headers": dict(scope.get("headers", [])),
        }
        
        # Log request
        self.logger.info("Request started", **request_info)
        
        # Process request
        start_time = time.time()
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                # Log response
                duration = time.time() - start_time
                self.logger.info(
                    "Request completed",
                    status_code=message["status"],
                    duration_ms=round(duration * 1000, 2),
                    **request_info
                )
            await send(message)
        
        await self.app(scope, receive, send_wrapper)

def log_function_call(func_name: str, args: Optional[Dict[str, Any]] = None, result: Any = None):
    """
    Log function call with arguments and result.
    
    Args:
        func_name: Function name
        args: Function arguments
        result: Function result
    """
    logger = get_logger("function_call")
    logger.debug(
        "Function called",
        function=func_name,
        args=args or {},
        result_type=type(result).__name__ if result is not None else None
    )


def log_error(error: Exception, context: Optional[Dict[str, Any]] = None):
    """
    Log error with context information.
    
    Args:
        error: Exception instance
        context: Additional context information
    """
    logger = get_logger("error")
    logger.error(
        "Error occurred",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context or {},
        exc_info=True
    )
