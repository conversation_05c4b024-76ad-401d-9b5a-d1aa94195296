"""
Custom exception classes for the application.
"""

from typing import Any, Dict, Optional


class CerebroException(Exception):
    """Base exception class for Cerebro application."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(CerebroException):
    """Raised when validation fails."""
    
    def __init__(
        self,
        message: str = "Validation failed",
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message, "VALIDATION_ERROR", details)
        self.field = field


class NotFoundError(CerebroException):
    """Raised when a resource is not found."""
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        super().__init__(message, "NOT_FOUND", {
            "resource_type": resource_type,
            "resource_id": resource_id
        })


class AuthenticationError(CerebroException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, "AUTHENTICATION_ERROR")


class AuthorizationError(CerebroException):
    """Raised when authorization fails."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, "AUTHORIZATION_ERROR")


class ConflictError(CerebroException):
    """Raised when there's a conflict (e.g., duplicate resource)."""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        resource_type: Optional[str] = None
    ):
        super().__init__(message, "CONFLICT", {"resource_type": resource_type})


class RateLimitError(CerebroException):
    """Raised when rate limit is exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None
    ):
        super().__init__(message, "RATE_LIMIT_EXCEEDED", {"retry_after": retry_after})


class ExternalServiceError(CerebroException):
    """Raised when external service fails."""
    
    def __init__(
        self,
        message: str = "External service error",
        service_name: Optional[str] = None,
        status_code: Optional[int] = None
    ):
        super().__init__(message, "EXTERNAL_SERVICE_ERROR", {
            "service_name": service_name,
            "status_code": status_code
        })


class DatabaseError(CerebroException):
    """Raised when database operation fails."""
    
    def __init__(
        self,
        message: str = "Database error",
        operation: Optional[str] = None
    ):
        super().__init__(message, "DATABASE_ERROR", {"operation": operation})


class ConfigurationError(CerebroException):
    """Raised when configuration is invalid."""
    
    def __init__(
        self,
        message: str = "Configuration error",
        config_key: Optional[str] = None
    ):
        super().__init__(message, "CONFIGURATION_ERROR", {"config_key": config_key})
