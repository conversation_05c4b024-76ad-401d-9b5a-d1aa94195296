"""
User model with authentication and role-based access control.
"""

from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base
from app.models.role import user_role_association
if TYPE_CHECKING:
    from app.models.role import Role
    from app.models.workflow import WorkFlow, WorkFlowVersion
    from app.models.tag import Tag


class User(Base):
    """User model with authentication and RBAC support."""
    
    __tablename__ = "users"
    
    # Basic user information
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    full_name: Mapped[str] = mapped_column(String(255), nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # User status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Authentication tracking
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    failed_login_attempts: Mapped[int] = mapped_column(default=0)
    locked_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Profile information
    avatar_url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    bio: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)
    timezone: Mapped[str] = mapped_column(String(50), default="UTC")
    language: Mapped[str] = mapped_column(String(10), default="en")
    
    # Relationships
    roles: Mapped[List["Role"]] = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users"
    )

    # Audit trail back-references
    created_workflows: Mapped[List["WorkFlow"]] = relationship(
        "WorkFlow",
        foreign_keys="WorkFlow.created_by",
        back_populates="creator"
    )

    edited_workflows: Mapped[List["WorkFlow"]] = relationship(
        "WorkFlow",
        foreign_keys="WorkFlow.edited_by",
        back_populates="editor"
    )

    created_workflow_versions: Mapped[List["WorkFlowVersion"]] = relationship(
        "WorkFlowVersion",
        foreign_keys="WorkFlowVersion.created_by",
        back_populates="creator"
    )

    edited_workflow_versions: Mapped[List["WorkFlowVersion"]] = relationship(
        "WorkFlowVersion",
        foreign_keys="WorkFlowVersion.edited_by",
        back_populates="editor"
    )

    created_tags: Mapped[List["Tag"]] = relationship(
        "Tag",
        foreign_keys="Tag.created_by",
        back_populates="creator"
    )

    edited_tags: Mapped[List["Tag"]] = relationship(
        "Tag",
        foreign_keys="Tag.edited_by",
        back_populates="editor"
    )
    
    def __repr__(self) -> str:
        return f"<User(email={self.email}, full_name={self.full_name})>"
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def has_role(self, role_name: str) -> bool:
        """Check if user has specific role."""
        return any(role.name == role_name for role in self.roles)
    
    def has_permission(self, resource: str, action: str) -> bool:
        """Check if user has specific permission through their roles."""
        if self.is_superuser:
            return True
            
        return any(
            role.has_permission(resource, action)
            for role in self.roles
        )
    
    def get_roles_list(self) -> List[str]:
        """Get list of role names for this user."""
        return [role.name for role in self.roles]
    
    def get_permissions_list(self) -> List[str]:
        """Get list of all permissions for this user."""
        permissions = set()
        for role in self.roles:
            permissions.update(role.get_permissions_list())
        return list(permissions)
    
    def add_role(self, role: "Role") -> None:
        """Add role to user."""
        if role not in self.roles:
            self.roles.append(role)
    
    def remove_role(self, role: "Role") -> None:
        """Remove role from user."""
        if role in self.roles:
            self.roles.remove(role)
    
    def reset_failed_login_attempts(self) -> None:
        """Reset failed login attempts counter."""
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def increment_failed_login_attempts(self, max_attempts: int = 5, lockout_minutes: int = 30) -> None:
        """Increment failed login attempts and lock account if necessary."""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= max_attempts:
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_minutes)
    
    def update_last_login(self) -> None:
        """Update last login timestamp."""
        self.last_login = datetime.utcnow()
