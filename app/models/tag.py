"""
Tag model for categorizing workflows and workflow versions.
"""

from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base

if TYPE_CHECKING:
    from app.models.workflow import WorkFlow
    from app.models.user import User


class Tag(Base):
    """
    Tag model for categorizing workflows and workflow versions.
    
    Tags can be used to organize workflows by categories, environments,
    or any other classification system. The usage_count field tracks
    how many times this tag is used across workflows and versions.
    """
    
    __tablename__ = "tag"
    
    # Tag information
    name: Mapped[str] = mapped_column(
        String(100), 
        unique=True, 
        nullable=False, 
        index=True
    )
    
    usage_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        index=True
    )

    # Audit trail fields
    created_by: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )

    edited_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        Foreign<PERSON>ey("users.id"),
        nullable=True,
        index=True
    )
    
    # Relationships
    workflows: Mapped[List["WorkFlow"]] = relationship(
        "WorkFlow",
        secondary="workflow_tags",
        back_populates="tags"
    )

    # Audit trail relationships
    creator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[created_by]
    )

    editor: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[edited_by]
    )
    
    def __repr__(self) -> str:
        return f"<Tag(id={self.id}, name={self.name}, usage_count={self.usage_count})>"
    
    def increment_usage(self) -> None:
        """Increment the usage count for this tag."""
        self.usage_count += 1
    
    def decrement_usage(self) -> None:
        """Decrement the usage count for this tag."""
        if self.usage_count > 0:
            self.usage_count -= 1
