"""
Credential model for storing authentication details for external services.
"""

from typing import List
import uuid

from sqlalchemy import Boolean, String, Text, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column

from app.core.security import decrypt_credential_data
from app.models.base import Base

class Credential(Base):
    """Credential model for storing encrypted authentication details for external services."""
    
    __tablename__ = "credentials"
    
    # Override the id field from Base to use UUID instead of int
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Basic credential information
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(100), nullable=False)  # renamed from provider
    auth_method: Mapped[str] = mapped_column(String(50), nullable=False)  # e.g., 'bearer', 'jwt', 'oauth2'
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="active")  # e.g., 'active', 'failed', 'expired'
    
    # Encrypted credential data
    data: Mapped[str] = mapped_column(Text, nullable=False)  # Encrypted data (tokens, keys, etc.)
    
    # Access control
    allowed_nodes: Mapped[List[str]] = mapped_column(ARRAY(String(100)), nullable=False, default=[])
    
    # Audit fields
    created_by: Mapped[str] = mapped_column(String(255), nullable=False)
    
    # Soft deletion support
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False)
    
    def __repr__(self) -> str:
        return f"<Credential(id={self.id}, name={self.name}, type={self.type})>"
    
    def to_dict(self, sanitize_sensitive=True) -> dict:
        """
        Convert model to dictionary with secure sanitization for sensitive data.
        
        Args:
            sanitize_sensitive: If True, sanitize sensitive fields in credential data
                                If False, include raw credential data (USE WITH CAUTION)
        
        Returns:
            Dictionary representation of the model with sanitized credential data
        """
        data_dict = super().to_dict()
        
        # If not sanitizing or no data, return as is
        if not sanitize_sensitive or "data" not in data_dict or not data_dict["data"]:
            return data_dict
            
        try:
            credential_data = decrypt_credential_data(data_dict["data"])

            # Apply sanitization to the credential data
            sanitized_data = self._sanitize_credential_data(credential_data)
            
            # Replace the raw data with sanitized version
            data_dict["data"] = sanitized_data
            return data_dict

        except (TypeError, AttributeError) as e:
            # If data is not valid JSON or doesn't have the expected structure,
            # replace with a generic placeholder to avoid security risks
            data_dict["data"] = "***INVALID_CREDENTIAL_FORMAT***"
            return data_dict
            
    def _sanitize_credential_data(self, credential_data: dict) -> dict:
        """
        Sanitize sensitive values in credential data while preserving structure.
        
        Args:
            credential_data: Dictionary containing credential data with structure and sensitivity flags
        
        Returns:
            Dictionary with same structure but with sensitive values redacted
        """
        sanitized = {}
        
        # Process each field in the credential data
        for key, field_data in credential_data.items():
            # Create a copy to avoid modifying the original
            if isinstance(field_data, dict):
                field_copy = field_data.copy()
                
                # Check if this is a field with sensitivity flag
                if "value" in field_copy and "is_sensitive" in field_copy:
                    # Redact sensitive values
                    if field_copy.get("is_sensitive") is True:
                        field_copy["value"] = "***SENSITIVE_VALUE_HIDDEN***"
                    
                # If it's a nested dictionary without sensitivity flags, recursively process
                else:
                    field_copy = self._sanitize_credential_data(field_copy)
                    
                sanitized[key] = field_copy
            else:
                # For non-dict values, preserve as is
                sanitized[key] = field_data
                
        return sanitized
    
    def is_accessible_by_node(self, node_id: str) -> bool:
        """Check if credential can be accessed by a specific node."""
        if not self.allowed_nodes:  # Empty list means all nodes can access
            return True
        return node_id in self.allowed_nodes
