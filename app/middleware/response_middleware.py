"""
Comprehensive API response middleware for standardizing all FastAPI responses.

This middleware automatically wraps all API responses in a consistent JSON structure
and sets appropriate HTTP status codes based on the response type.
"""

import json
import traceback
from typing import Any, Dict, Optional, Union

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.config import settings
from app.schemas.common import BaseResponse
from app.utils.logging import get_logger
from starlette.responses import Response as StarletteResponse

logger = get_logger("middleware.response")


class APIResponseMiddleware(BaseHTTPMiddleware):
    """
    Middleware that standardizes all API responses across the entire FastAPI application.
    
    Features:
    - Automatic response wrapping in BaseResponse format
    - HTTP status code mapping based on response type
    - Error code standardization
    - Exception handling and formatting
    - Content-type filtering (only applies to JSON API responses)
    - Performance optimization
    - Backward compatibility with existing BaseResponse endpoints
    """
    
    def __init__(self, app: ASGIApp, api_prefix: str = "/api/v1"):
        """
        Initialize the response middleware.
        
        Args:
            app: FastAPI application instance
            api_prefix: API prefix to apply middleware to (default: "/api/v1")
        """
        super().__init__(app)
        self.api_prefix = api_prefix

        
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and standardize the response.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware/endpoint in the chain
            
        Returns:
            Response: Standardized JSON response
        """
        
        
        # Check if this is an API request that should be processed
        if not self._should_process_request(request):
            return await call_next(request)
        
        try:
            # Process the request
            original_response = await call_next(request)

            # Check if response should be processed before consuming body
            if not self._should_process_response(request):
                return await call_next(request)
            # Consume the body from the streaming response
            body = b""
            async for chunk in original_response.body_iterator:
                body += chunk

            # Reconstruct the Response with the consumed body so that `.body` is accessible
            new_response = Response(
                content=body,
                status_code=original_response.status_code,
                headers=dict(original_response.headers),
                media_type=original_response.media_type
            )

            # Extract response data
            response_data = await self._extract_response_data(new_response)
            # Check if response is already in BaseResponse format
            if self._is_base_response_format(response_data):
                # Already standardized, just ensure proper status code
                status_code = self._determine_status_code_from_base_response(response_data)
                return JSONResponse(
                    content=response_data,
                    status_code=status_code
                )
            
            # Wrap response in standardized format
            standardized_response = self._standardize_response(
                response_data, 
                original_response.status_code
            )
            
            # Determine final status code
            final_status_code = self._determine_final_status_code(
                original_response.status_code,
                standardized_response
            )
            
            logger.debug(
                "Response standardized",
                path=request.url.path,
                method=request.method,
                original_status=original_response.status_code,
                final_status=final_status_code,
                response_type="success" if standardized_response.get("status") else "error"
            )
            
            return JSONResponse(
                content=standardized_response,
                status_code=final_status_code
            )
            
        except Exception as exc:
            # Handle unhandled exceptions
            logger.error(
                "Unhandled exception in response middleware",
                path=request.url.path,
                method=request.method,
                error=str(exc),
                traceback=traceback.format_exc() if settings.DEBUG else None
            )
            
            # Create standardized error response
            error_response = BaseResponse.error(
                message="Internal server error",
                error_code="HTTP_ERROR"
            ).model_dump()
            
            return JSONResponse(
                content=error_response,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _should_process_request(self, request: Request) -> bool:
        """
        Determine if the request should be processed by this middleware.
        
        Args:
            request: Incoming HTTP request
            
        Returns:
            bool: True if request should be processed
        """
        path = request.url.path
        
        # Only process API requests
        if not path.startswith(self.api_prefix):
            return False
        
        # Skip static files, docs, and other non-API endpoints
        skip_paths = [
            f"{self.api_prefix}/docs",
            f"{self.api_prefix}/redoc",
            f"{self.api_prefix}/openapi.json",
            "/static/",
            "/favicon.ico"
        ]
        
        return not any(path.startswith(skip_path) for skip_path in skip_paths)
    
    def _should_process_response(self, response: Response) -> bool:
        """
        Determine if the response should be processed.
        
        Args:
            response: HTTP response
            
        Returns:
            bool: True if response should be processed
        """
        # Only process JSON responses
        content_type = response.headers.get("content-type", "")
        return "application/json" in content_type or not content_type
    
    async def _extract_response_data(self, response: Response) -> Any:
        """
        Extract data from the response body.

        Args:
            response: HTTP response

        Returns:
            Any: Parsed response data
        """
        try:
            # Handle JSONResponse
            if isinstance(response, JSONResponse):
                return response.content

            # Handle regular Response with body
            if hasattr(response, 'body'):
                body = response.body
                if isinstance(body, bytes):
                    body = body.decode('utf-8')
                if body:
                    return json.loads(body)
                return None

            # Handle streaming responses or other types
            return None

        except (json.JSONDecodeError, UnicodeDecodeError, AttributeError):
            return None
    
    def _is_base_response_format(self, data: Any) -> bool:
        """
        Check if response data is already in BaseResponse format.
        
        Args:
            data: Response data
            
        Returns:
            bool: True if already in BaseResponse format
        """
        if not isinstance(data, dict):
            return False
        
        # Check for BaseResponse structure
        has_status = "status" in data and isinstance(data["status"], bool)
        
        if data.get("status") is True:
            # Success response should have message and optionally data
            return has_status and "message" in data
        else:
            # Error response should have error and error_code
            return has_status and "error" in data and "error_code" in data
    
    def _determine_status_code_from_base_response(self, data: Dict[str, Any]) -> int:
        """
        Determine appropriate HTTP status code from BaseResponse data.
        
        Args:
            data: BaseResponse formatted data
            
        Returns:
            int: HTTP status code
        """
        if data.get("status") is True:
            return status.HTTP_200_OK
        
        # Map error codes to HTTP status codes
        error_code = data.get("error_code", "HTTP_ERROR")
        error_code_mapping = {
            "VALIDATION_ERROR": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "NOT_FOUND": status.HTTP_404_NOT_FOUND,
            "AUTHENTICATION_ERROR": status.HTTP_401_UNAUTHORIZED,
            "AUTHORIZATION_ERROR": status.HTTP_403_FORBIDDEN,
            "CONFLICT_ERROR": status.HTTP_409_CONFLICT,
            "HTTP_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        
        return error_code_mapping.get(error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _standardize_response(self, data: Any, original_status_code: int) -> Dict[str, Any]:
        """
        Convert response data to standardized BaseResponse format.
        
        Args:
            data: Original response data
            original_status_code: Original HTTP status code
            
        Returns:
            Dict[str, Any]: Standardized response data
        """
        # Determine if this should be a success or error response
        is_success = 200 <= original_status_code < 400
        
        if is_success:
            # Create success response
            message = self._extract_success_message(data, original_status_code)
            return BaseResponse.success(message=message, data=data).model_dump()
        else:
            # Create error response
            error_message, error_code = self._extract_error_info(data, original_status_code)
            return BaseResponse.error(
                message=error_message,
                error_code=error_code
            ).model_dump()
    
    def _extract_success_message(self, data: Any, status_code: int) -> str:
        """
        Extract or generate success message from response data.
        
        Args:
            data: Response data
            status_code: HTTP status code
            
        Returns:
            str: Success message
        """
        # Try to extract message from data
        if isinstance(data, dict):
            if "message" in data:
                return str(data["message"])
            if "detail" in data:
                return str(data["detail"])
        
        # Generate default message based on status code
        status_messages = {
            200: "Operation successful",
            201: "Resource created successfully",
            202: "Request accepted",
            204: "Operation completed successfully",
        }
        
        return status_messages.get(status_code, "Operation successful")
    
    def _extract_error_info(self, data: Any, status_code: int) -> tuple[str, str]:
        """
        Extract error message and code from response data.

        Args:
            data: Response data
            status_code: HTTP status code

        Returns:
            tuple[str, str]: Error message and error code
        """
        error_message = "An error occurred"
        error_code = self._map_status_code_to_error_code(status_code)

        if isinstance(data, dict):
            # Extract error message with priority order
            if "detail" in data:
                detail = data["detail"]
                if isinstance(detail, str):
                    error_message = detail
                elif isinstance(detail, dict) and "msg" in detail:
                    error_message = detail["msg"]
                else:
                    error_message = str(detail)
            elif "error" in data:
                error_message = str(data["error"])
            elif "message" in data:
                error_message = str(data["message"])
            elif "msg" in data:
                error_message = str(data["msg"])

            # Extract error code if available
            if "error_code" in data:
                error_code = str(data["error_code"])

        elif isinstance(data, str):
            error_message = data
        elif isinstance(data, list) and len(data) > 0:
            # Handle validation error arrays
            first_error = data[0]
            if isinstance(first_error, dict):
                if "msg" in first_error:
                    error_message = str(first_error["msg"])
                elif "message" in first_error:
                    error_message = str(first_error["message"])

        # Provide better default messages based on status code
        if error_message == "An error occurred":
            status_messages = {
                400: "Bad request",
                401: "Authentication required",
                403: "Access forbidden",
                404: "Resource not found",
                409: "Resource conflict",
                422: "Validation failed",
                429: "Too many requests",
                500: "Internal server error",
                502: "Bad gateway",
                503: "Service unavailable",
            }
            error_message = status_messages.get(status_code, "An error occurred")

        return error_message, error_code
    
    def _map_status_code_to_error_code(self, status_code: int) -> str:
        """
        Map HTTP status code to standardized error code.
        
        Args:
            status_code: HTTP status code
            
        Returns:
            str: Standardized error code
        """
        status_code_mapping = {
            400: "VALIDATION_ERROR",
            401: "AUTHENTICATION_ERROR",
            403: "AUTHORIZATION_ERROR",
            404: "NOT_FOUND",
            409: "CONFLICT_ERROR",
            422: "VALIDATION_ERROR",
            429: "HTTP_ERROR",  # Rate limit
            500: "HTTP_ERROR",
            502: "HTTP_ERROR",
            503: "HTTP_ERROR",
        }
        
        return status_code_mapping.get(status_code, "HTTP_ERROR")
    
    def _determine_final_status_code(
        self, 
        original_status_code: int, 
        standardized_response: Dict[str, Any]
    ) -> int:
        """
        Determine the final HTTP status code for the response.
        
        Args:
            original_status_code: Original HTTP status code
            standardized_response: Standardized response data
            
        Returns:
            int: Final HTTP status code
        """
        # If response is already standardized with proper status codes, use original
        if self._is_base_response_format(standardized_response):
            return self._determine_status_code_from_base_response(standardized_response)
        
        # For success responses, preserve original status code if it's a success code
        if standardized_response.get("status") is True:
            return original_status_code if 200 <= original_status_code < 400 else status.HTTP_200_OK
        
        # For error responses, use original status code if it's an error code
        return original_status_code if original_status_code >= 400 else status.HTTP_500_INTERNAL_SERVER_ERROR
