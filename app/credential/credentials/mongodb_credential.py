"""
MongoDB Credential Model

This module defines the MongoDB credential model for database connections,
supporting both connection string and individual parameter configurations.
"""

from app.credential.base.credential_model import AuthMethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes, DisplayOptions


@credential_provider(name="mongodb")
class MongoDBCredential(CredentialModel):
    """
    MongoDB credential model supporting both connection string and individual parameters.
    
    Provides flexible authentication options for MongoDB connections including:
    - Direct connection string configuration
    - Individual parameter configuration (host, port, database, username, password)
    - SSL/TLS connection options
    - Authentication database specification
    """
    
    name: str = "mongodb"
    display_name: str = "MongoDB"
    description: str = "MongoDB database credentials for connecting to MongoDB instances"
    icon: str = "mongodb"
    icon_color: str = "#47A248"
    icon_url: str = "https://example.com/icons/mongodb.png"
    documentation_url: str = "https://docs.mongodb.com/manual/reference/connection-string/"
    subtitle: str = "MongoDB database credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["mongodb"]
    base_url: str = ""
    
    parameters: list[NodeParameter] = [
        # Configuration type selection
        NodeParameter(
            name="configuration_type",
            display_name="Configuration Type",
            description="Choose how to configure the MongoDB connection",
            type=PropertyTypes.OPTIONS,
            required=True,
            default="connection_string",
            options=[
                {"name": "Connection String", "value": "connection_string", "description": "Use a MongoDB connection string"},
                {"name": "Individual Parameters", "value": "parameters", "description": "Configure connection using individual parameters"}
            ]
        ),
        
        # Connection string configuration
        NodeParameter(
            name="connection_string",
            display_name="Connection String",
            description="MongoDB connection string (e.g., ********************************:port/database)",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="****************************************************",
            display_options=DisplayOptions(
                show={"configuration_type": ["connection_string"]}
            )
        ),
        
        # Individual parameter configuration
        NodeParameter(
            name="host",
            display_name="Host",
            description="MongoDB server hostname or IP address",
            type=PropertyTypes.STRING,
            required=True,
            default="localhost",
            placeholder="localhost",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        NodeParameter(
            name="port",
            display_name="Port",
            description="MongoDB server port number",
            type=PropertyTypes.NUMBER,
            required=True,
            default=27017,
            placeholder="27017",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        NodeParameter(
            name="database",
            display_name="Database",
            description="MongoDB database name",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="mydatabase",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        NodeParameter(
            name="username",
            display_name="Username",
            description="MongoDB username for authentication",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="username",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        NodeParameter(
            name="password",
            display_name="Password",
            description="MongoDB password for authentication",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="password",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        NodeParameter(
            name="auth_database",
            display_name="Authentication Database",
            description="Database to authenticate against (defaults to admin)",
            type=PropertyTypes.STRING,
            required=True,
            default="admin",
            placeholder="admin",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        # SSL/TLS options
        NodeParameter(
            name="use_ssl",
            display_name="Use SSL/TLS",
            description="Enable SSL/TLS connection encryption",
            type=PropertyTypes.BOOLEAN,
            required=False,
            default=False,
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        ),
        
        # Connection options
        NodeParameter(
            name="connection_timeout",
            display_name="Connection Timeout (ms)",
            description="Connection timeout in milliseconds",
            type=PropertyTypes.NUMBER,
            required=False,
            default=30000,
            placeholder="30000",
            display_options=DisplayOptions(
                show={"configuration_type": ["parameters"]}
            )
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        """
        MongoDB doesn't use HTTP authentication, so this returns a basic model.
        Actual authentication is handled in the connection string or parameters.
        """
        return CredentialAuthModel(
            method=AuthMethod.CUSTOM,
            headers={},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        """
        Define the credential test configuration for MongoDB.
        This tests the connection without relying on HTTP endpoints.
        """
        return CredentialTestModel(
            # Use an empty string to indicate non-HTTP testing
            endpoint="",
            # Use GET as a placeholder - actual connection test will be handled by custom logic
            method="GET",
            # Simple validation that expects the connection test to return a success flag
            validation=lambda response: response.get('connected', False) is True,
            # Add custom testing info to indicate this requires special handling
            custom_test=True,
            test_type="mongodb_connection"
        )
