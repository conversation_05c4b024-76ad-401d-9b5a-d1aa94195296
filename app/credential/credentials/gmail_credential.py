"""
Gmail Credential Model

This module defines the Gmail credential model for OAuth2 authentication
with the Gmail API, following the project's established credential patterns.
"""

from app.credential.base.credential_model import (
    AuthMethod, 
    CredentialAuthModel, 
    CredentialModel, 
    CredentialTestModel
)
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes


@credential_provider(name="gmail_api")
class GmailCredential(CredentialModel):
    """
    Gmail API credential model for OAuth2 authentication.
    
    Supports both OAuth2 flow and service account authentication
    for accessing Gmail API endpoints.
    """
    
    name: str = "gmail_api"
    display_name: str = "Gmail API"
    description: str = "Gmail API credentials for email operations"
    icon: str = "gmail"
    icon_color: str = "#EA4335"
    icon_url: str = "https://developers.google.com/gmail/images/gmail-api-logo.png"
    documentation_url: str = "https://developers.google.com/gmail/api"
    subtitle: str = "Gmail API OAuth2 credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["gmail"]
    base_url: str = "https://gmail.googleapis.com/gmail/v1"
    
    parameters: list[NodeParameter] = [
        NodeParameter(
            name="client_id",
            display_name="Client ID",
            description="OAuth2 client ID from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="*********-abcdefghijklmnop.apps.googleusercontent.com"
        ),
        NodeParameter(
            name="client_secret",
            display_name="Client Secret",
            description="OAuth2 client secret from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="GOCSPX-abcdefghijklmnopqrstuvwxyz"
        ),
        NodeParameter(
            name="refresh_token",
            display_name="Refresh Token",
            description="OAuth2 refresh token for accessing Gmail API",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="1//0abcdefghijklmnopqrstuvwxyz"
        ),
        NodeParameter(
            name="access_token",
            display_name="Access Token",
            description="Current OAuth2 access token (auto-refreshed)",
            type=PropertyTypes.STRING,
            required=False,
            sensitive=True,
            placeholder="ya29.abcdefghijklmnopqrstuvwxyz"
        ),
        NodeParameter(
            name="user_email",
            display_name="User Email",
            description="Email address of the Gmail account to access",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="<EMAIL>"
        ),
        NodeParameter(
            name="scopes",
            display_name="OAuth2 Scopes",
            description="Comma-separated list of Gmail API scopes",
            type=PropertyTypes.STRING,
            required=False,
            default="https://www.googleapis.com/auth/gmail.modify",
            placeholder="https://www.googleapis.com/auth/gmail.modify,https://www.googleapis.com/auth/gmail.send"
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        """
        Configure OAuth2 Bearer token authentication for Gmail API.
        
        Returns:
            CredentialAuthModel: Authentication configuration
        """
        return CredentialAuthModel(
            method=AuthMethod.BEARER,
            headers={"Authorization": "Bearer {access_token}"},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        """
        Configure credential test endpoint for Gmail API.
        
        Returns:
            CredentialTestModel: Test configuration
        """
        return CredentialTestModel(
            endpoint="/users/me/profile",
            method="GET",
            validation=lambda response: (
                'emailAddress' in response and 
                response['emailAddress'] is not None and
                '@' in response['emailAddress']
            )
        )
