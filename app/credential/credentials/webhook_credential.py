"""
Webhook Credential Implementation

This module defines the webhook credential model for webhook authentication
and configuration following the project's established patterns.
"""

from app.credential.base.credential_model import AuthMethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes


@credential_provider(name="webhook_auth")
class WebhookCredential(CredentialModel):
    """
    Webhook authentication credential for securing webhook endpoints.
    
    This credential provides various authentication methods for webhook endpoints
    including basic auth, header auth, and JWT authentication.
    """
    
    name: str = "webhook_auth"
    display_name: str = "Webhook Authentication"
    description: str = "Authentication credentials for webhook endpoints"
    icon: str = "webhook"
    icon_color: str = "#4A90E2"
    icon_url: str = "https://example.com/icons/webhook.png"
    documentation_url: str = "https://docs.example.com/webhooks"
    subtitle: str = "Webhook authentication credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["webhook"]
    base_url: str = ""  # Webhooks don't have a base URL
    parameters: list[NodeParameter] = [
        NodeParameter(
            name="authentication_method",
            display_name="Authentication Method",
            description="The method to use for webhook authentication",
            type=PropertyTypes.OPTIONS,
            required=True,
            default="none",
            options=[
                {"name": "None", "value": "none"},
                {"name": "Basic Auth", "value": "basic"},
                {"name": "Header Auth", "value": "header"},
                {"name": "JWT Auth", "value": "jwt"}
            ]
        ),
        NodeParameter(
            name="username",
            display_name="Username",
            description="Username for basic authentication",
            type=PropertyTypes.STRING,
            required=True,
            display_options={
                "show": {"authentication_method": ["basic"]}
            }
        ),
        NodeParameter(
            name="password",
            display_name="Password",
            description="Password for basic authentication",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            display_options={
                "show": {"authentication_method": ["basic"]}
            }
        ),
        NodeParameter(
            name="header_name",
            display_name="Header Name",
            description="Name of the authentication header",
            type=PropertyTypes.STRING,
            required=True,
            default="X-API-Key",
            display_options={
                "show": {"authentication_method": ["header"]}
            }
        ),
        NodeParameter(
            name="header_value",
            display_name="Header Value",
            description="Value of the authentication header",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            display_options={
                "show": {"authentication_method": ["header"]}
            }
        ),
        NodeParameter(
            name="jwt_secret",
            display_name="JWT Secret",
            description="Secret key for JWT token verification",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            display_options={
                "show": {"authentication_method": ["jwt"]}
            }
        ),
        NodeParameter(
            name="jwt_algorithm",
            display_name="JWT Algorithm",
            description="Algorithm used for JWT token verification",
            type=PropertyTypes.OPTIONS,
            required=True,
            default="HS256",
            options=[
                {"name": "HS256", "value": "HS256"},
                {"name": "HS384", "value": "HS384"},
                {"name": "HS512", "value": "HS512"},
                {"name": "RS256", "value": "RS256"},
                {"name": "RS384", "value": "RS384"},
                {"name": "RS512", "value": "RS512"}
            ],
            display_options={
                "show": {"authentication_method": ["jwt"]}
            }
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        """
        Get authentication configuration based on the selected method.
        
        Returns:
            CredentialAuthModel: Authentication configuration
        """
        # For webhooks, authentication is handled in the webhook endpoint itself
        # This method returns a basic configuration
        return CredentialAuthModel(
            method=AuthMethod.NONE,
            headers={},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        """
        Get test configuration for webhook credentials.
        
        Returns:
            CredentialTestModel: Test configuration
        """
        # Webhook credentials don't have a standard test endpoint
        # The test is performed when the webhook is actually called
        return CredentialTestModel(
            endpoint="/test",
            method="GET",
            validation=lambda response: True  # Always pass for webhook credentials
        )
