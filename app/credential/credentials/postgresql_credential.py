"""
PostgreSQL Credential Model

This module implements the PostgreSQL credential model for database connections,
providing secure authentication and connection parameter management.
"""

from app.credential.base.credential_model import AuthMethod, CredentialAuthModel, CredentialModel, CredentialTestModel
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import NodeParameter, PropertyTypes, PropertyTypeOptions


@credential_provider(name="postgresql")
class PostgreSQLCredential(CredentialModel):
    """
    PostgreSQL database credential model.
    
    Provides secure storage and management of PostgreSQL connection parameters
    including host, port, database, authentication, and SSL configuration.
    """
    
    name: str = "postgresql"
    display_name: str = "PostgreSQL"
    description: str = "PostgreSQL database connection credentials"
    icon: str = "🐘"
    icon_color: str = "#336791"
    icon_url: str = "https://www.postgresql.org/media/img/about/press/elephant.png"
    documentation_url: str = "https://www.postgresql.org/docs/"
    subtitle: str = "PostgreSQL database credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["postgresql"]
    base_url: str = ""
    
    parameters: list[NodeParameter] = [
        NodeParameter(
            name="host",
            display_name="Host",
            description="The hostname or IP address of the PostgreSQL server",
            type=PropertyTypes.STRING,
            required=True,
            default="localhost",
            placeholder="localhost"
        ),
        NodeParameter(
            name="port",
            display_name="Port",
            description="The port number of the PostgreSQL server",
            type=PropertyTypes.NUMBER,
            required=True,
            default=5432,
            type_options=PropertyTypeOptions(
                min_value=1,
                max_value=65535,
                number_precision=0
            )
        ),
        NodeParameter(
            name="database",
            display_name="Database",
            description="The name of the PostgreSQL database to connect to",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="my_database"
        ),
        NodeParameter(
            name="username",
            display_name="Username",
            description="The username for database authentication",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="postgres"
        ),
        NodeParameter(
            name="password",
            display_name="Password",
            description="The password for database authentication",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True
        ),
        NodeParameter(
            name="ssl_mode",
            display_name="SSL Mode",
            description="SSL connection mode for secure connections",
            type=PropertyTypes.OPTIONS,
            required=False,
            default="prefer",
            options=[
                {"name": "Disable", "value": "disable"},
                {"name": "Allow", "value": "allow"},
                {"name": "Prefer", "value": "prefer"},
                {"name": "Require", "value": "require"},
                {"name": "Verify CA", "value": "verify-ca"},
                {"name": "Verify Full", "value": "verify-full"}
            ]
        ),
        NodeParameter(
            name="connection_timeout",
            display_name="Connection Timeout",
            description="Connection timeout in seconds (0 for no timeout)",
            type=PropertyTypes.NUMBER,
            required=False,
            default=30,
            type_options=PropertyTypeOptions(
                min_value=0,
                max_value=300,
                number_precision=0
            )
        ),
        NodeParameter(
            name="max_connections",
            display_name="Max Connections",
            description="Maximum number of connections in the pool",
            type=PropertyTypes.NUMBER,
            required=False,
            default=10,
            type_options=PropertyTypeOptions(
                min_value=1,
                max_value=100,
                number_precision=0
            )
        ),
        NodeParameter(
            name="allow_unauthorized_certs",
            display_name="Allow Unauthorized Certificates",
            description="Allow connections with self-signed or invalid SSL certificates",
            type=PropertyTypes.BOOLEAN,
            required=False,
            default=False
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        """
        Define authentication method for PostgreSQL connections.
        
        PostgreSQL uses username/password authentication, so we don't need
        special headers but we return the auth model for consistency.
        
        Returns:
            CredentialAuthModel: Authentication configuration
        """
        return CredentialAuthModel(
            method=AuthMethod.CUSTOM,  # PostgreSQL handles auth internally
            headers={},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        """
        Define credential testing configuration.
        
        Returns:
            CredentialTestModel: Test configuration for validating credentials
        """
        return CredentialTestModel(
            endpoint="",  # We'll handle testing in the node implementation
            method="GET",
            validation=lambda response: True  # Custom validation in node
        )
