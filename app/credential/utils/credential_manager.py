from contextlib import asynccontextmanager
from typing import Any, Dict
import re
import uuid
from typing import Optional

from app.core.database import As<PERSON><PERSON><PERSON><PERSON><PERSON>ocal
from app.core.security import decrypt_credential_data
from app.credential.base.credential_model import CredentialRequestModel
from app.credential.utils.credential_registry import Credential<PERSON><PERSON><PERSON>ry
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.credential_repository import CredentialRepository


class CredentialManager:

    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
    @asynccontextmanager
    async def _get_session(self):
        """
        Get a database session as an async context manager.
        Uses existing session if available, otherwise creates a new one.
        """
        if self.db is not None:
            yield self.db
        else:
            async with AsyncSessionLocal() as session:
                try:
                    yield session
                finally:
                    await session.close()

    def validate_credential(self, credential_request: CredentialRequestModel):
        credential = CredentialRegistry.get(credential_request.name)
        if not credential:
            raise ValueError(f"Credential type '{credential_request.name}' not found")
        
        if not credential.parameters:
            raise ValueError(f"Credential type '{credential_request.name}' has no parameters defined")
     
        for param in credential.parameters:
            # Check if required parameter is present
            if param.required and param.name not in credential_request.parameters:
                raise ValueError(f"Missing required parameter: {param.name}")
                
            # If parameter is present, validate its type
            if param.name in credential_request.parameters:
                value = credential_request.parameters[param.name]
                
                # Validate type based on PropertyTypes enum
                self._validate_param_type(param.name, value, param.type)
                
        return True
    
    async def test_credential(self, id: uuid.UUID):
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")
        try:
            credential_data = decrypt_credential_data(credential.data)
            test_config = credential_details.test()
            url = credential_details.base_url + test_config.endpoint
            auth = credential_details.authentication()
            headers = self._get_placeholder_values(auth.headers, credential_data)
            query_params = self._get_placeholder_values(auth.query_params, credential_data)

            from app.utils.http_client import HttpClient, HttpClientError
            response = await HttpClient.request(
                method=test_config.method,
                url=url,
                headers=headers,
                params=query_params,
                data=test_config.body
            )

            if test_config.validation is not None:
                if not test_config.validation(response["json"]):
                    raise ValueError("Credential test failed: Invalid response data.")
            
            if credential.status != "active":
                await credential_repo.update(credential, {"status": "active"})
                await self.db.commit()
            return True
        except HttpClientError as e:
            error_msg = str(e)
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_msg += " - Check if you're providing all required parameters for this API"
            raise ValueError(f"Failed to test credential: {error_msg}")
    
    async def get_credential(self, credential_id: str, node_type: str) -> Dict[str, Any]:
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        async with self._get_session() as db:
            credential_repo = CredentialRepository(db)
            credential = await credential_repo.get_by_id(id) # TODO: Use a more efficient query with conditions (method, status, node_type)
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            nested_data = decrypt_credential_data(credential.data)
            flat_data = {}
            for key, details in nested_data.items():
                flat_data[key] = details["value"]
            return flat_data
            

    def _get_placeholder_values(self, placeholder_dict: Dict[str, str], params: Dict[str, Dict[str, Any]]) -> dict[str, str]:
        result = placeholder_dict.copy()
        for key, value in result.items():
            # Find all placeholders like {access_token} within the header value
            placeholders = re.findall(r"{(.*?)}", value)

            # Replace each placeholder with its corresponding value from params
            for placeholder in placeholders:
                if placeholder in params:
                    value = value.replace(f"{{{placeholder}}}", str(params[placeholder]["value"]))
                else:
                    raise ValueError(f"Missing value for placeholder: {placeholder}")
            
            # Update the header with the replaced value
            result[key] = value

        return result
    
    async def auth_request(self, credential_id: str, url: str) -> Dict[str, Any]:
        """
        Authenticate a request using a credential.
        
        Args:
            credential_id: ID of the credential to use
            url: The original URL to make the request to
            
        Returns:
            Dict containing base_url, headers and query_params
        """
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {credential_id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")

        # Extract endpoint from the original URL
        original_url_parts = url.split("://", 1)
        if len(original_url_parts) > 1:
            _, path_and_query = original_url_parts
            path_parts = path_and_query.split("/", 1)
            endpoint = "/" + path_parts[1] if len(path_parts) > 1 else "/"
        else:
            endpoint = "/"
            
        # Decrypt credential data
        credential_data = decrypt_credential_data(credential.data)

        # Get authentication headers
        auth = credential_details.authentication()
        headers = self._get_placeholder_values(auth.headers, credential_data)
        query_params = self._get_placeholder_values(auth.query_params, credential_data)

        endpoint = self._get_endpoint_placeholder_values(endpoint, credential_data)
        
        return {
            "base_url": credential_details.base_url,
            "endpoint": endpoint,
            "headers": headers,
            "query_params": query_params
        }

    def _get_endpoint_placeholder_values(self, endpoint: str, params: Dict[str, Dict[str, Any]]) -> str:
        result = endpoint
            # Find all placeholders like {access_token} within the header value
        placeholders = re.findall(r"{(.*?)}", result)

        # Replace each placeholder with its corresponding value from params
        for placeholder in placeholders:
            if placeholder.startswith("credential.") and placeholder.split(".", 1)[1] in params:
                result = result.replace(f"{{{placeholder}}}", str(params[placeholder.split(".", 1)[1]]["value"]))
            else:
                raise ValueError(f"Missing value for placeholder: {placeholder}")
        return result
    
    def _validate_param_type(self, param_name: str, value: Any, param_type_enum: str) -> None:
        """
        Validate that a parameter value matches the expected type from PropertyTypes enum.
        
        Args:
            param_name: The name of the parameter being validated
            value: The value to validate
            param_type_enum: The PropertyTypes enum value
            
        Raises:
            ValueError: If the value doesn't match the expected type
        """
        from app.node.node_base.node_models import PropertyTypes
        
        # Handle validation based on the type specified in PropertyTypes enum
        if param_type_enum == PropertyTypes.STRING:
            if not isinstance(value, str):
                raise ValueError(f"Parameter '{param_name}' must be a string, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.NUMBER:
            if not isinstance(value, (int, float)):
                raise ValueError(f"Parameter '{param_name}' must be a number, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.BOOLEAN:
            if not isinstance(value, bool):
                raise ValueError(f"Parameter '{param_name}' must be a boolean, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.JSON:
            if not isinstance(value, (dict, list)):
                raise ValueError(f"Parameter '{param_name}' must be a JSON object or array, got {type(value).__name__}")
        
        elif param_type_enum == PropertyTypes.OPTIONS or param_type_enum == PropertyTypes.MULTI_OPTIONS:
            # These are typically string values selected from options
            if not isinstance(value, str) and not (isinstance(value, list) and all(isinstance(item, str) for item in value)):
                raise ValueError(f"Parameter '{param_name}' must be a string or list of strings, got {type(value).__name__}")
        
        # Add more type validations as needed for other PropertyTypes
        # For now, other types will pass validation
