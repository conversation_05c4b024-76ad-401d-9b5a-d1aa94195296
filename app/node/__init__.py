"""
Nodes Package

This package contains the base Node class, node-related models, implementations
of various node types, and the node registry for discovering and searching nodes.
"""

import importlib
import os
from pathlib import Path
# First, import all base classes and models
from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import (
    # Type definitions
    NodeParameterValue,
    NodeGroupType,
    NodeConnectionType,
    
    # Enums
    PropertyTypes,
    NodePropertyAction,
    CodeAutocompleteTypes,
    EditorType,
    SQLDialect,
    
    # Models
    NodeTypeDescription,
    NodeParameter,
    NodeParameterOption,
    NodeParameterValueType,
    NodeData,
    NodeConnection,
    NodeCredentialDescription,
    NodeRequest,
    ValidationResult,
    
    # UI models
    PropertyTypeOptions,
    CalloutAction,
    DisplayOptions,
    DisplayCondition,
    CredentialsDisplayOptions,
)

# Then import node implementations
from app.node.nodes.wait.wait_node import WaitNode
from app.node.nodes.http_request.http_request_node import HTTPRequestNode
from app.node.nodes.if_node.if_node import IfNode
from app.node.nodes.switch_node.switch_node import SwitchNode
from app.node.nodes.split_batches_node.split_batches_node import SplitBatchesNode
from app.node.nodes.edit_field_node.edit_field_node import EditFieldNode
from app.node.nodes.merge_node.merge_node import MergeNode
from app.node.nodes.webhook.webhook_node import WebhookNode
from app.node.nodes.sort.sort_node import SortNode

# Import WhatsAppNode after all the models have been imported
from app.node.nodes.whatsapp.whatsapp_node import WhatsAppNode
# Import PostgreSQLNode
from app.node.nodes.postgresql.postgresql_node import PostgreSQLNode
# Import FilterNode
from app.node.nodes.filter_node.filter_node import FilterNode
# Import MongoDBNode
from app.node.nodes.mongodb.mongodb_node import MongoDBNode
#import GmailNode
from app.node.nodes.gmail.gmail_node import GmailNode

__all__ = [
    # Base classes
    'Node',
    'NodeResult',
    

    # Node implementations
    'WaitNode',
    'HTTPRequestNode',
    'IfNode',
    'SwitchNode',
    'SplitBatchesNode',
    'EditFieldNode',
    'MergeNode',
    'SortNode',
    'WhatsAppNode',
    'PostgreSQLNode',
    'FilterNode',
    'MongoDBNode',
    'GmailNode',
    
    # Registry
    # 'node_registry',
    # 'register_builtin_nodes',
    
    # Type definitions and models
    'NodeParameterValue',
    'NodeGroupType',
    'NodeConnectionType',
    'PropertyTypes',
    'NodePropertyAction',
    'CodeAutocompleteTypes',
    'EditorType',
    'SQLDialect',
    'NodeTypeDescription',
    'NodeParameter',
    'NodeParameterOption',
    'NodeParameterValueType',
    'NodeData',
    'NodeConnection',
    'NodeCredentialDescription',
    'NodeRequest',
    'ValidationResult',
    'PropertyTypeOptions',
    'CalloutAction',
    'DisplayOptions',
    'DisplayCondition',
    'CredentialsDisplayOptions',
]

