"""
Wait Node Model

This module defines the model for the wait node, which introduces configurable
waiting/pausing functionality in workflow execution.
"""

from typing import ClassVar, List, Optional

from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    NodeParameterOption,
    PropertyTypes,
    NodeConnectionType,
    NodeGroupType,
    DisplayOptions,
    PropertyTypeOptions
)


class WaitNodeDescription(NodeTypeDescription):
    """
    Description for the Wait Node.
    
    This node pauses execution for a specified duration or until a specific
    date/time before allowing the workflow to continue.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "wait"
    
    @classmethod
    def create(cls) -> "WaitNodeDescription":
        """
        Factory method to create a standard wait node description.
        
        Returns:
            WaitNodeDescription: A configured description for wait nodes
        """
        return cls(
            name="wait",
            display_name="Wait",
            description="Pauses workflow execution for a specified duration or until a specific time.",
            icon="pause-circle",
            icon_color="#804050",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="resume_type",
                    display_name="Resume",
                    description="How to determine when to resume workflow execution",
                    type=PropertyTypes.OPTIONS,
                    default="time_interval",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="After Time Interval", 
                            value="time_interval", 
                            description="Waits for a certain amount of time"
                        ),
                        NodeParameterOption(
                            name="At Specified Time", 
                            value="specific_time", 
                            description="Waits until a specific date and time to continue"
                        )
                    ]
                ),
                NodeParameter(
                    name="wait_value",
                    display_name="Wait Amount",
                    description="How long to wait before resuming execution (maximum equivalent of 1 year)",
                    type=PropertyTypes.NUMBER,
                    default=5,
                    required=True,
                    display_options=DisplayOptions(
                        show={"resume_type": ["time_interval"]}
                    ),
                    type_options=PropertyTypeOptions(min_value=0, number_precision=2)
                ),
                NodeParameter(
                    name="wait_unit",
                    display_name="Wait Unit",
                    description="Unit of time for the wait duration",
                    type=PropertyTypes.OPTIONS,
                    default="seconds",
                    required=True,
                    display_options=DisplayOptions(
                        show={"resume_type": ["time_interval"]}
                    ),
                    options=[
                        NodeParameterOption(name="Seconds", value="seconds", description="Wait in seconds"),
                        NodeParameterOption(name="Minutes", value="minutes", description="Wait in minutes"),
                        NodeParameterOption(name="Hours", value="hours", description="Wait in hours"),
                        NodeParameterOption(name="Days", value="days", description="Wait in days"),
                    ]
                ),
                NodeParameter(
                    name="date_time",
                    display_name="Date and Time",
                    description="The specific date and time to wait until before continuing",
                    type=PropertyTypes.DATE_TIME,
                    default=None,
                    required=True,
                    display_options=DisplayOptions(
                        show={"resume_type": ["specific_time"]}
                    ),
                ),
            ],
        )
