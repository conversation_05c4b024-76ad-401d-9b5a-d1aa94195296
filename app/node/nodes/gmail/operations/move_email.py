"""
Gmail Move Email Operation

This module implements the move email operation for Gmail nodes,
providing the ability to move emails between folders/labels.
"""

import logging
from typing import Dict, Any, List, Union
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class MoveEmailOperation(BaseOperation):
    """
    Gmail MOVE EMAIL operation implementation.
    
    Provides the ability to move emails between folders/labels by:
    - Adding and removing label IDs from messages
    - Supporting common folder operations (inbox, sent, drafts, etc.)
    - Supporting both single message ID and multiple message IDs
    - Proper error handling and result formatting
    """
    
    # Common Gmail label mappings
    FOLDER_LABEL_MAP = {
        'inbox': 'INBOX',
        'sent': 'SENT',
        'drafts': 'DRAFT',
        'spam': 'SPAM',
        'trash': 'TRASH',
        'important': 'IMPORTANT',
        'starred': 'STARRED'
    }
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a MOVE EMAIL operation on Gmail.
        
        Args:
            data: Node execution data containing message parameters
            
        Returns:
            NodeResult: Operation result or error information
        """
        try:
            # Extract parameters
            message_ids = data.parameters.get('message_ids')
            add_labels = data.parameters.get('add_labels', [])
            remove_labels = data.parameters.get('remove_labels', [])
            destination_folder = data.parameters.get('destination_folder', '')
            
            # Validate required parameters
            if not message_ids:
                return NodeResult(error="Message IDs are required for MOVE EMAIL operation")
            
            if not add_labels and not remove_labels and not destination_folder:
                return NodeResult(error="At least one of add_labels, remove_labels, or destination_folder must be specified")
            
            # Parse message IDs
            if isinstance(message_ids, str):
                id_list = [msg_id.strip() for msg_id in message_ids.split(',') if msg_id.strip()]
            elif isinstance(message_ids, list):
                id_list = [str(msg_id).strip() for msg_id in message_ids if str(msg_id).strip()]
            else:
                return NodeResult(error="Message IDs must be a string or list")
            
            if not id_list:
                return NodeResult(error="No valid message IDs provided")
            
            # Parse labels
            add_label_ids = MoveEmailOperation._parse_labels(add_labels)
            remove_label_ids = MoveEmailOperation._parse_labels(remove_labels)
            
            # Handle destination folder
            if destination_folder:
                folder_label = MoveEmailOperation._get_folder_label(destination_folder)
                if folder_label:
                    add_label_ids.append(folder_label)
                    # Remove INBOX when moving to other folders (except SENT, DRAFT)
                    if folder_label not in ['SENT', 'DRAFT'] and 'INBOX' not in remove_label_ids:
                        remove_label_ids.append('INBOX')
                else:
                    return NodeResult(error=f"Unknown destination folder: {destination_folder}")
            
            if not add_label_ids and not remove_label_ids:
                return NodeResult(error="No valid labels to add or remove")
            
            logger.info(f"Moving {len(id_list)} messages - adding: {add_label_ids}, removing: {remove_label_ids}")
            
            # Execute move operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    results = []
                    errors = []
                    
                    for message_id in id_list:
                        try:
                            result = await MoveEmailOperation._modify_message_labels(
                                session=session,
                                message_id=message_id,
                                add_label_ids=add_label_ids,
                                remove_label_ids=remove_label_ids
                            )
                            results.append(result)
                        except Exception as e:
                            error_msg = f"Failed to move message {message_id}: {str(e)}"
                            errors.append(error_msg)
                            logger.warning(error_msg)
                    
                    # Format result
                    success_count = len(results)
                    error_count = len(errors)
                    
                    logger.info(f"Move operation completed: {success_count} successful, {error_count} errors")
                    
                    formatted_result = MoveEmailOperation.format_results(
                        data={
                            'successful_messages': results,
                            'errors': errors,
                            'success_count': success_count,
                            'error_count': error_count,
                            'total_requested': len(id_list),
                            'added_labels': add_label_ids,
                            'removed_labels': remove_label_ids
                        },
                        operation="move_email",
                        processed_count=len(id_list)
                    )
                    
                    # Return error if all operations failed
                    if success_count == 0 and error_count > 0:
                        return NodeResult(error=f"Failed to move any messages. Errors: {'; '.join(errors)}")
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = MoveEmailOperation.format_error(e, "move email")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error moving messages: {str(e)}")
        
        except Exception as e:
            error_msg = MoveEmailOperation.format_error(e, "move email")
            logger.error(f"Move email operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    def _parse_labels(labels: Union[str, List[str]]) -> List[str]:
        """
        Parse labels from string or list format.
        
        Args:
            labels: Labels as string (comma-separated) or list
            
        Returns:
            List[str]: List of label IDs
        """
        if not labels:
            return []
        
        if isinstance(labels, str):
            label_list = [label.strip() for label in labels.split(',') if label.strip()]
        elif isinstance(labels, list):
            label_list = [str(label).strip() for label in labels if str(label).strip()]
        else:
            return []
        
        # Convert folder names to label IDs
        converted_labels = []
        for label in label_list:
            folder_label = MoveEmailOperation._get_folder_label(label)
            if folder_label:
                converted_labels.append(folder_label)
            else:
                # Assume it's already a label ID
                converted_labels.append(label)
        
        return converted_labels
    
    @staticmethod
    def _get_folder_label(folder: str) -> str:
        """
        Get Gmail label ID for a folder name.
        
        Args:
            folder: Folder name
            
        Returns:
            str: Gmail label ID or empty string if not found
        """
        folder_lower = folder.lower()
        return MoveEmailOperation.FOLDER_LABEL_MAP.get(folder_lower, '')
    
    @staticmethod
    async def _modify_message_labels(
        session: aiohttp.ClientSession,
        message_id: str,
        add_label_ids: List[str],
        remove_label_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Modify labels for a single message.
        
        Args:
            session: Authenticated HTTP session
            message_id: ID of the message to modify
            add_label_ids: List of label IDs to add
            remove_label_ids: List of label IDs to remove
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}/modify"
        
        payload = {}
        if add_label_ids:
            payload['addLabelIds'] = add_label_ids
        if remove_label_ids:
            payload['removeLabelIds'] = remove_label_ids
        
        async with session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to modify message labels: {error_text}"
                )
            
            result = await response.json()
            
            return {
                'message_id': message_id,
                'thread_id': result.get('threadId'),
                'label_ids': result.get('labelIds', []),
                'added_labels': add_label_ids,
                'removed_labels': remove_label_ids,
                'status': 'moved'
            }
