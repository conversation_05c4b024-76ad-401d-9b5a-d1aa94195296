"""
Gmail Mark Unread Operation

This module implements the mark unread operation for Gmail nodes,
providing the ability to mark emails as unread.
"""

import logging
from typing import Dict, Any, List, Union
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class MarkUnreadOperation(BaseOperation):
    """
    Gmail MARK UNREAD operation implementation.
    
    Provides the ability to mark one or more emails as unread by:
    - Adding the UNREAD label to specified messages
    - Supporting both single message ID and multiple message IDs
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a MARK UNREAD operation on Gmail.
        
        Args:
            data: Node execution data containing message parameters
            
        Returns:
            NodeResult: Operation result or error information
        """
        try:
            # Extract parameters
            message_ids = data.parameters.get('message_ids')
            
            # Validate required parameters
            if not message_ids:
                return NodeResult(error="Message IDs are required for MARK UNREAD operation")
            
            # Parse message IDs
            if isinstance(message_ids, str):
                id_list = [msg_id.strip() for msg_id in message_ids.split(',') if msg_id.strip()]
            elif isinstance(message_ids, list):
                id_list = [str(msg_id).strip() for msg_id in message_ids if str(msg_id).strip()]
            else:
                return NodeResult(error="Message IDs must be a string or list")
            
            if not id_list:
                return NodeResult(error="No valid message IDs provided")
            
            logger.info(f"Marking {len(id_list)} messages as unread")
            
            # Execute mark unread operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    results = []
                    errors = []
                    
                    for message_id in id_list:
                        try:
                            result = await MarkUnreadOperation._mark_single_message_unread(
                                session=session,
                                message_id=message_id
                            )
                            results.append(result)
                        except Exception as e:
                            error_msg = f"Failed to mark message {message_id} as unread: {str(e)}"
                            errors.append(error_msg)
                            logger.warning(error_msg)
                    
                    # Format result
                    success_count = len(results)
                    error_count = len(errors)
                    
                    logger.info(f"Mark unread completed: {success_count} successful, {error_count} errors")
                    
                    formatted_result = MarkUnreadOperation.format_results(
                        data={
                            'successful_messages': results,
                            'errors': errors,
                            'success_count': success_count,
                            'error_count': error_count,
                            'total_requested': len(id_list)
                        },
                        operation="mark_unread",
                        processed_count=len(id_list)
                    )
                    
                    # Return error if all operations failed
                    if success_count == 0 and error_count > 0:
                        return NodeResult(error=f"Failed to mark any messages as unread. Errors: {'; '.join(errors)}")
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = MarkUnreadOperation.format_error(e, "mark unread")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error marking messages as unread: {str(e)}")
        
        except Exception as e:
            error_msg = MarkUnreadOperation.format_error(e, "mark unread")
            logger.error(f"Mark unread operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _mark_single_message_unread(
        session: aiohttp.ClientSession,
        message_id: str
    ) -> Dict[str, Any]:
        """
        Mark a single message as unread by adding the UNREAD label.
        
        Args:
            session: Authenticated HTTP session
            message_id: ID of the message to mark as unread
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}/modify"
        
        payload = {
            'addLabelIds': ['UNREAD']
        }
        
        async with session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to mark message as unread: {error_text}"
                )
            
            result = await response.json()
            
            return {
                'message_id': message_id,
                'thread_id': result.get('threadId'),
                'label_ids': result.get('labelIds', []),
                'status': 'marked_unread'
            }
