"""
Gmail Remove Label Operation

This module implements the remove label operation for Gmail nodes,
providing the ability to remove labels from existing emails.
"""

import logging
from typing import Dict, Any, List, Union
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class RemoveLabelOperation(BaseOperation):
    """
    Gmail REMOVE LABEL operation implementation.
    
    Provides the ability to remove labels from emails with:
    - Support for multiple message IDs
    - Support for multiple label IDs
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a REMOVE LABEL operation on Gmail.
        
        Args:
            data: Node execution data containing message and label parameters
            
        Returns:
            NodeResult: Operation result or error information
        """
        try:
            # Extract parameters
            message_ids = data.parameters.get('message_ids')
            label_ids = data.parameters.get('label_ids')
            
            # Validate required parameters
            if not message_ids:
                return NodeResult(error="Message IDs are required for REMOVE LABEL operation")
            
            if not label_ids:
                return NodeResult(error="Label IDs are required for REMOVE LABEL operation")
            
            # Parse message IDs
            if isinstance(message_ids, str):
                msg_id_list = [msg_id.strip() for msg_id in message_ids.split(',') if msg_id.strip()]
            elif isinstance(message_ids, list):
                msg_id_list = [str(msg_id).strip() for msg_id in message_ids if str(msg_id).strip()]
            else:
                return NodeResult(error="Message IDs must be a string or list")
            
            # Parse label IDs
            if isinstance(label_ids, str):
                label_id_list = [label_id.strip() for label_id in label_ids.split(',') if label_id.strip()]
            elif isinstance(label_ids, list):
                label_id_list = [str(label_id).strip() for label_id in label_ids if str(label_id).strip()]
            else:
                return NodeResult(error="Label IDs must be a string or list")
            
            if not msg_id_list:
                return NodeResult(error="No valid message IDs provided")
            
            if not label_id_list:
                return NodeResult(error="No valid label IDs provided")
            
            logger.info(f"Removing {len(label_id_list)} labels from {len(msg_id_list)} messages")
            
            # Execute remove label operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    results = []
                    errors = []
                    
                    for message_id in msg_id_list:
                        try:
                            result = await RemoveLabelOperation._remove_labels_from_message(
                                session=session,
                                message_id=message_id,
                                label_ids=label_id_list
                            )
                            results.append(result)
                        except Exception as e:
                            error_msg = f"Failed to remove labels from message {message_id}: {str(e)}"
                            errors.append(error_msg)
                            logger.warning(error_msg)
                    
                    # Format result
                    success_count = len(results)
                    error_count = len(errors)
                    
                    logger.info(f"Remove label completed: {success_count} successful, {error_count} errors")
                    
                    formatted_result = RemoveLabelOperation.format_results(
                        data={
                            'successful_messages': results,
                            'errors': errors,
                            'success_count': success_count,
                            'error_count': error_count,
                            'total_requested': len(msg_id_list),
                            'removed_labels': label_id_list
                        },
                        operation="remove_label",
                        processed_count=len(msg_id_list),
                        labels_removed=len(label_id_list)
                    )
                    
                    # Return error if all operations failed
                    if success_count == 0 and error_count > 0:
                        return NodeResult(error=f"Failed to remove labels from any messages. Errors: {'; '.join(errors)}")
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = RemoveLabelOperation.format_error(e, "remove label")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error removing labels: {str(e)}")
        
        except Exception as e:
            error_msg = RemoveLabelOperation.format_error(e, "remove label")
            logger.error(f"Remove label operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _remove_labels_from_message(
        session: aiohttp.ClientSession,
        message_id: str,
        label_ids: List[str]
    ) -> Dict[str, Any]:
        """
        Remove labels from a single message.
        
        Args:
            session: Authenticated HTTP session
            message_id: ID of the message to modify
            label_ids: List of label IDs to remove
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}/modify"
        
        payload = {
            'removeLabelIds': label_ids
        }
        
        async with session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to remove labels from message: {error_text}"
                )
            
            result = await response.json()
            
            return {
                'message_id': message_id,
                'thread_id': result.get('threadId'),
                'label_ids': result.get('labelIds', []),
                'removed_labels': label_ids,
                'status': 'labels_removed'
            }
