"""
Gmail Send Email Operation

This module implements the send email operation for Gmail nodes,
providing comprehensive email sending capabilities with attachments and formatting.
"""

import logging
import json
import base64
from typing import Dict, Any, List, Optional, Union
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>Base
from email import encoders
import aiohttp

from app.node.node_base.node import Node<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class SendEmailOperation(BaseOperation):
    """
    Gmail SEND EMAIL operation implementation.
    
    Provides comprehensive email sending capabilities including:
    - HTML and plain text content
    - Multiple recipients (to, cc, bcc)
    - File attachments with proper encoding
    - Custom headers and reply-to addresses
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a SEND EMAIL operation on Gmail.
        
        Args:
            data: Node execution data containing email parameters
            
        Returns:
            NodeResult: Send result or error information
        """
        try:
            # Extract parameters
            to_emails = data.parameters.get('to')
            cc_emails = data.parameters.get('cc', '')
            bcc_emails = data.parameters.get('bcc', '')
            subject = data.parameters.get('subject', '')
            body_text = data.parameters.get('body_text', '')
            body_html = data.parameters.get('body_html', '')
            reply_to = data.parameters.get('reply_to', '')
            attachments = data.parameters.get('attachments', [])
            
            # Validate required parameters
            if not to_emails:
                return NodeResult(error="To email addresses are required for SEND EMAIL operation")
            
            if not subject and not body_text and not body_html:
                return NodeResult(error="Subject or body content is required for SEND EMAIL operation")
            
            # Parse and validate email addresses
            try:
                to_list = SendEmailOperation.parse_email_list(to_emails)
                cc_list = SendEmailOperation.parse_email_list(cc_emails) if cc_emails else []
                bcc_list = SendEmailOperation.parse_email_list(bcc_emails) if bcc_emails else []
                
                if reply_to and not SendEmailOperation.validate_email_address(reply_to):
                    return NodeResult(error=f"Invalid reply-to email address: {reply_to}")
                    
            except ValueError as e:
                return NodeResult(error=str(e))
            
            logger.info(f"Sending email to {len(to_list)} recipients")
            logger.debug(f"Subject: {subject}")
            
            # Create email message
            try:
                message = await SendEmailOperation._create_email_message(
                    to_list=to_list,
                    cc_list=cc_list,
                    bcc_list=bcc_list,
                    subject=subject,
                    body_text=body_text,
                    body_html=body_html,
                    reply_to=reply_to,
                    attachments=attachments
                )
            except Exception as e:
                return NodeResult(error=f"Failed to create email message: {str(e)}")
            
            # Send email via Gmail API
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    # Encode message for Gmail API
                    encoded_message = SendEmailOperation.encode_message(message)
                    
                    # Prepare API request
                    url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/send"
                    payload = {
                        'raw': encoded_message
                    }
                    
                    # Send the email
                    async with session.post(url, json=payload) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            return NodeResult(error=f"Failed to send email: {error_text}")
                        
                        result_data = await response.json()
                        
                        logger.info(f"Email sent successfully. Message ID: {result_data.get('id')}")
                        
                        # Format successful result
                        formatted_result = SendEmailOperation.format_results(
                            data={
                                'message_id': result_data.get('id'),
                                'thread_id': result_data.get('threadId'),
                                'to': to_list,
                                'cc': cc_list,
                                'bcc': bcc_list,
                                'subject': subject,
                                'sent_from': user_email
                            },
                            operation="send_email",
                            recipients_count=len(to_list) + len(cc_list) + len(bcc_list),
                            has_attachments=len(attachments) > 0
                        )
                        
                        return NodeResult(data=formatted_result)
                        
                except aiohttp.ClientError as e:
                    error_msg = SendEmailOperation.format_error(e, "send email")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error sending email: {str(e)}")
        
        except Exception as e:
            error_msg = SendEmailOperation.format_error(e, "send email")
            logger.error(f"Send email operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _create_email_message(
        to_list: List[str],
        cc_list: List[str],
        bcc_list: List[str],
        subject: str,
        body_text: str,
        body_html: str,
        reply_to: str,
        attachments: List[Dict[str, Any]]
    ) -> MIMEMultipart:
        """
        Create a MIME email message with all components.
        
        Args:
            to_list: List of recipient email addresses
            cc_list: List of CC email addresses
            bcc_list: List of BCC email addresses
            subject: Email subject
            body_text: Plain text body
            body_html: HTML body
            reply_to: Reply-to email address
            attachments: List of attachment data
            
        Returns:
            MIMEMultipart: Complete email message
        """
        # Create message container
        message = MIMEMultipart('alternative')
        
        # Set headers
        message['To'] = ', '.join(to_list)
        if cc_list:
            message['Cc'] = ', '.join(cc_list)
        if bcc_list:
            message['Bcc'] = ', '.join(bcc_list)
        message['Subject'] = subject
        if reply_to:
            message['Reply-To'] = reply_to
        
        # Add body content
        if body_text:
            text_part = MIMEText(body_text, 'plain', 'utf-8')
            message.attach(text_part)
        
        if body_html:
            html_part = MIMEText(body_html, 'html', 'utf-8')
            message.attach(html_part)
        
        # Add attachments
        if attachments:
            for attachment in attachments:
                try:
                    await SendEmailOperation._add_attachment(message, attachment)
                except Exception as e:
                    logger.warning(f"Failed to add attachment: {str(e)}")
                    # Continue with other attachments
        
        return message
    
    @staticmethod
    async def _add_attachment(message: MIMEMultipart, attachment: Dict[str, Any]):
        """
        Add an attachment to the email message.
        
        Args:
            message: Email message to add attachment to
            attachment: Attachment data containing filename and content
        """
        filename = attachment.get('filename', 'attachment')
        content = attachment.get('content', '')
        content_type = attachment.get('content_type', 'application/octet-stream')
        
        if not content:
            raise ValueError(f"Attachment {filename} has no content")
        
        # Create attachment part
        attachment_part = MIMEBase(*content_type.split('/', 1))
        
        # Handle different content formats
        if isinstance(content, str):
            # Assume base64 encoded content
            try:
                attachment_data = base64.b64decode(content)
            except Exception:
                # If not base64, encode as UTF-8
                attachment_data = content.encode('utf-8')
        elif isinstance(content, bytes):
            attachment_data = content
        else:
            attachment_data = str(content).encode('utf-8')
        
        attachment_part.set_payload(attachment_data)
        encoders.encode_base64(attachment_part)
        
        # Add headers
        attachment_part.add_header(
            'Content-Disposition',
            f'attachment; filename="{filename}"'
        )
        
        message.attach(attachment_part)
