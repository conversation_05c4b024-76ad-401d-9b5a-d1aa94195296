"""
Gmail List Labels Operation

This module implements the list labels operation for Gmail nodes,
providing the ability to retrieve all available labels.
"""

import logging
from typing import Dict, Any, List
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class ListLabelsOperation(BaseOperation):
    """
    Gmail LIST LABELS operation implementation.
    
    Provides the ability to retrieve all Gmail labels including:
    - System labels (INBOX, SENT, DRAFT, etc.)
    - User-created labels
    - Label metadata (name, type, visibility, etc.)
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a LIST LABELS operation on Gmail.
        
        Args:
            data: Node execution data
            
        Returns:
            NodeResult: List of labels or error information
        """
        try:
            # Extract parameters
            include_system = data.parameters.get('include_system', True)
            include_user = data.parameters.get('include_user', True)
            
            logger.info(f"Listing Gmail labels - system: {include_system}, user: {include_user}")
            
            # Execute list labels operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    labels = await ListLabelsOperation._get_all_labels(session)
                    
                    # Filter labels based on parameters
                    filtered_labels = []
                    for label in labels:
                        label_type = label.get('type', 'user')
                        
                        if label_type == 'system' and include_system:
                            filtered_labels.append(label)
                        elif label_type == 'user' and include_user:
                            filtered_labels.append(label)
                    
                    logger.info(f"Retrieved {len(filtered_labels)} labels")
                    
                    # Format result
                    formatted_result = ListLabelsOperation.format_results(
                        data=filtered_labels,
                        operation="list_labels",
                        total_count=len(filtered_labels),
                        system_labels_included=include_system,
                        user_labels_included=include_user
                    )
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = ListLabelsOperation.format_error(e, "list labels")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error listing labels: {str(e)}")
        
        except Exception as e:
            error_msg = ListLabelsOperation.format_error(e, "list labels")
            logger.error(f"List labels operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _get_all_labels(session: aiohttp.ClientSession) -> List[Dict[str, Any]]:
        """
        Get all labels from Gmail API.
        
        Args:
            session: Authenticated HTTP session
            
        Returns:
            List[Dict[str, Any]]: List of label information
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/labels"
        
        async with session.get(url) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to list labels: {error_text}"
                )
            
            result = await response.json()
            labels = result.get('labels', [])
            
            # Parse and format label data
            formatted_labels = []
            for label in labels:
                formatted_label = ListLabelsOperation._parse_label_data(label)
                formatted_labels.append(formatted_label)
            
            return formatted_labels
    
    @staticmethod
    def _parse_label_data(label: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse Gmail API label data into a standardized format.
        
        Args:
            label: Raw label data from Gmail API
            
        Returns:
            Dict[str, Any]: Parsed label data
        """
        return {
            'id': label.get('id'),
            'name': label.get('name'),
            'type': label.get('type', 'user'),
            'messages_total': label.get('messagesTotal', 0),
            'messages_unread': label.get('messagesUnread', 0),
            'threads_total': label.get('threadsTotal', 0),
            'threads_unread': label.get('threadsUnread', 0),
            'label_list_visibility': label.get('labelListVisibility', 'labelShow'),
            'message_list_visibility': label.get('messageListVisibility', 'show'),
            'color': label.get('color', {}),
            'is_system': label.get('type') == 'system'
        }
