"""
Gmail Delete Email Operation

This module implements the delete email operation for Gmail nodes,
providing the ability to permanently delete emails or move them to trash.
"""

import logging
from typing import Dict, Any, List, Union
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class DeleteEmailOperation(BaseOperation):
    """
    Gmail DELETE EMAIL operation implementation.
    
    Provides the ability to delete emails with options for:
    - Moving to trash (default, recoverable)
    - Permanent deletion (irreversible)
    - Supporting both single message ID and multiple message IDs
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a DELETE EMAIL operation on Gmail.
        
        Args:
            data: Node execution data containing message parameters
            
        Returns:
            NodeResult: Operation result or error information
        """
        try:
            # Extract parameters
            message_ids = data.parameters.get('message_ids')
            permanent = data.parameters.get('permanent', False)
            
            # Validate required parameters
            if not message_ids:
                return NodeResult(error="Message IDs are required for DELETE EMAIL operation")
            
            # Parse message IDs
            if isinstance(message_ids, str):
                id_list = [msg_id.strip() for msg_id in message_ids.split(',') if msg_id.strip()]
            elif isinstance(message_ids, list):
                id_list = [str(msg_id).strip() for msg_id in message_ids if str(msg_id).strip()]
            else:
                return NodeResult(error="Message IDs must be a string or list")
            
            if not id_list:
                return NodeResult(error="No valid message IDs provided")
            
            delete_type = "permanently" if permanent else "to trash"
            logger.info(f"Deleting {len(id_list)} messages {delete_type}")
            
            # Execute delete operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    results = []
                    errors = []
                    
                    for message_id in id_list:
                        try:
                            if permanent:
                                result = await DeleteEmailOperation._delete_message_permanently(
                                    session=session,
                                    message_id=message_id
                                )
                            else:
                                result = await DeleteEmailOperation._move_message_to_trash(
                                    session=session,
                                    message_id=message_id
                                )
                            results.append(result)
                        except Exception as e:
                            error_msg = f"Failed to delete message {message_id}: {str(e)}"
                            errors.append(error_msg)
                            logger.warning(error_msg)
                    
                    # Format result
                    success_count = len(results)
                    error_count = len(errors)
                    
                    logger.info(f"Delete operation completed: {success_count} successful, {error_count} errors")
                    
                    formatted_result = DeleteEmailOperation.format_results(
                        data={
                            'successful_messages': results,
                            'errors': errors,
                            'success_count': success_count,
                            'error_count': error_count,
                            'total_requested': len(id_list),
                            'permanent_delete': permanent
                        },
                        operation="delete_email",
                        processed_count=len(id_list),
                        delete_type=delete_type
                    )
                    
                    # Return error if all operations failed
                    if success_count == 0 and error_count > 0:
                        return NodeResult(error=f"Failed to delete any messages. Errors: {'; '.join(errors)}")
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = DeleteEmailOperation.format_error(e, "delete email")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error deleting messages: {str(e)}")
        
        except Exception as e:
            error_msg = DeleteEmailOperation.format_error(e, "delete email")
            logger.error(f"Delete email operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _move_message_to_trash(
        session: aiohttp.ClientSession,
        message_id: str
    ) -> Dict[str, Any]:
        """
        Move a message to trash (recoverable deletion).
        
        Args:
            session: Authenticated HTTP session
            message_id: ID of the message to move to trash
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}/trash"
        
        async with session.post(url) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to move message to trash: {error_text}"
                )
            
            result = await response.json()
            
            return {
                'message_id': message_id,
                'thread_id': result.get('threadId'),
                'label_ids': result.get('labelIds', []),
                'status': 'moved_to_trash',
                'permanent': False
            }
    
    @staticmethod
    async def _delete_message_permanently(
        session: aiohttp.ClientSession,
        message_id: str
    ) -> Dict[str, Any]:
        """
        Permanently delete a message (irreversible).
        
        Args:
            session: Authenticated HTTP session
            message_id: ID of the message to delete permanently
            
        Returns:
            Dict[str, Any]: Result of the operation
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}"
        
        async with session.delete(url) as response:
            if response.status != 204:  # Gmail API returns 204 for successful deletion
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to delete message permanently: {error_text}"
                )
            
            return {
                'message_id': message_id,
                'status': 'deleted_permanently',
                'permanent': True
            }
