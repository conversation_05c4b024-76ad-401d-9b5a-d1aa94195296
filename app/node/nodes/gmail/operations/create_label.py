"""
Gmail Create Label Operation

This module implements the create label operation for Gmail nodes,
providing the ability to create new custom labels.
"""

import logging
from typing import Dict, Any, Optional
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class CreateLabelOperation(BaseOperation):
    """
    Gmail CREATE LABEL operation implementation.
    
    Provides the ability to create new Gmail labels with:
    - Custom label names
    - Visibility settings (show/hide in label list and message list)
    - Color customization
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a CREATE LABEL operation on Gmail.
        
        Args:
            data: Node execution data containing label parameters
            
        Returns:
            NodeResult: Created label information or error information
        """
        try:
            # Extract parameters
            label_name = data.parameters.get('label_name', '').strip()
            label_list_visibility = data.parameters.get('label_list_visibility', 'labelShow')
            message_list_visibility = data.parameters.get('message_list_visibility', 'show')
            background_color = data.parameters.get('background_color', '')
            text_color = data.parameters.get('text_color', '')
            
            # Validate required parameters
            if not label_name:
                return NodeResult(error="Label name is required for CREATE LABEL operation")
            
            # Validate visibility options
            valid_label_visibility = ['labelShow', 'labelHide']
            valid_message_visibility = ['show', 'hide']
            
            if label_list_visibility not in valid_label_visibility:
                return NodeResult(error=f"label_list_visibility must be one of: {', '.join(valid_label_visibility)}")
            
            if message_list_visibility not in valid_message_visibility:
                return NodeResult(error=f"message_list_visibility must be one of: {', '.join(valid_message_visibility)}")
            
            logger.info(f"Creating Gmail label: {label_name}")
            
            # Execute create label operation
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    label_data = await CreateLabelOperation._create_single_label(
                        session=session,
                        label_name=label_name,
                        label_list_visibility=label_list_visibility,
                        message_list_visibility=message_list_visibility,
                        background_color=background_color,
                        text_color=text_color
                    )
                    
                    logger.info(f"Label created successfully: {label_data.get('name')} (ID: {label_data.get('id')})")
                    
                    # Format result
                    formatted_result = CreateLabelOperation.format_results(
                        data=label_data,
                        operation="create_label",
                        label_name=label_name
                    )
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = CreateLabelOperation.format_error(e, "create label")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error creating label: {str(e)}")
        
        except Exception as e:
            error_msg = CreateLabelOperation.format_error(e, "create label")
            logger.error(f"Create label operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _create_single_label(
        session: aiohttp.ClientSession,
        label_name: str,
        label_list_visibility: str,
        message_list_visibility: str,
        background_color: str,
        text_color: str
    ) -> Dict[str, Any]:
        """
        Create a single label using Gmail API.
        
        Args:
            session: Authenticated HTTP session
            label_name: Name of the label to create
            label_list_visibility: Visibility in label list
            message_list_visibility: Visibility in message list
            background_color: Background color (hex format)
            text_color: Text color (hex format)
            
        Returns:
            Dict[str, Any]: Created label information
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/labels"
        
        # Prepare label payload
        payload = {
            'name': label_name,
            'labelListVisibility': label_list_visibility,
            'messageListVisibility': message_list_visibility
        }
        
        # Add color information if provided
        color_info = {}
        if background_color:
            color_info['backgroundColor'] = background_color
        if text_color:
            color_info['textColor'] = text_color
        
        if color_info:
            payload['color'] = color_info
        
        async with session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                
                # Handle specific error cases
                if response.status == 409:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"Label '{label_name}' already exists"
                    )
                else:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"Failed to create label: {error_text}"
                    )
            
            result = await response.json()
            
            # Parse and format the created label data
            return CreateLabelOperation._parse_created_label(result)
    
    @staticmethod
    def _parse_created_label(label: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse created label data into a standardized format.
        
        Args:
            label: Raw label data from Gmail API
            
        Returns:
            Dict[str, Any]: Parsed label data
        """
        return {
            'id': label.get('id'),
            'name': label.get('name'),
            'type': label.get('type', 'user'),
            'messages_total': label.get('messagesTotal', 0),
            'messages_unread': label.get('messagesUnread', 0),
            'threads_total': label.get('threadsTotal', 0),
            'threads_unread': label.get('threadsUnread', 0),
            'label_list_visibility': label.get('labelListVisibility'),
            'message_list_visibility': label.get('messageListVisibility'),
            'color': label.get('color', {}),
            'created': True
        }
