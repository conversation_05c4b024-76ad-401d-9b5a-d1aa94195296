"""
Gmail Read Emails Operation

This module implements the read emails operation for Gmail nodes,
providing comprehensive email retrieval with filtering, pagination, and metadata extraction.
"""

import logging
import json
import base64
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import aiohttp

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, GmailConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class ReadEmailsOperation(BaseOperation):
    """
    Gmail READ EMAILS operation implementation.
    
    Provides comprehensive email reading capabilities including:
    - Inbox and folder-specific queries
    - Advanced filtering with Gmail search syntax
    - Pagination with configurable limits
    - Metadata extraction (headers, labels, etc.)
    - Attachment information
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a READ EMAILS operation on Gmail.
        
        Args:
            data: Node execution data containing query parameters
            
        Returns:
            NodeResult: Email list or error information
        """
        try:
            # Extract parameters
            query = data.parameters.get('query', '')
            max_results = data.parameters.get('max_results', 10)
            include_spam_trash = data.parameters.get('include_spam_trash', False)
            label_ids = data.parameters.get('label_ids', [])
            format_type = data.parameters.get('format', 'metadata')  # metadata, minimal, full
            
            # Validate parameters
            if not isinstance(max_results, int) or max_results < 1 or max_results > 500:
                return NodeResult(error="max_results must be an integer between 1 and 500")
            
            if format_type not in ['metadata', 'minimal', 'full']:
                return NodeResult(error="format must be one of: metadata, minimal, full")
            
            # Parse label_ids if provided as string
            if isinstance(label_ids, str):
                label_ids = [label.strip() for label in label_ids.split(',') if label.strip()]
            
            logger.info(f"Reading emails with query: '{query}', max_results: {max_results}")
            
            # Execute email search and retrieval
            async with GmailConnectionManager.get_session(data) as (session, access_token, user_email):
                try:
                    # First, get list of message IDs
                    message_ids = await ReadEmailsOperation._search_messages(
                        session=session,
                        query=query,
                        max_results=max_results,
                        include_spam_trash=include_spam_trash,
                        label_ids=label_ids
                    )
                    
                    if not message_ids:
                        logger.info("No emails found matching criteria")
                        formatted_result = ReadEmailsOperation.format_results(
                            data=[],
                            operation="read_emails",
                            total_count=0,
                            query=query
                        )
                        return NodeResult(data=formatted_result)
                    
                    # Get detailed message information
                    emails = await ReadEmailsOperation._get_message_details(
                        session=session,
                        message_ids=message_ids,
                        format_type=format_type
                    )
                    
                    logger.info(f"Retrieved {len(emails)} emails successfully")
                    
                    # Format successful result
                    formatted_result = ReadEmailsOperation.format_results(
                        data=emails,
                        operation="read_emails",
                        total_count=len(emails),
                        query=query,
                        format=format_type
                    )
                    
                    return NodeResult(data=formatted_result)
                    
                except aiohttp.ClientError as e:
                    error_msg = ReadEmailsOperation.format_error(e, "read emails")
                    return NodeResult(error=error_msg)
                except Exception as e:
                    return NodeResult(error=f"Unexpected error reading emails: {str(e)}")
        
        except Exception as e:
            error_msg = ReadEmailsOperation.format_error(e, "read emails")
            logger.error(f"Read emails operation failed: {error_msg}")
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _search_messages(
        session: aiohttp.ClientSession,
        query: str,
        max_results: int,
        include_spam_trash: bool,
        label_ids: List[str]
    ) -> List[str]:
        """
        Search for messages using Gmail API.
        
        Args:
            session: Authenticated HTTP session
            query: Gmail search query
            max_results: Maximum number of results
            include_spam_trash: Whether to include spam and trash
            label_ids: List of label IDs to filter by
            
        Returns:
            List[str]: List of message IDs
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages"
        
        params = {
            'maxResults': min(max_results, 500),  # Gmail API limit
            'includeSpamTrash': str(include_spam_trash).lower()
        }
        
        if query:
            params['q'] = query
        
        if label_ids:
            params['labelIds'] = label_ids
        
        async with session.get(url, params=params) as response:
            if response.status != 200:
                error_text = await response.text()
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status,
                    message=f"Failed to search messages: {error_text}"
                )
            
            result = await response.json()
            messages = result.get('messages', [])
            
            return [msg['id'] for msg in messages]
    
    @staticmethod
    async def _get_message_details(
        session: aiohttp.ClientSession,
        message_ids: List[str],
        format_type: str
    ) -> List[Dict[str, Any]]:
        """
        Get detailed information for messages.
        
        Args:
            session: Authenticated HTTP session
            message_ids: List of message IDs to retrieve
            format_type: Level of detail to retrieve
            
        Returns:
            List[Dict[str, Any]]: List of message details
        """
        emails = []
        
        for message_id in message_ids:
            try:
                email_data = await ReadEmailsOperation._get_single_message(
                    session=session,
                    message_id=message_id,
                    format_type=format_type
                )
                if email_data:
                    emails.append(email_data)
            except Exception as e:
                logger.warning(f"Failed to retrieve message {message_id}: {str(e)}")
                # Continue with other messages
        
        return emails
    
    @staticmethod
    async def _get_single_message(
        session: aiohttp.ClientSession,
        message_id: str,
        format_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get details for a single message.
        
        Args:
            session: Authenticated HTTP session
            message_id: Message ID to retrieve
            format_type: Level of detail to retrieve
            
        Returns:
            Optional[Dict[str, Any]]: Message details or None if failed
        """
        url = f"{GmailConnectionManager.GMAIL_API_BASE_URL}/users/me/messages/{message_id}"
        
        params = {'format': format_type}
        
        async with session.get(url, params=params) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.warning(f"Failed to get message {message_id}: {error_text}")
                return None
            
            message_data = await response.json()
            
            # Parse and format message data
            return ReadEmailsOperation._parse_message_data(message_data, format_type)
    
    @staticmethod
    def _parse_message_data(message_data: Dict[str, Any], format_type: str) -> Dict[str, Any]:
        """
        Parse Gmail API message data into a standardized format.
        
        Args:
            message_data: Raw message data from Gmail API
            format_type: Format type used for retrieval
            
        Returns:
            Dict[str, Any]: Parsed message data
        """
        parsed = {
            'id': message_data.get('id'),
            'thread_id': message_data.get('threadId'),
            'label_ids': message_data.get('labelIds', []),
            'snippet': message_data.get('snippet', ''),
            'history_id': message_data.get('historyId'),
            'internal_date': message_data.get('internalDate'),
            'size_estimate': message_data.get('sizeEstimate')
        }
        
        # Add timestamp in readable format
        if parsed['internal_date']:
            try:
                timestamp = int(parsed['internal_date']) / 1000
                parsed['date'] = datetime.fromtimestamp(timestamp).isoformat()
            except (ValueError, TypeError):
                parsed['date'] = None
        
        # Parse payload if available
        payload = message_data.get('payload', {})
        if payload:
            # Extract headers
            headers = {}
            for header in payload.get('headers', []):
                headers[header['name'].lower()] = header['value']
            
            parsed.update({
                'subject': headers.get('subject', ''),
                'from': headers.get('from', ''),
                'to': headers.get('to', ''),
                'cc': headers.get('cc', ''),
                'bcc': headers.get('bcc', ''),
                'date_header': headers.get('date', ''),
                'message_id_header': headers.get('message-id', ''),
                'mime_type': payload.get('mimeType', ''),
                'filename': payload.get('filename', '')
            })
            
            # Extract body content for full format
            if format_type == 'full':
                body_data = ReadEmailsOperation._extract_body_content(payload)
                parsed.update(body_data)
            
            # Extract attachment information
            attachments = ReadEmailsOperation._extract_attachment_info(payload)
            if attachments:
                parsed['attachments'] = attachments
        
        return parsed
    
    @staticmethod
    def _extract_body_content(payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract body content from message payload.
        
        Args:
            payload: Message payload from Gmail API
            
        Returns:
            Dict[str, Any]: Body content data
        """
        body_data = {
            'body_text': '',
            'body_html': ''
        }
        
        def extract_from_part(part):
            mime_type = part.get('mimeType', '')
            body = part.get('body', {})
            
            if body.get('data'):
                try:
                    decoded = base64.urlsafe_b64decode(body['data']).decode('utf-8')
                    if mime_type == 'text/plain':
                        body_data['body_text'] = decoded
                    elif mime_type == 'text/html':
                        body_data['body_html'] = decoded
                except Exception:
                    pass
            
            # Recursively check parts
            for subpart in part.get('parts', []):
                extract_from_part(subpart)
        
        extract_from_part(payload)
        return body_data
    
    @staticmethod
    def _extract_attachment_info(payload: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract attachment information from message payload.
        
        Args:
            payload: Message payload from Gmail API
            
        Returns:
            List[Dict[str, Any]]: List of attachment information
        """
        attachments = []
        
        def extract_from_part(part):
            body = part.get('body', {})
            filename = part.get('filename', '')
            
            if filename and body.get('attachmentId'):
                attachments.append({
                    'filename': filename,
                    'mime_type': part.get('mimeType', ''),
                    'size': body.get('size', 0),
                    'attachment_id': body.get('attachmentId')
                })
            
            # Recursively check parts
            for subpart in part.get('parts', []):
                extract_from_part(subpart)
        
        extract_from_part(payload)
        return attachments
