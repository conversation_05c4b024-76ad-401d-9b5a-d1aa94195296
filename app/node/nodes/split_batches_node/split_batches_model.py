from typing import ClassVar
from app.node.node_base.node_models import (
    NodeConnectionType, 
    NodeParameter, 
    NodeParameterOption, 
    PropertyTypes, 
    NodeTypeDescription,
    DisplayOptions,
    PropertyTypeOptions
)


class SplitBatchesNodeDescription(NodeTypeDescription):
    """
    Description for the Split in Batches Node.

    This node splits data into batches and iterates over each batch, allowing for
    controlled processing of large datasets. It maintains state between executions
    to handle batch processing workflows.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "split_batches"

    @classmethod
    def create(cls) -> "SplitBatchesNodeDescription":
        """
        Factory method to create a standard Split in Batches node description.

        Returns:
            SplitBatchesNodeDescription: A configured description for Split in Batches nodes
        """
        return cls(
            name="split_batches",
            display_name="Loop Over Items (Split in Batches)",
            description="Split data into batches and iterate over each batch",
            icon="sync",
            icon_color="#007755",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main, NodeConnectionType.Main],
            output_names=["done", "loop"],
            parameters=[
                NodeParameter(
                    name="split_batches_notice",
                    display_name="Notice",
                    description="You may not need this node — workflow nodes automatically run once for each input item. This node is useful when you need to process items in specific batch sizes.",
                    type=PropertyTypes.NOTICE,
                    default="",
                    required=False
                ),
                NodeParameter(
                    name="batch_size",
                    display_name="Batch Size",
                    description="The number of items to return with each call",
                    type=PropertyTypes.NUMBER,
                    default=1,
                    required=True,
                    type_options=PropertyTypeOptions(
                        min_value=1,
                        max_value=1000
                    )
                ),
                NodeParameter(
                    name="input_data",
                    display_name="Input Data",
                    description="The data to split into batches (array or JSON string). Leave empty to use incoming data.",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False
                ),
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional options for batch processing",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Reset",
                            value="reset",
                            description="Whether the node starts again from the beginning of the input items"
                        ),
                        NodeParameterOption(
                            name="Include Batch Info",
                            value="include_batch_info",
                            description="Whether to include batch metadata in the output"
                        ),
                        NodeParameterOption(
                            name="Preserve Order",
                            value="preserve_order",
                            description="Whether to maintain the original order of items"
                        )
                    ]
                ),
                NodeParameter(
                    name="reset",
                    display_name="Reset",
                    description="Whether the node starts again from the beginning of the input items. This will treat incoming data as a new set rather than continuing with the previous items.",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["reset"]
                        }
                    )
                ),
                NodeParameter(
                    name="include_batch_info",
                    display_name="Include Batch Info",
                    description="Whether to include batch metadata (batch number, total batches, etc.) in the output",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["include_batch_info"]
                        }
                    )
                ),
                NodeParameter(
                    name="preserve_order",
                    display_name="Preserve Order",
                    description="Whether to maintain the original order of items across batches",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["preserve_order"]
                        }
                    )
                ),
                NodeParameter(
                    name="batch_overlap",
                    display_name="Batch Overlap",
                    description="Number of items to overlap between batches (0 = no overlap)",
                    type=PropertyTypes.NUMBER,
                    default=0,
                    required=False,
                    type_options=PropertyTypeOptions(
                        min_value=0,
                        max_value=100
                    )
                ),
                NodeParameter(
                    name="processing_mode",
                    display_name="Processing Mode",
                    description="How to handle the batch processing",
                    type=PropertyTypes.OPTIONS,
                    default="sequential",
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Sequential", 
                            value="sequential",
                            description="Process batches one after another"
                        ),
                        NodeParameterOption(
                            name="Parallel Preparation", 
                            value="parallel_prep",
                            description="Prepare batches for parallel processing"
                        ),
                        NodeParameterOption(
                            name="Collect Results", 
                            value="collect",
                            description="Collect and combine all batch results"
                        )
                    ]
                ),
                NodeParameter(
                    name="error_handling",
                    display_name="Error Handling",
                    description="How to handle errors during batch processing",
                    type=PropertyTypes.OPTIONS,
                    default="stop",
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Stop on Error", 
                            value="stop",
                            description="Stop processing when an error occurs"
                        ),
                        NodeParameterOption(
                            name="Continue on Error", 
                            value="continue",
                            description="Continue processing remaining batches if one fails"
                        ),
                        NodeParameterOption(
                            name="Skip Failed Batches", 
                            value="skip",
                            description="Skip failed batches and continue with successful ones"
                        )
                    ]
                ),
                NodeParameter(
                    name="output_format",
                    display_name="Output Format",
                    description="How to format the output data",
                    type=PropertyTypes.OPTIONS,
                    default="items_only",
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Items Only", 
                            value="items_only",
                            description="Output only the batch items"
                        ),
                        NodeParameterOption(
                            name="With Metadata", 
                            value="with_metadata",
                            description="Include batch metadata with items"
                        ),
                        NodeParameterOption(
                            name="Structured", 
                            value="structured",
                            description="Output in a structured format with batch info"
                        )
                    ]
                )
            ]
        )
