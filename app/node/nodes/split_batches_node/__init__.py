"""
Split in Batches Node Module

This module provides batch processing capabilities for the Cerebro workflow system.
The Split in Batches node can split data into configurable batches and maintain
state between executions for iterative processing.
"""

from .split_batches_node import SplitBatchesNode
from .split_batches_model import SplitBatchesNodeDescription

__all__ = [
    'SplitBatchesNode',
    'SplitBatchesNodeDescription'
]
