from app.node.node_base.node import Node, Node<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult, ValidationError
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.split_batches_node.split_batches_model import SplitBatchesNodeDescription
import json
import copy
from typing import Any, Dict, List, Union, Optional


@node_defn(type='split_batches', is_activity=False)
class SplitBatchesNode(Node):
    """
    Split in Batches Node for batch processing workflows.
    
    This node splits input data into configurable batches and maintains state
    between executions to enable iterative batch processing. It supports
    various batch processing modes and output formats.
    """

    def __init__(self):
        super().__init__()
        # Node context for maintaining state between executions
        # Each instance gets its own context
        self.node_context = {}

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return SplitBatchesNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Split in Batches node's batch processing logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with batch data and appropriate output routing
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get parameters
            batch_size = int(str(self.data.parameters.get('batch_size', 1)))
            input_data = self.data.parameters.get('input_data', '')
            reset = bool(self.data.parameters.get('reset', False))
            
            # Parse input data
            items = await self._parse_input_data(input_data)
            
            # Handle batch processing
            return await self._process_batches(items, batch_size, reset)
            
        except Exception as e:
            return NodeResult(error=f"Batch processing error: {str(e)}")
    
    async def _parse_input_data(self, input_data: Any) -> List[Any]:
        """Parse input data from various formats."""
        if not input_data or (isinstance(input_data, str) and input_data.strip() == ''):
            # Use empty list if no input data provided
            # In a real implementation, this would come from the workflow input
            return []
        
        try:
            if isinstance(input_data, str):
                if input_data.strip().startswith(('[', '{')):
                    parsed_data = json.loads(input_data)
                    if isinstance(parsed_data, list):
                        return parsed_data
                    elif isinstance(parsed_data, dict):
                        # Convert dict to list of key-value pairs
                        return [{"key": k, "value": v} for k, v in parsed_data.items()]
                    else:
                        return [parsed_data]
                else:
                    # Treat as comma-separated values
                    return [item.strip() for item in input_data.split(',') if item.strip()]
            elif isinstance(input_data, list):
                return input_data
            else:
                return [input_data]
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON input data: {str(e)}")
    
    async def _process_batches(self, items: List[Any], batch_size: int, reset: bool) -> NodeResult:
        """Process items in batches with state management."""
        
        # Initialize or reset context
        if 'items' not in self.node_context or reset:
            self.node_context = {
                'items': copy.deepcopy(items),
                'original_items': copy.deepcopy(items),
                'current_run_index': 0,
                'max_run_index': self._calculate_max_runs(len(items), batch_size),
                'processed_items': [],
                'done': False,
                'no_items_left': len(items) == 0
            }

            # If we have items, process the first batch immediately
            if items:
                current_batch = await self._get_current_batch(batch_size)
                if current_batch:
                    result = await self._format_batch_output(current_batch)
                    self.node_context['current_run_index'] += 1
                    self.node_context['no_items_left'] = len(self.node_context['items']) == 0
                    return NodeResult(
                        result=result,
                        next_connection_index=1  # Loop output
                    )

            # No items to process
            return await self._handle_completion()
        
        # Continue processing subsequent batches
        current_batch = await self._get_current_batch(batch_size)

        # Check if processing is complete
        if not current_batch or self.node_context.get('no_items_left', False):
            return await self._handle_completion()

        # Format output
        result = await self._format_batch_output(current_batch)

        # Update context for next iteration
        self.node_context['current_run_index'] += 1
        self.node_context['no_items_left'] = len(self.node_context['items']) == 0

        # Route to loop output (index 1) for continued processing
        return NodeResult(
            result=result,
            next_connection_index=1  # Loop output
        )
    
    async def _get_current_batch(self, batch_size: int) -> List[Any]:
        """Get the current batch of items."""
        if not self.node_context.get('items'):
            return []
        
        # Handle batch overlap
        batch_overlap = int(str(self.data.parameters.get('batch_overlap', 0)))
        
        if self.node_context['current_run_index'] == 0:
            # First batch - no overlap
            batch = self.node_context['items'][:batch_size]
            self.node_context['items'] = self.node_context['items'][batch_size:]
        else:
            # Subsequent batches - handle overlap
            start_index = max(0, batch_size - batch_overlap)
            batch = self.node_context['items'][:batch_size]
            self.node_context['items'] = self.node_context['items'][start_index:]
        
        return batch
    
    async def _handle_completion(self) -> NodeResult:
        """Handle completion of all batches."""
        processing_mode = str(self.data.parameters.get('processing_mode', 'sequential'))
        
        if processing_mode == 'collect':
            # Return all processed items
            result = {
                "all_items": self.node_context.get('processed_items', []),
                "total_batches": self.node_context.get('max_run_index', 0),
                "total_items": len(self.node_context.get('original_items', [])),
                "processing_complete": True
            }
        else:
            # Return completion summary
            result = {
                "processing_complete": True,
                "total_batches": self.node_context.get('max_run_index', 0),
                "total_items": len(self.node_context.get('original_items', [])),
                "processed_items_count": len(self.node_context.get('processed_items', []))
            }
        
        # Reset context for next execution
        self.node_context = {}
        
        # Route to done output (index 0)
        return NodeResult(
            result=result,
            next_connection_index=0  # Done output
        )
    
    async def _format_batch_output(self, batch: List[Any]) -> Dict[str, Any]:
        """Format the batch output according to configuration."""
        output_format = str(self.data.parameters.get('output_format', 'items_only'))
        include_batch_info = bool(self.data.parameters.get('include_batch_info', True))
        
        # Initialize result as a dictionary
        result: Dict[str, Any] = {}
        
        if output_format == 'items_only':
            result = {"items": batch}
        elif output_format == 'structured':
            current_batch_number = self.node_context['current_run_index'] + 1
            result = {
                "batch": {
                    "items": batch,
                    "size": len(batch),
                    "number": current_batch_number,
                    "total_batches": self.node_context['max_run_index'],
                    "is_last": current_batch_number >= self.node_context['max_run_index']
                }
            }
        else:  # with_metadata
            result = {"items": batch}
        
        
        # Add batch info if requested
        if include_batch_info and output_format != 'structured':
            current_batch_number = self.node_context['current_run_index'] + 1
            result["batch_info"] = {
                "batch_number": current_batch_number,
                "batch_size": len(batch),
                "total_batches": self.node_context['max_run_index'],
                "total_items": len(self.node_context.get('original_items', [])),
                "is_first_batch": self.node_context['current_run_index'] == 0,
                "is_last_batch": current_batch_number >= self.node_context['max_run_index'],
                "progress_percent": round(current_batch_number / self.node_context['max_run_index'] * 100, 2) if self.node_context['max_run_index'] > 0 else 100
            }
        
        # Store processed items for collection mode
        if str(self.data.parameters.get('processing_mode', 'sequential')) == 'collect':
            self.node_context.setdefault('processed_items', []).extend(batch)
        
        return result
    
    def _calculate_max_runs(self, total_items: int, batch_size: int) -> int:
        """Calculate the maximum number of batch runs needed."""
        if total_items == 0 or batch_size <= 0:
            return 0
        
        import math
        return math.ceil(total_items / batch_size)
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """Validate the Split in Batches node request."""
        # Perform base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        # Add specific validation for Split in Batches node
        errors = []
        
        if not request.parameters:
            return ValidationResult(valid=False, errors=[ValidationError(parameter="", message="Parameters are required")])
        
        # Validate batch size
        batch_size = request.parameters.get('batch_size')
        if batch_size is not None:
            try:
                batch_size_int = int(str(batch_size))
                if batch_size_int < 1:
                    errors.append(ValidationError(parameter="batch_size", message="Batch size must be at least 1"))
                elif batch_size_int > 1000:
                    errors.append(ValidationError(parameter="batch_size", message="Batch size cannot exceed 1000"))
            except (ValueError, TypeError):
                errors.append(ValidationError(parameter="batch_size", message="Batch size must be a valid number"))
        
        # Validate batch overlap
        batch_overlap = request.parameters.get('batch_overlap')
        if batch_overlap is not None:
            try:
                overlap_int = int(str(batch_overlap))
                if overlap_int < 0:
                    errors.append(ValidationError(parameter="batch_overlap", message="Batch overlap cannot be negative"))
                elif overlap_int > 100:
                    errors.append(ValidationError(parameter="batch_overlap", message="Batch overlap cannot exceed 100"))
                
                # Check if overlap is not larger than batch size
                if batch_size is not None:
                    try:
                        batch_size_int = int(str(batch_size))
                        if overlap_int >= batch_size_int:
                            errors.append(ValidationError(parameter="batch_overlap", message="Batch overlap must be smaller than batch size"))
                    except (ValueError, TypeError):
                        pass  # Already handled above
            except (ValueError, TypeError):
                errors.append(ValidationError(parameter="batch_overlap", message="Batch overlap must be a valid number"))
        
        # Validate input data if provided
        input_data = request.parameters.get('input_data')
        if input_data and isinstance(input_data, str) and input_data.strip():
            if input_data.strip().startswith(('[', '{')):
                try:
                    json.loads(input_data)
                except json.JSONDecodeError:
                    errors.append(ValidationError(parameter="input_data", message="Invalid JSON format in input data"))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
