from typing import ClassVar
from app.node.node_base.node_models import (
    NodeConnectionType, 
    NodeParameter, 
    NodeParameterOption, 
    PropertyTypes, 
    NodeTypeDescription,
    DisplayOptions,
    PropertyTypeOptions
)


class IfNodeDescription(NodeTypeDescription):
    """
    Description for the If Node.

    This node evaluates conditional expressions and routes workflow execution
    based on the result (true/false branches).
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "if"

    @classmethod
    def create(cls) -> "IfNodeDescription":
        """
        Factory method to create a standard If node description.

        Returns:
            IfNodeDescription: A configured description for If nodes
        """
        return cls(
            name="if",
            display_name="If",
            description="Evaluates conditions and routes execution based on true/false results.",
            icon="code-branch",
            icon_color="#408000",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main, NodeConnectionType.Main],
            output_names=["true", "false"],
            parameters=[
                NodeParameter(
                    name="condition_type",
                    display_name="Condition Type",
                    description="Type of condition to evaluate",
                    type=PropertyTypes.OPTIONS,
                    default="simple",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Simple Comparison", 
                            value="simple",
                            description="Compare two values using basic operators"
                        ),
                        NodeParameterOption(
                            name="Multiple Conditions", 
                            value="multiple",
                            description="Evaluate multiple conditions with AND/OR logic"
                        ),
                        NodeParameterOption(
                            name="Expression", 
                            value="expression",
                            description="Evaluate a custom JavaScript expression"
                        )
                    ]
                ),
                # Simple condition parameters
                NodeParameter(
                    name="value1",
                    display_name="Value 1",
                    description="First value to compare",
                    type=PropertyTypes.STRING,
                    default="",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["simple"]
                        }
                    )
                ),
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="Comparison operation to perform",
                    type=PropertyTypes.OPTIONS,
                    default="equal",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["simple"]
                        }
                    ),
                    options=[
                        NodeParameterOption(name="Equal", value="equal"),
                        NodeParameterOption(name="Not Equal", value="notEqual"),
                        NodeParameterOption(name="Greater Than", value="greaterThan"),
                        NodeParameterOption(name="Less Than", value="lessThan"),
                        NodeParameterOption(name="Greater Than or Equal", value="greaterThanOrEqual"),
                        NodeParameterOption(name="Less Than or Equal", value="lessThanOrEqual"),
                        NodeParameterOption(name="Contains", value="contains"),
                        NodeParameterOption(name="Not Contains", value="notContains"),
                        NodeParameterOption(name="Starts With", value="startsWith"),
                        NodeParameterOption(name="Ends With", value="endsWith"),
                        NodeParameterOption(name="Is Empty", value="isEmpty"),
                        NodeParameterOption(name="Is Not Empty", value="isNotEmpty"),
                        NodeParameterOption(name="Regex Match", value="regex")
                    ]
                ),
                NodeParameter(
                    name="value2",
                    display_name="Value 2",
                    description="Second value to compare",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["simple"],
                            "operation": ["equal", "notEqual", "greaterThan", "lessThan", 
                                        "greaterThanOrEqual", "lessThanOrEqual", "contains", 
                                        "notContains", "startsWith", "endsWith", "regex"]
                        }
                    )
                ),
                # Multiple conditions parameters
                NodeParameter(
                    name="conditions",
                    display_name="Conditions",
                    description="List of conditions to evaluate",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["multiple"]
                        }
                    ),
                    type_options=PropertyTypeOptions(
                        multiple_values=True,
                        multiple_value_button_text="Add Condition"
                    )
                ),
                NodeParameter(
                    name="combine_operation",
                    display_name="Combine Operation",
                    description="How to combine multiple conditions",
                    type=PropertyTypes.OPTIONS,
                    default="AND",
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["multiple"]
                        }
                    ),
                    options=[
                        NodeParameterOption(name="AND", value="AND"),
                        NodeParameterOption(name="OR", value="OR")
                    ]
                ),
                # Expression parameter
                NodeParameter(
                    name="expression",
                    display_name="Expression",
                    description="JavaScript expression to evaluate (must return boolean)",
                    type=PropertyTypes.STRING,
                    default="",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "condition_type": ["expression"]
                        }
                    )
                ),
                # Options
                NodeParameter(
                    name="case_sensitive",
                    display_name="Case Sensitive",
                    description="Whether string comparisons should be case sensitive",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False
                ),
                NodeParameter(
                    name="ignore_whitespace",
                    display_name="Ignore Whitespace",
                    description="Whether to ignore leading/trailing whitespace in string comparisons",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False
                )
            ]
        )
