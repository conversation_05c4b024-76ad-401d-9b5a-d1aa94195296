from app.node.node_base.node import Node, Node<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult, ValidationError
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.if_node.if_model import IfNodeDescription
from simpleeval import EvalWithCompoundTypes
import json
import re
from typing import Any, Dict, List, Union



@node_defn(type='if', is_activity=False)
class IfNode(Node):
    """
    If Node for conditional workflow routing.
    
    This node evaluates conditions and routes execution to either the 'true' or 'false' output
    based on the evaluation result. Supports simple comparisons, multiple conditions, and
    custom JavaScript expressions.
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return IfNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the If node's conditional logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with next_connection_index set to 0 (true) or 1 (false)
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get condition type
            condition_type = str(self.data.parameters.get('condition_type', 'simple'))
            
            # Evaluate condition based on type
            if condition_type == 'simple':
                result = await self._evaluate_simple_condition()
            elif condition_type == 'multiple':
                result = await self._evaluate_multiple_conditions()
            elif condition_type == 'expression':
                result = await self._evaluate_expression()
            else:
                return NodeResult(error=f"Unknown condition type: {condition_type}")
            
            # Return result with appropriate connection index
            # 0 = true branch, 1 = false branch
            next_connection_index = 0 if result else 1
            
            return NodeResult(
                result={"condition_result": result},
                next_connection_index=next_connection_index
            )
            
        except Exception as e:
            return NodeResult(error=f"Condition evaluation error: {str(e)}")
    
    async def _evaluate_simple_condition(self) -> bool:
        """Evaluate a simple two-value comparison."""
        value1 = self.data.parameters.get('value1', '')
        value2 = self.data.parameters.get('value2', '')
        operation = str(self.data.parameters.get('operation', 'equal'))
        
        # Get options
        case_sensitive = bool(self.data.parameters.get('case_sensitive', True))
        ignore_whitespace = bool(self.data.parameters.get('ignore_whitespace', False))
        
        # Process values
        val1 = self._process_value(value1, case_sensitive, ignore_whitespace)
        val2 = self._process_value(value2, case_sensitive, ignore_whitespace)
        
        # Perform comparison
        return self._compare_values(val1, val2, operation)
    
    async def _evaluate_multiple_conditions(self) -> bool:
        """Evaluate multiple conditions with AND/OR logic."""
        conditions = self.data.parameters.get('conditions', [])
        combine_operation = str(self.data.parameters.get('combine_operation', 'AND'))
        
        if not conditions:
            return False
        
        # Ensure conditions is a list
        if not isinstance(conditions, list):
            conditions = [conditions]
            
        results = []
        for condition in conditions:
            if isinstance(condition, dict):
                # Each condition should have value1, operation, value2
                val1 = condition.get('value1', '')
                val2 = condition.get('value2', '')
                op = str(condition.get('operation', 'equal'))
                
                # Get options
                case_sensitive = bool(self.data.parameters.get('case_sensitive', True))
                ignore_whitespace = bool(self.data.parameters.get('ignore_whitespace', False))
                
                # Process and compare
                processed_val1 = self._process_value(val1, case_sensitive, ignore_whitespace)
                processed_val2 = self._process_value(val2, case_sensitive, ignore_whitespace)
                
                result = self._compare_values(processed_val1, processed_val2, op)
                results.append(result)
        
        # Combine results
        if combine_operation == 'AND':
            return all(results)
        else:  # OR
            return any(results)
    
    async def _evaluate_expression(self) -> bool:
        """Evaluate a custom JavaScript-like expression."""
        expression = str(self.data.parameters.get('expression', ''))
        
        if not expression:
            return False
        
        try:
            # Process expression to convert JavaScript-like syntax to Python
            processed_expr = self._process_expression(expression)
            
            # Create a secure evaluator with limited functions
            evaluator = EvalWithCompoundTypes(
                functions={
                    "len": len,
                    "str": str,
                    "int": int,
                    "float": float,
                    "bool": bool
                }
            )
            
            # Safely evaluate the expression
            result = evaluator.eval(processed_expr)
            return bool(result)
            
        except Exception as e:
            raise ValueError(f"Expression evaluation failed: {str(e)}")
    
    def _process_value(self, value: Any, case_sensitive: bool = True, ignore_whitespace: bool = False) -> Any:
        """Process a value according to options."""
        if isinstance(value, str):
            if ignore_whitespace:
                value = value.strip()
            if not case_sensitive:
                value = value.lower()
        
        return value
    
    def _compare_values(self, val1: Any, val2: Any, operation: str) -> bool:
        """Compare two values using the specified operation."""
        try:
            if operation == 'equal':
                return val1 == val2
            elif operation == 'notEqual':
                return val1 != val2
            elif operation == 'greaterThan':
                return self._to_number(val1) > self._to_number(val2)
            elif operation == 'lessThan':
                return self._to_number(val1) < self._to_number(val2)
            elif operation == 'greaterThanOrEqual':
                return self._to_number(val1) >= self._to_number(val2)
            elif operation == 'lessThanOrEqual':
                return self._to_number(val1) <= self._to_number(val2)
            elif operation == 'contains':
                return str(val2) in str(val1)
            elif operation == 'notContains':
                return str(val2) not in str(val1)
            elif operation == 'startsWith':
                return str(val1).startswith(str(val2))
            elif operation == 'endsWith':
                return str(val1).endswith(str(val2))
            elif operation == 'isEmpty':
                return not val1 or (isinstance(val1, str) and val1.strip() == '')
            elif operation == 'isNotEmpty':
                return bool(val1) and not (isinstance(val1, str) and val1.strip() == '')
            elif operation == 'regex':
                return bool(re.search(str(val2), str(val1)))
            else:
                raise ValueError(f"Unknown operation: {operation}")
                
        except Exception as e:
            raise ValueError(f"Comparison failed for operation '{operation}': {str(e)}")
    
    def _to_number(self, value: Any) -> Union[int, float]:
        """Convert value to number for numeric comparisons."""
        if isinstance(value, (int, float)):
            return value
        
        try:
            # Try int first
            if isinstance(value, str) and '.' not in value:
                return int(value)
            else:
                return float(value)
        except (ValueError, TypeError):
            raise ValueError(f"Cannot convert '{value}' to number")
    
    def _process_expression(self, expression: str) -> str:
        """Process expression for basic evaluation."""
        # Simple preprocessing - replace common patterns
        # This is a basic implementation
        processed = expression

        # Replace common JavaScript-like operators with Python equivalents
        # Handle multi-character operators first
        processed = processed.replace('!==', '!=')
        processed = processed.replace('===', '==')
        processed = processed.replace('&&', ' and ')
        processed = processed.replace('||', ' or ')

        # Use regex to replace standalone ! operators (not part of != or !==)
        # This matches ! that is not followed by =
        import re
        processed = re.sub(r'!(?!=)', ' not ', processed)

        return processed
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """Validate the If node request."""
        # Perform base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        # Add specific validation for If node
        errors = []
        
        if not request.parameters:
            return ValidationResult(valid=False, errors=[ValidationError(parameter="", message="Parameters are required")])
        
        condition_type = request.parameters.get('condition_type', 'simple')
        
        if condition_type == 'simple':
            if not request.parameters.get('value1'):
                errors.append(ValidationError(parameter="value1", message="Value 1 is required for simple conditions"))
            
            operation = request.parameters.get('operation')
            if operation in ['equal', 'notEqual', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 
                           'lessThanOrEqual', 'contains', 'notContains', 'startsWith', 'endsWith', 'regex']:
                if not request.parameters.get('value2'):
                    errors.append(ValidationError(parameter="value2", message=f"Value 2 is required for operation '{operation}'"))
        
        elif condition_type == 'multiple':
            conditions = request.parameters.get('conditions', [])
            if not conditions:
                errors.append(ValidationError(parameter="conditions", message="At least one condition is required for multiple conditions"))
        
        elif condition_type == 'expression':
            if not request.parameters.get('expression'):
                errors.append(ValidationError(parameter="expression", message="Expression is required for expression type"))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
