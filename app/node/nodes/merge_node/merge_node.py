from app.node.node_base.node import Node, Node<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult, ValidationError
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.merge_node.merge_node_model import MergeNodeDescription
import json
import copy
from typing import Any, Dict, List, Union, Optional


@node_defn(type='merge', is_activity=False)
class MergeNode(Node):
    """
    Merge Node for combining data from multiple inputs.
    
    This node merges data from multiple inputs using various merge strategies
    including append, combine by position, combine by fields, combine all,
    and choose branch modes.
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return MergeNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Merge node's data combination logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with merged data
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get mode
            mode = str(self.data.parameters.get('mode', 'append'))
            
            # Parse input data
            input1_data, input2_data = await self._parse_input_data()
            
            # Execute based on mode
            if mode == 'append':
                result = await self._execute_append_mode(input1_data, input2_data)
            elif mode == 'combine':
                result = await self._execute_combine_mode(input1_data, input2_data)
            elif mode == 'chooseBranch':
                result = await self._execute_choose_branch_mode(input1_data, input2_data)
            else:
                return NodeResult(error=f"Unknown mode: {mode}")
            
            return NodeResult(result={"items": result})
            
        except Exception as e:
            return NodeResult(error=f"Merge error: {str(e)}")
    
    async def _parse_input_data(self) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Parse input data from parameters or workflow inputs."""
        input1_data = self.data.parameters.get('input1_data', '')
        input2_data = self.data.parameters.get('input2_data', '')
        
        # Parse input 1
        if input1_data and isinstance(input1_data, str) and input1_data.strip():
            try:
                parsed1 = json.loads(input1_data)
                if isinstance(parsed1, list):
                    input1_items = parsed1
                else:
                    input1_items = [parsed1]
            except json.JSONDecodeError:
                input1_items = []
        else:
            # In a real implementation, this would come from workflow input 1
            input1_items = []
        
        # Parse input 2
        if input2_data and isinstance(input2_data, str) and input2_data.strip():
            try:
                parsed2 = json.loads(input2_data)
                if isinstance(parsed2, list):
                    input2_items = parsed2
                else:
                    input2_items = [parsed2]
            except json.JSONDecodeError:
                input2_items = []
        else:
            # In a real implementation, this would come from workflow input 2
            input2_items = []
        
        return input1_items, input2_items
    
    async def _execute_append_mode(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute append mode - add all items from input 2 to input 1."""
        result = []
        
        # Add all items from input 1
        for item in input1_data:
            processed_item = await self._add_input_info(item, 1)
            result.append(processed_item)
        
        # Add all items from input 2
        for item in input2_data:
            processed_item = await self._add_input_info(item, 2)
            result.append(processed_item)
        
        return result
    
    async def _execute_combine_mode(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute combine mode based on combine strategy."""
        combine_by = str(self.data.parameters.get('combineBy', 'combineByPosition'))
        
        if combine_by == 'combineByPosition':
            return await self._combine_by_position(input1_data, input2_data)
        elif combine_by == 'combineByFields':
            return await self._combine_by_fields(input1_data, input2_data)
        elif combine_by == 'combineAll':
            return await self._combine_all(input1_data, input2_data)
        else:
            raise ValueError(f"Unknown combine strategy: {combine_by}")
    
    async def _execute_choose_branch_mode(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Execute choose branch mode - pass through data from specified input."""
        output = str(self.data.parameters.get('output', 'input1'))
        
        if output == 'input1':
            result = []
            for item in input1_data:
                processed_item = await self._add_input_info(item, 1)
                result.append(processed_item)
            return result
        elif output == 'input2':
            result = []
            for item in input2_data:
                processed_item = await self._add_input_info(item, 2)
                result.append(processed_item)
            return result
        elif output == 'empty':
            return [{}]
        else:
            raise ValueError(f"Unknown output option: {output}")
    
    async def _combine_by_position(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Combine items by their position/index."""
        result = []
        max_length = max(len(input1_data), len(input2_data))
        
        for i in range(max_length):
            item1 = input1_data[i] if i < len(input1_data) else {}
            item2 = input2_data[i] if i < len(input2_data) else {}
            
            merged_item = await self._merge_items(item1, item2)
            result.append(merged_item)
        
        return result
    
    async def _combine_by_fields(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Combine items based on matching field values."""
        fields_to_match = str(self.data.parameters.get('fieldsToMatchOn', 'id'))
        join_mode = str(self.data.parameters.get('joinMode', 'inner'))
        match_fields = [f.strip() for f in fields_to_match.split(',') if f.strip()]
        
        result = []
        
        # Create lookup for input2 items
        input2_lookup = {}
        for item2 in input2_data:
            key = self._create_match_key(item2, match_fields)
            if key not in input2_lookup:
                input2_lookup[key] = []
            input2_lookup[key].append(item2)
        
        # Track which input2 items have been matched
        matched_input2_keys = set()
        
        # Process input1 items
        for item1 in input1_data:
            key = self._create_match_key(item1, match_fields)
            
            if key in input2_lookup:
                # Found matches in input2
                matched_input2_keys.add(key)
                for item2 in input2_lookup[key]:
                    merged_item = await self._merge_items(item1, item2)
                    result.append(merged_item)
            elif join_mode in ['left', 'outer']:
                # No match found, but include item1 for left/outer join
                merged_item = await self._merge_items(item1, {})
                result.append(merged_item)
        
        # For outer join, add unmatched items from input2
        if join_mode == 'outer':
            for key, items in input2_lookup.items():
                if key not in matched_input2_keys:
                    for item2 in items:
                        merged_item = await self._merge_items({}, item2)
                        result.append(merged_item)
        
        return result
    
    async def _combine_all(self, input1_data: List[Dict[str, Any]], input2_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create all possible combinations of items from both inputs."""
        result = []
        
        if not input1_data and not input2_data:
            return result
        
        if not input1_data:
            input1_data = [{}]
        if not input2_data:
            input2_data = [{}]
        
        for item1 in input1_data:
            for item2 in input2_data:
                merged_item = await self._merge_items(item1, item2)
                result.append(merged_item)
        
        return result
    
    async def _merge_items(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> Dict[str, Any]:
        """Merge two items based on clash handling settings."""
        clash_handling = str(self.data.parameters.get('clashHandling', 'preferInput2'))
        merge_arrays = str(self.data.parameters.get('mergeArrays', 'replace'))
        
        if clash_handling == 'preferInput1':
            # Start with item2, then overlay item1
            merged = copy.deepcopy(item2)
            merged.update(item1)
        elif clash_handling == 'addSuffix':
            # Add suffixes to conflicting keys
            merged = {}
            
            # Add all keys from item1 with suffix if needed
            for key, value in item1.items():
                if key in item2:
                    merged[f"{key}_1"] = value
                else:
                    merged[key] = value
            
            # Add all keys from item2 with suffix if needed
            for key, value in item2.items():
                if key in item1:
                    merged[f"{key}_2"] = value
                else:
                    merged[key] = value
        else:  # preferInput2 (default)
            # Start with item1, then overlay item2
            merged = copy.deepcopy(item1)
            for key, value in item2.items():
                if key in merged and isinstance(merged[key], list) and isinstance(value, list):
                    # Handle array merging
                    if merge_arrays == 'concatenate':
                        merged[key] = merged[key] + value
                    elif merge_arrays == 'mergeUnique':
                        merged[key] = list(set(merged[key] + value))
                    else:  # replace
                        merged[key] = value
                else:
                    merged[key] = value
        
        return merged
    
    async def _add_input_info(self, item: Dict[str, Any], input_number: int) -> Dict[str, Any]:
        """Add input information to an item if requested."""
        include_input_info = bool(self.data.parameters.get('includeInputInfo', False))
        
        if include_input_info:
            result = copy.deepcopy(item)
            result['_input'] = input_number
            return result
        
        return copy.deepcopy(item)
    
    def _create_match_key(self, item: Dict[str, Any], match_fields: List[str]) -> str:
        """Create a key for matching items based on specified fields."""
        key_parts = []
        for field in match_fields:
            value = item.get(field, '')
            key_parts.append(str(value))
        return '|'.join(key_parts)
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """Validate the Merge node request."""
        # Perform base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        # Add specific validation for Merge node
        errors = []
        
        if not request.parameters:
            return ValidationResult(valid=False, errors=[ValidationError(parameter="", message="Parameters are required")])
        
        mode = request.parameters.get('mode', 'append')
        
        if mode == 'combine':
            combine_by = request.parameters.get('combineBy', 'combineByPosition')
            
            if combine_by == 'combineByFields':
                fields_to_match = request.parameters.get('fieldsToMatchOn')
                if not fields_to_match or not str(fields_to_match).strip():
                    errors.append(ValidationError(parameter="fieldsToMatchOn", message="Fields to match on is required for combine by fields mode"))
        
        # Validate input data if provided
        for input_param in ['input1_data', 'input2_data']:
            input_data = request.parameters.get(input_param)
            if input_data and isinstance(input_data, str) and input_data.strip():
                try:
                    json.loads(input_data)
                except json.JSONDecodeError:
                    errors.append(ValidationError(parameter=input_param, message=f"Invalid JSON format in {input_param}"))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
