from typing import ClassVar
from app.node.node_base.node_models import (
    NodeConnectionType, 
    NodeParameter, 
    NodeParameterOption, 
    PropertyTypes, 
    NodeTypeDescription,
    DisplayOptions,
    PropertyTypeOptions
)


class MergeNodeDescription(NodeTypeDescription):
    """
    Description for the Merge Node.

    This node merges data from multiple inputs using various merge strategies.
    It supports different merge modes including append, combine by fields,
    combine by position, and combine all data.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "merge"

    @classmethod
    def create(cls) -> "MergeNodeDescription":
        """
        Factory method to create a standard Merge node description.

        Returns:
            MergeNodeDescription: A configured description for Merge nodes
        """
        return cls(
            name="merge",
            display_name="Merge",
            description="Merge data from multiple inputs",
            icon="code-branch",
            icon_color="#FF6B6B",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main, NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            input_names=["Input 1", "Input 2"],
            parameters=[
                NodeParameter(
                    name="mode",
                    display_name="Mode",
                    description="How to merge the inputs",
                    type=PropertyTypes.OPTIONS,
                    default="append",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Append", 
                            value="append",
                            description="Add all items from input 2 to input 1"
                        ),
                        NodeParameterOption(
                            name="Combine", 
                            value="combine",
                            description="Combine items from both inputs"
                        ),
                        NodeParameterOption(
                            name="Choose Branch", 
                            value="chooseBranch",
                            description="Pass through data from a specific input"
                        )
                    ]
                ),
                # Combine mode parameters
                NodeParameter(
                    name="combineBy",
                    display_name="Combine By",
                    description="How to combine the data from both inputs",
                    type=PropertyTypes.OPTIONS,
                    default="combineByPosition",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["combine"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Combine by Position", 
                            value="combineByPosition",
                            description="Combine items based on their position/index"
                        ),
                        NodeParameterOption(
                            name="Combine by Fields", 
                            value="combineByFields",
                            description="Combine items that have matching field values"
                        ),
                        NodeParameterOption(
                            name="Combine All", 
                            value="combineAll",
                            description="Create all possible combinations of items"
                        )
                    ]
                ),
                # Combine by fields parameters
                NodeParameter(
                    name="joinMode",
                    display_name="Join Mode",
                    description="How to join the data",
                    type=PropertyTypes.OPTIONS,
                    default="inner",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["combine"],
                            "combineBy": ["combineByFields"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Inner Join", 
                            value="inner",
                            description="Only include items that have matches in both inputs"
                        ),
                        NodeParameterOption(
                            name="Left Join", 
                            value="left",
                            description="Include all items from input 1, with matches from input 2"
                        ),
                        NodeParameterOption(
                            name="Outer Join", 
                            value="outer",
                            description="Include all items from both inputs"
                        )
                    ]
                ),
                NodeParameter(
                    name="fieldsToMatchOn",
                    display_name="Fields to Match On",
                    description="The fields to use for matching items between inputs",
                    type=PropertyTypes.STRING,
                    default="id",
                    required=True,
                    placeholder="e.g. id,name",
                    display_options=DisplayOptions(
                        show={
                            "mode": ["combine"],
                            "combineBy": ["combineByFields"]
                        }
                    )
                ),
                # Choose branch parameters
                NodeParameter(
                    name="output",
                    display_name="Output",
                    description="What data to output",
                    type=PropertyTypes.OPTIONS,
                    default="input1",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["chooseBranch"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Input 1", 
                            value="input1",
                            description="Pass through data from input 1"
                        ),
                        NodeParameterOption(
                            name="Input 2", 
                            value="input2",
                            description="Pass through data from input 2"
                        ),
                        NodeParameterOption(
                            name="Empty Item", 
                            value="empty",
                            description="Output a single empty item"
                        )
                    ]
                ),
                # Common options
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional options for merging",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Clash Handling",
                            value="clashHandling",
                            description="How to handle field name conflicts"
                        ),
                        NodeParameterOption(
                            name="Include Input Info",
                            value="includeInputInfo",
                            description="Include information about which input each item came from"
                        ),
                        NodeParameterOption(
                            name="Merge Arrays",
                            value="mergeArrays",
                            description="How to handle array fields when merging"
                        )
                    ]
                ),
                NodeParameter(
                    name="clashHandling",
                    display_name="Clash Handling",
                    description="How to handle conflicts when both inputs have the same field name",
                    type=PropertyTypes.OPTIONS,
                    default="preferInput2",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["clashHandling"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Prefer Input 1", 
                            value="preferInput1",
                            description="Keep values from input 1 when there are conflicts"
                        ),
                        NodeParameterOption(
                            name="Prefer Input 2", 
                            value="preferInput2",
                            description="Keep values from input 2 when there are conflicts"
                        ),
                        NodeParameterOption(
                            name="Add Suffix", 
                            value="addSuffix",
                            description="Add suffixes to conflicting field names"
                        )
                    ]
                ),
                NodeParameter(
                    name="includeInputInfo",
                    display_name="Include Input Info",
                    description="Add metadata about which input each item originated from",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["includeInputInfo"]
                        }
                    )
                ),
                NodeParameter(
                    name="mergeArrays",
                    display_name="Merge Arrays",
                    description="How to handle array fields when merging objects",
                    type=PropertyTypes.OPTIONS,
                    default="replace",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["mergeArrays"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Replace", 
                            value="replace",
                            description="Replace arrays from input 1 with arrays from input 2"
                        ),
                        NodeParameterOption(
                            name="Concatenate", 
                            value="concatenate",
                            description="Combine arrays by concatenating them"
                        ),
                        NodeParameterOption(
                            name="Merge Unique", 
                            value="mergeUnique",
                            description="Combine arrays and remove duplicates"
                        )
                    ]
                ),
                # Input data parameters for testing
                NodeParameter(
                    name="input1_data",
                    display_name="Input 1 Data",
                    description="Test data for input 1 (JSON array format). Leave empty to use workflow data.",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False
                ),
                NodeParameter(
                    name="input2_data",
                    display_name="Input 2 Data",
                    description="Test data for input 2 (JSON array format). Leave empty to use workflow data.",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False
                )
            ]
        )
