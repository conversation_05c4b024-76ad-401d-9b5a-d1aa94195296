"""
Merge Node Module

This module provides data merging capabilities for the Cerebro workflow system.
The Merge node can combine data from multiple inputs using various merge strategies
including append, combine by position, combine by fields, and combine all.
"""

from .merge_node import MergeNode
from .merge_node_model import MergeNodeDescription

__all__ = [
    'MergeNode',
    'MergeNodeDescription'
]
