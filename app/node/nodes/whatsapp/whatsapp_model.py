"""
WhatsApp Node Model

This module defines the WhatsApp node model with parameter definitions,
credentials, and configuration following the project's established patterns.
"""

from typing import ClassVar
from app.node import (
    NodeConnectionType,
    NodeCredentialDescription,
    NodeParameter,
    NodeParameterOption,
    PropertyTypes,
    NodeTypeDescription,
    DisplayOptions
)
from app.node.node_base.node_models import LoadOptions, PropertyTypeOptions


class WhatsAppNodeDescription(NodeTypeDescription):
    """
    Description for the WhatsApp Business Cloud Node.
    
    This node provides integration with WhatsApp Business Cloud API for sending
    messages, media, and templates to WhatsApp users.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "whatsapp"
    
    @classmethod
    def create(cls) -> "WhatsAppNodeDescription":
        """
        Factory method to create a standard WhatsApp node description.
        
        Returns:
            WhatsAppNodeDescription: A configured description for WhatsApp nodes
        """
        return cls(
            name="whatsapp",
            display_name="WhatsApp Business",
            description="Send messages, media, and templates via WhatsApp Business API",
            icon="📱",
            icon_color="#25D366",
            group=["output"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            credentials = [
                NodeCredentialDescription(
                    name="whatsapp_api",
                    display_name="WhatsApp",
                    required=True
                )
            ],
            parameters=[
                NodeParameter(
                    name="resource",
                    display_name="Resource",
                    description="The resource to operate on",
                    type=PropertyTypes.OPTIONS,
                    default="message",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Message",
                            value="message",
                            description="Send messages to WhatsApp users"
                        ),
                        NodeParameterOption(
                            name="Media",
                            value="media",
                            description="Send media files to WhatsApp users"
                        )
                    ]
                ),
                
                # Operation selection for messages
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="The operation to perform",
                    type=PropertyTypes.OPTIONS,
                    default="send",
                    required=True,
                    display_options=DisplayOptions(
                        show={"resource": ["message"]}
                    ),
                    options=[
                        NodeParameterOption(
                            name="Send Message",
                            value="send",
                            description="Send a text message"
                        ),
                        NodeParameterOption(
                            name="Send Template",
                            value="sendTemplate",
                            description="Send a template message"
                        )
                    ]
                ),
                
                # Operation selection for media
                NodeParameter(
                    name="mediaOperation",
                    display_name="Operation",
                    description="The media operation to perform",
                    type=PropertyTypes.OPTIONS,
                    default="send",
                    required=True,
                    display_options=DisplayOptions(
                        show={"resource": ["media"]}
                    ),
                    options=[
                        NodeParameterOption(
                            name="Send Media",
                            value="send",
                            description="Send media file"
                        ),
                        NodeParameterOption(
                            name="Upload Media",
                            value="upload",
                            description="Upload media file"
                        ),
                        NodeParameterOption(
                            name="Get Media URL",
                            value="getUrl",
                            description="Get media download URL"
                        )
                    ]
                ),
                
                # Phone Number ID (required for all operations)
                NodeParameter(
                    name="phoneNumberId",
                    display_name="Phone Number ID",
                    description="The WhatsApp Business phone number ID",
                    type=PropertyTypes.OPTIONS,
                    depends_on=["credentials"],
                    type_options=PropertyTypeOptions(
                        load_options_depends_on=["credentials"], # TODO credentials.whatsapp_api, check with frontend
                        load_options=LoadOptions(function="get_phone_numbers")
                    ),
                    required=True,
                    placeholder="1234567890123456"
                ),
                
                # Recipient phone number (for sending operations)
                NodeParameter(
                    name="recipientPhoneNumber",
                    display_name="Recipient Phone Number",
                    description="The recipient's phone number (with country code, no + sign)",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="1234567890",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message", "media"],
                            "operation": ["send"],
                            "mediaOperation": ["send"]
                        }
                    )
                ),
                
                # Message type selection
                NodeParameter(
                    name="messageType",
                    display_name="Message Type",
                    description="The type of message to send",
                    type=PropertyTypes.OPTIONS,
                    default="text",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"]
                        }
                    ),
                    options=[
                        NodeParameterOption(name="Text", value="text"),
                        NodeParameterOption(name="Image", value="image"),
                        NodeParameterOption(name="Document", value="document"),
                        NodeParameterOption(name="Audio", value="audio"),
                        NodeParameterOption(name="Video", value="video"),
                        NodeParameterOption(name="Sticker", value="sticker"),
                        NodeParameterOption(name="Location", value="location"),
                        NodeParameterOption(name="Contacts", value="contacts")
                    ]
                ),
                
                # Text message content
                NodeParameter(
                    name="message",
                    display_name="Message",
                    description="The text message to send",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="Hello from WhatsApp!",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["text"]
                        }
                    )
                ),
                
                # Template name
                NodeParameter(
                    name="template",
                    display_name="Template",
                    description="Template name and language (format: template_name|language_code)",
                    type=PropertyTypes.OPTIONS,
                    type_options=PropertyTypeOptions(
                        load_options_depends_on=["credentials"],
                        load_options=LoadOptions(function="get_all_templates")
                    ),
                    required=True,
                    placeholder="hello_world|en_US",

                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["sendTemplate"]
                        }
                    )
                ),
                
                # Media URL for media messages
                NodeParameter(
                    name="mediaUrl",
                    display_name="Media URL",
                    description="URL of the media file to send",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="https://example.com/image.jpg",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["image", "document", "audio", "video", "sticker"]
                        }
                    )
                ),
                
                # Caption for media
                NodeParameter(
                    name="caption",
                    display_name="Caption",
                    description="Caption for the media file",
                    type=PropertyTypes.STRING,
                    required=False,
                    placeholder="Media caption",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["image", "document", "video"]
                        }
                    )
                ),
                
                # Filename for documents
                NodeParameter(
                    name="filename",
                    display_name="Filename",
                    description="Filename for the document",
                    type=PropertyTypes.STRING,
                    required=False,
                    placeholder="document.pdf",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["document"]
                        }
                    )
                ),

                # Location parameters
                NodeParameter(
                    name="latitude",
                    display_name="Latitude",
                    description="Latitude coordinate for location message",
                    type=PropertyTypes.NUMBER,
                    required=True,
                    placeholder="37.7749",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["location"]
                        }
                    )
                ),
                NodeParameter(
                    name="longitude",
                    display_name="Longitude",
                    description="Longitude coordinate for location message",
                    type=PropertyTypes.NUMBER,
                    required=True,
                    placeholder="-122.4194",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["location"]
                        }
                    )
                ),
                NodeParameter(
                    name="locationName",
                    display_name="Location Name",
                    description="Name of the location",
                    type=PropertyTypes.STRING,
                    required=False,
                    placeholder="San Francisco",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["location"]
                        }
                    )
                ),
                NodeParameter(
                    name="locationAddress",
                    display_name="Location Address",
                    description="Address of the location",
                    type=PropertyTypes.STRING,
                    required=False,
                    placeholder="123 Main St, San Francisco, CA",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["send"],
                            "messageType": ["location"]
                        }
                    )
                ),

                # Template parameters
                NodeParameter(
                    name="templateParameters",
                    display_name="Template Parameters",
                    description="Parameters for template message (JSON format)",
                    type=PropertyTypes.COMPONENT,
                    type_options=PropertyTypeOptions(
                        load_options_depends_on=["credentials", "template"],
                        load_options=LoadOptions(function="get_template"),
                        # json_schema={
                        #     "type": "object",
                        #     "properties": {
                        #         "template_name": {"type": "string"},
                        #         "language": {"type": "string"}
                        #     },
                        #     "required": ["template_name", "language"]
                        # }
                    ),
                    required=False,
                    default={},
                    display_options=DisplayOptions(
                        show={
                            "resource": ["message"],
                            "operation": ["sendTemplate"]
                        }
                    )
                ),

                # Media ID for uploaded media
                NodeParameter(
                    name="mediaId",
                    display_name="Media ID",
                    description="ID of previously uploaded media",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="1234567890",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["media"],
                            "mediaOperation": ["getUrl"]
                        }
                    )
                ),

                # Media file for upload
                NodeParameter(
                    name="mediaFile",
                    display_name="Media File",
                    description="Media file to upload (base64 encoded or file path)",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="/path/to/file.jpg or base64_encoded_data",
                    display_options=DisplayOptions(
                        show={
                            "resource": ["media"],
                            "mediaOperation": ["upload"]
                        }
                    )
                ),

                # Media type for upload
                NodeParameter(
                    name="mediaType",
                    display_name="Media Type",
                    description="Type of media being uploaded",
                    type=PropertyTypes.OPTIONS,
                    default="image",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "resource": ["media"],
                            "mediaOperation": ["upload"]
                        }
                    ),
                    options=[
                        NodeParameterOption(name="Image", value="image"),
                        NodeParameterOption(name="Document", value="document"),
                        NodeParameterOption(name="Audio", value="audio"),
                        NodeParameterOption(name="Video", value="video"),
                        NodeParameterOption(name="Sticker", value="sticker")
                    ]
                )
            ]
        )
