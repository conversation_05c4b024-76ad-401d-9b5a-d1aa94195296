from typing import ClassVar
from app.node.node_base.node_models import (
    NodeConnectionType, 
    NodeParameter, 
    NodeParameterOption, 
    PropertyTypes, 
    NodeTypeDescription,
    DisplayOptions,
    PropertyTypeOptions
)


class EditFieldNodeDescription(NodeTypeDescription):
    """
    Description for the Edit Field (Set) Node.

    This node modifies, adds, or removes item fields using either manual field
    configuration or raw JSON manipulation. It supports dot notation for nested
    field access and various data type conversions.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "edit_field"

    @classmethod
    def create(cls) -> "EditFieldNodeDescription":
        """
        Factory method to create a standard Edit Field node description.

        Returns:
            EditFieldNodeDescription: A configured description for Edit Field nodes
        """
        return cls(
            name="edit_field",
            display_name="Edit Fields (Set)",
            description="Modify, add, or remove item fields",
            icon="edit",
            icon_color="#0066CC",
            group=["input"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="mode",
                    display_name="Mode",
                    description="How to edit the fields",
                    type=PropertyTypes.OPTIONS,
                    default="manual",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Manual Mapping", 
                            value="manual",
                            description="Edit item fields one by one",
                            action="Edit item fields one by one"
                        ),
                        NodeParameterOption(
                            name="JSON", 
                            value="raw",
                            description="Customize item output with JSON",
                            action="Customize item output with JSON"
                        )
                    ]
                ),
                # Manual mode parameters
                NodeParameter(
                    name="fields",
                    display_name="Fields to Set",
                    description="Edit existing fields or add new ones to modify the output data",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="Field Name",
                            value="name",
                            description="Name of the field to set (supports dot notation)"
                        ),
                        NodeParameterOption(
                            name="Field Type",
                            value="type",
                            description="The data type of the field value"
                        ),
                        NodeParameterOption(
                            name="Field Value",
                            value="value",
                            description="The value to set for this field"
                        )
                    ]
                ),
                # Individual field parameters for manual mode
                NodeParameter(
                    name="field_name",
                    display_name="Field Name",
                    description="Name of the field to set the value of. Supports dot-notation. Example: data.person[0].name",
                    type=PropertyTypes.STRING,
                    default="",
                    required=True,
                    placeholder="e.g. fieldName",
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"]
                        }
                    )
                ),
                NodeParameter(
                    name="field_type",
                    display_name="Field Type",
                    description="The field value type",
                    type=PropertyTypes.OPTIONS,
                    default="string",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="String", 
                            value="string",
                            description="Text value"
                        ),
                        NodeParameterOption(
                            name="Number", 
                            value="number",
                            description="Numeric value"
                        ),
                        NodeParameterOption(
                            name="Boolean", 
                            value="boolean",
                            description="True/false value"
                        ),
                        NodeParameterOption(
                            name="Array", 
                            value="array",
                            description="List of values"
                        ),
                        NodeParameterOption(
                            name="Object", 
                            value="object",
                            description="JSON object"
                        )
                    ]
                ),
                NodeParameter(
                    name="string_value",
                    display_name="String Value",
                    description="The string value to set",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"],
                            "field_type": ["string"]
                        }
                    )
                ),
                NodeParameter(
                    name="number_value",
                    display_name="Number Value",
                    description="The numeric value to set",
                    type=PropertyTypes.NUMBER,
                    default=0,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"],
                            "field_type": ["number"]
                        }
                    )
                ),
                NodeParameter(
                    name="boolean_value",
                    display_name="Boolean Value",
                    description="The boolean value to set",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"],
                            "field_type": ["boolean"]
                        }
                    )
                ),
                NodeParameter(
                    name="array_value",
                    display_name="Array Value",
                    description="The array value to set (JSON format)",
                    type=PropertyTypes.STRING,
                    default="[]",
                    required=False,
                    placeholder="e.g. [\"item1\", \"item2\", \"item3\"]",
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"],
                            "field_type": ["array"]
                        }
                    )
                ),
                NodeParameter(
                    name="object_value",
                    display_name="Object Value",
                    description="The object value to set (JSON format)",
                    type=PropertyTypes.JSON,
                    default="{}",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["manual"],
                            "field_type": ["object"]
                        }
                    )
                ),
                # Raw mode parameters
                NodeParameter(
                    name="json_output",
                    display_name="JSON",
                    description="JSON object defining the output structure",
                    type=PropertyTypes.JSON,
                    default='{\n  "my_field_1": "value",\n  "my_field_2": 1\n}',
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["raw"]
                        }
                    )
                ),
                # Common options
                NodeParameter(
                    name="include_other_fields",
                    display_name="Include Other Input Fields",
                    description="Whether to pass to the output all the input fields (along with the fields set)",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False
                ),
                NodeParameter(
                    name="include_mode",
                    display_name="Input Fields to Include",
                    description="How to select the fields you want to include in your output items",
                    type=PropertyTypes.OPTIONS,
                    default="all",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "include_other_fields": [True]
                        }
                    ),
                    options=[
                        NodeParameterOption(
                            name="All", 
                            value="all",
                            description="Include all unchanged fields from the input"
                        ),
                        NodeParameterOption(
                            name="Selected", 
                            value="selected",
                            description="Include only the fields listed in 'Fields to Include'"
                        ),
                        NodeParameterOption(
                            name="All Except", 
                            value="except",
                            description="Exclude the fields listed in 'Fields to Exclude'"
                        )
                    ]
                ),
                NodeParameter(
                    name="include_fields",
                    display_name="Fields to Include",
                    description="Comma-separated list of field names to include in the output",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    placeholder="e.g. field1,field2,field3",
                    display_options=DisplayOptions(
                        show={
                            "include_other_fields": [True],
                            "include_mode": ["selected"]
                        }
                    )
                ),
                NodeParameter(
                    name="exclude_fields",
                    display_name="Fields to Exclude",
                    description="Comma-separated list of field names to exclude from the output",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    placeholder="e.g. field1,field2,field3",
                    display_options=DisplayOptions(
                        show={
                            "include_other_fields": [True],
                            "include_mode": ["except"]
                        }
                    )
                ),
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional options for field editing",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    options=[
                        NodeParameterOption(
                            name="Support Dot Notation",
                            value="dot_notation",
                            description="Enable dot notation for nested field access"
                        ),
                        NodeParameterOption(
                            name="Ignore Type Conversion Errors",
                            value="ignore_conversion_errors",
                            description="Continue processing if type conversion fails"
                        ),
                        NodeParameterOption(
                            name="Remove Empty Fields",
                            value="remove_empty_fields",
                            description="Remove fields with null or empty values"
                        )
                    ]
                ),
                NodeParameter(
                    name="dot_notation",
                    display_name="Support Dot Notation",
                    description="By default, dot-notation is used in property names. This means that 'a.b' will set the property 'b' underneath 'a' so { 'a': { 'b': value} }. If disabled, it will set { 'a.b': value } instead.",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["dot_notation"]
                        }
                    )
                ),
                NodeParameter(
                    name="ignore_conversion_errors",
                    display_name="Ignore Type Conversion Errors",
                    description="Whether to ignore field type errors and apply a less strict type conversion",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["ignore_conversion_errors"]
                        }
                    )
                ),
                NodeParameter(
                    name="remove_empty_fields",
                    display_name="Remove Empty Fields",
                    description="Whether to remove fields with null, undefined, or empty string values",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "options": ["remove_empty_fields"]
                        }
                    )
                )
            ]
        )
