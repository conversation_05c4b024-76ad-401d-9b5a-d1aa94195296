from app.node.node_base.node import Node, <PERSON>de<PERSON><PERSON>ult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult, ValidationError
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.edit_field_node.edit_field_model import EditFieldNodeDescription
import json
import copy
import re
from typing import Any, Dict, List, Union, Optional


@node_defn(type='edit_field', is_activity=False)
class EditFieldNode(Node):
    """
    Edit Field (Set) Node for field manipulation in workflows.
    
    This node modifies, adds, or removes item fields using either manual field
    configuration or raw JSON manipulation. It supports dot notation for nested
    field access and various data type conversions.
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return EditFieldNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Edit Field node's field manipulation logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with modified field data
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get mode
            mode = str(self.data.parameters.get('mode', 'manual'))
            
            # Get input data (in a real implementation, this would come from workflow)
            input_data = self.data.parameters.get('input_data', {})
            if isinstance(input_data, str) and input_data.strip():
                try:
                    input_data = json.loads(input_data)
                except json.JSONDecodeError:
                    input_data = {}
            elif not input_data:
                input_data = {}
            
            # Ensure input_data is a dictionary before proceeding
            if not isinstance(input_data, dict):
                input_data = {}
            
            # Process based on mode
            if mode == 'manual':
                result = await self._process_manual_mode(input_data)
            elif mode == 'raw':
                result = await self._process_raw_mode(input_data)
            else:
                return NodeResult(error=f"Unknown mode: {mode}")
            
            return NodeResult(result=result)
            
        except Exception as e:
            return NodeResult(error=f"Field editing error: {str(e)}")
    
    async def _process_manual_mode(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process manual field configuration mode."""
        # Get field configuration
        field_name = str(self.data.parameters.get('field_name', ''))
        field_type = str(self.data.parameters.get('field_type', 'string'))

        if not field_name:
            raise ValueError("Field name is required in manual mode")

        # Get the value based on type
        field_value = await self._get_typed_value(field_type)

        # Create new data with just the new field
        new_data = await self._set_field_value({}, field_name, field_value)

        # Handle field inclusion/exclusion
        result = await self._compose_return_item(input_data, new_data)

        return result
    
    async def _process_raw_mode(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process raw JSON mode."""
        json_output = str(self.data.parameters.get('json_output', '{}'))
        
        try:
            # Parse the JSON output
            new_data = json.loads(json_output)
            if not isinstance(new_data, dict):
                raise ValueError("JSON output must be an object")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON output: {str(e)}")
        
        # Handle field inclusion/exclusion
        result = await self._compose_return_item(input_data, new_data)
        
        return result
    
    async def _get_typed_value(self, field_type: str) -> Any:
        """Get the value with proper type conversion."""
        ignore_errors = bool(self.data.parameters.get('ignore_conversion_errors', False))
        
        try:
            if field_type == 'string':
                return str(self.data.parameters.get('string_value', ''))
            elif field_type == 'number':
                value = self.data.parameters.get('number_value', 0)
                return float(value) if isinstance(value, (int, float, str)) else 0
            elif field_type == 'boolean':
                return bool(self.data.parameters.get('boolean_value', True))
            elif field_type == 'array':
                array_str = str(self.data.parameters.get('array_value', '[]'))
                try:
                    parsed = json.loads(array_str)
                    return parsed if isinstance(parsed, list) else [parsed]
                except json.JSONDecodeError:
                    if ignore_errors:
                        return []
                    raise ValueError(f"Invalid array JSON: {array_str}")
            elif field_type == 'object':
                object_str = str(self.data.parameters.get('object_value', '{}'))
                try:
                    parsed = json.loads(object_str)
                    return parsed if isinstance(parsed, dict) else {}
                except json.JSONDecodeError:
                    if ignore_errors:
                        return {}
                    raise ValueError(f"Invalid object JSON: {object_str}")
            else:
                raise ValueError(f"Unknown field type: {field_type}")
        except Exception as e:
            if ignore_errors:
                return None
            raise e
    
    async def _set_field_value(self, data: Dict[str, Any], field_path: str, value: Any) -> Dict[str, Any]:
        """Set a field value using dot notation if enabled."""
        new_data = copy.deepcopy(data)
        dot_notation = bool(self.data.parameters.get('dot_notation', True))
        
        if not dot_notation or '.' not in field_path:
            # Simple field assignment
            new_data[field_path] = value
            return new_data
        
        # Handle dot notation
        parts = field_path.split('.')
        current = new_data
        
        # Navigate to the parent of the target field
        for part in parts[:-1]:
            # Handle array indices like "items[0]"
            if '[' in part and ']' in part:
                array_name, index_part = part.split('[', 1)
                index = int(index_part.rstrip(']'))
                
                if array_name not in current:
                    current[array_name] = []
                
                # Ensure the array is long enough
                while len(current[array_name]) <= index:
                    current[array_name].append({})
                
                current = current[array_name][index]
            else:
                if part not in current:
                    current[part] = {}
                current = current[part]
        
        # Set the final value
        final_key = parts[-1]
        if '[' in final_key and ']' in final_key:
            array_name, index_part = final_key.split('[', 1)
            index = int(index_part.rstrip(']'))
            
            if array_name not in current:
                current[array_name] = []
            
            # Ensure the array is long enough
            while len(current[array_name]) <= index:
                current[array_name].append(None)
            
            current[array_name][index] = value
        else:
            current[final_key] = value
        
        return new_data
    
    async def _compose_return_item(self, input_data: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Any]:
        """Compose the return item based on inclusion/exclusion settings."""
        include_other_fields = bool(self.data.parameters.get('include_other_fields', False))

        if not include_other_fields:
            # Return only the new data
            result = new_data
        else:
            # Handle field inclusion/exclusion
            include_mode = str(self.data.parameters.get('include_mode', 'all'))

            if include_mode == 'selected':
                include_fields = str(self.data.parameters.get('include_fields', ''))
                if include_fields:
                    fields_to_include = [f.strip() for f in include_fields.split(',') if f.strip()]
                    # Keep only specified fields from input data, plus new data
                    filtered_input = {k: v for k, v in input_data.items() if k in fields_to_include}
                    result = copy.deepcopy(filtered_input)
                    result.update(new_data)
                else:
                    # No fields specified, just return new data
                    result = copy.deepcopy(new_data)

            elif include_mode == 'except':
                exclude_fields = str(self.data.parameters.get('exclude_fields', ''))
                # Start with all input data plus new data
                result = copy.deepcopy(input_data)
                result.update(new_data)

                if exclude_fields:
                    fields_to_exclude = [f.strip() for f in exclude_fields.split(',') if f.strip()]
                    # Remove specified fields from result
                    for field in fields_to_exclude:
                        result.pop(field, None)

            else:  # include_mode == 'all'
                # Merge input data with new data
                result = copy.deepcopy(input_data)
                result.update(new_data)

        # Handle empty field removal
        remove_empty = bool(self.data.parameters.get('remove_empty_fields', False))
        if remove_empty:
            result = self._remove_empty_fields(result)

        return result
    
    def _remove_empty_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove fields with empty values."""
        cleaned = {}
        for key, value in data.items():
            if value is not None and value != '' and value != []:
                if isinstance(value, dict):
                    cleaned_nested = self._remove_empty_fields(value)
                    if cleaned_nested:  # Only include if not empty after cleaning
                        cleaned[key] = cleaned_nested
                else:
                    cleaned[key] = value
        return cleaned
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """Validate the Edit Field node request."""
        # Perform base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        # Add specific validation for Edit Field node
        errors = []
        
        if not request.parameters:
            return ValidationResult(valid=False, errors=[ValidationError(parameter="", message="Parameters are required")])
        
        mode = request.parameters.get('mode', 'manual')
        
        if mode == 'manual':
            # Validate manual mode parameters
            field_name = request.parameters.get('field_name')
            if not field_name or not str(field_name).strip():
                errors.append(ValidationError(parameter="field_name", message="Field name is required in manual mode"))
            
            field_type = request.parameters.get('field_type', 'string')
            if field_type not in ['string', 'number', 'boolean', 'array', 'object']:
                errors.append(ValidationError(parameter="field_type", message="Invalid field type"))
            
            # Validate type-specific values
            if field_type == 'array':
                array_value = request.parameters.get('array_value', '[]')
                if isinstance(array_value, str):
                    try:
                        json.loads(array_value)
                    except json.JSONDecodeError:
                        errors.append(ValidationError(parameter="array_value", message="Invalid JSON format for array value"))
            
            elif field_type == 'object':
                object_value = request.parameters.get('object_value', '{}')
                if isinstance(object_value, str):
                    try:
                        json.loads(object_value)
                    except json.JSONDecodeError:
                        errors.append(ValidationError(parameter="object_value", message="Invalid JSON format for object value"))
        
        elif mode == 'raw':
            # Validate raw mode parameters
            json_output = request.parameters.get('json_output')
            if not json_output:
                errors.append(ValidationError(parameter="json_output", message="JSON output is required in raw mode"))
            else:
                try:
                    parsed = json.loads(str(json_output))
                    if not isinstance(parsed, dict):
                        errors.append(ValidationError(parameter="json_output", message="JSON output must be an object"))
                except json.JSONDecodeError:
                    errors.append(ValidationError(parameter="json_output", message="Invalid JSON format"))
        
        else:
            errors.append(ValidationError(parameter="mode", message="Invalid mode. Must be 'manual' or 'raw'"))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
