"""
Sort Node Model

This module defines the model for the sort node, which provides data sorting
functionality for workflow items with support for multiple sort criteria,
different data types, and various sorting modes.
"""

from typing import ClassVar

from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    NodeParameterOption,
    PropertyTypes,
    NodeConnectionType,
    DisplayOptions,
    PropertyTypeOptions
)


class SortNodeDescription(NodeTypeDescription):
    """
    Description for the Sort Node.
    
    This node sorts workflow items based on configurable criteria including
    field-based sorting, random shuffling, and custom sorting logic.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "sort"

    @classmethod
    def create(cls) -> "SortNodeDescription":
        """
        Factory method to create a standard Sort node description.
        
        Returns:
            SortNodeDescription: A configured description for sort nodes
        """
        return cls(
            name="sort",
            display_name="Sort",
            description="Sort workflow items by specified criteria or randomly shuffle them.",
            icon="sort-amount-down",
            icon_color="#4A90E2",
            group=["transform"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="sort_type",
                    display_name="Sort Type",
                    description="The type of sorting to perform",
                    type=PropertyTypes.OPTIONS,
                    default="simple",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Simple Field Sort", 
                            value="simple", 
                            description="Sort by one or more fields with ascending/descending order"
                        ),
                        NodeParameterOption(
                            name="Random Shuffle", 
                            value="random", 
                            description="Randomly shuffle the items"
                        ),
                        NodeParameterOption(
                            name="Custom Expression", 
                            value="expression", 
                            description="Sort using a custom comparison expression"
                        )
                    ]
                ),
                NodeParameter(
                    name="sort_fields",
                    display_name="Sort Fields",
                    description="Fields to sort by in order of priority",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    display_options=DisplayOptions(
                        show={"sort_type": ["simple"]}
                    ),
                    options=[
                        NodeParameterOption(
                            name="Field Name",
                            value="field_name",
                            description="Name of the field to sort by (supports dot notation for nested fields)"
                        ),
                        NodeParameterOption(
                            name="Sort Order",
                            value="sort_order",
                            description="Whether to sort in ascending or descending order"
                        ),
                        NodeParameterOption(
                            name="Data Type",
                            value="data_type",
                            description="How to interpret the field values for sorting"
                        )
                    ]
                ),
                NodeParameter(
                    name="expression",
                    display_name="Sort Expression",
                    description="Custom expression for comparing two items (a and b). Return -1 if a < b, 1 if a > b, 0 if equal",
                    type=PropertyTypes.STRING,
                    default="# Compare two items a and b\n# Return -1 if a should come before b\n# Return 1 if b should come before a\n# Return 0 if they are equal\n\nfield_name = 'id'\nif a.get(field_name, 0) < b.get(field_name, 0):\n    return -1\nelif a.get(field_name, 0) > b.get(field_name, 0):\n    return 1\nelse:\n    return 0",
                    required=False,
                    display_options=DisplayOptions(
                        show={"sort_type": ["expression"]}
                    ),
                    type_options=PropertyTypeOptions(
                        editor="code",
                        editor_language="python"
                    )
                ),
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional sorting options",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=False,
                    display_options=DisplayOptions(
                        show={"sort_type": ["simple"]}
                    ),
                    options=[
                        NodeParameterOption(
                            name="Case Sensitive",
                            value="case_sensitive",
                            description="Whether string comparisons should be case sensitive"
                        ),
                        NodeParameterOption(
                            name="Null Handling",
                            value="null_handling",
                            description="How to handle null/undefined values during sorting"
                        ),
                        NodeParameterOption(
                            name="Stable Sort",
                            value="stable_sort",
                            description="Whether to preserve the relative order of equal elements"
                        )
                    ]
                )
            ]
        )
