"""
Sort Node Implementation

This module implements the sort node functionality for sorting workflow items
based on various criteria including field-based sorting, random shuffling,
and custom expressions.
"""

import random
import json
from typing import Any, Dict, List
from functools import cmp_to_key

from app.node.node_base.node import Node, NodeResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node_models import (
    NodeData, 
    NodeTypeDescription, 
    NodeRequest, 
    ValidationResult,
    ValidationError
)
from app.node.nodes.sort.sort_model import SortNodeDescription


@node_defn(type='sort', is_activity=False)
class SortNode(Node):
    """
    Node that sorts workflow items based on configurable criteria.
    
    This node supports:
    - Simple field-based sorting with multiple sort criteria
    - Random shuffling of items
    - Custom expression-based sorting
    - Various data type handling (string, number, date, boolean)
    - Null value handling options
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """
        Get the description for this node type.
        
        Returns:
            NodeTypeDescription: The node type description
        """
        return SortNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the sort node's logic by sorting the input data.
        
        Args:
            data: Node execution data containing parameters and input data
        
        Returns:
            NodeResult: The sorted data or error information
        
        Raises:
            ValueError: If parameters are missing or invalid
        """
        self.data = data
        if not self.data or not self.data.parameters:
            raise ValueError("Node data or parameters not set")

        try:
            # Get sort type
            sort_type = str(self.data.parameters.get("sort_type", "simple"))
            
            # Get input data - in a real workflow, this would come from the previous node
            input_data = self._get_input_data()
            
            if not isinstance(input_data, list):
                return NodeResult(error="Input data must be a list of items")
            
            if len(input_data) == 0:
                return NodeResult(result=input_data, next_connection_index=0, error=None)
            
            # Execute sorting based on type
            if sort_type == "simple":
                sorted_data = await self._sort_by_fields(input_data)
            elif sort_type == "random":
                sorted_data = await self._sort_random(input_data)
            elif sort_type == "expression":
                sorted_data = await self._sort_by_expression(input_data)
            else:
                return NodeResult(error=f"Unknown sort type: {sort_type}")
            
            return NodeResult(result=sorted_data, next_connection_index=0, error=None)
            
        except Exception as e:
            return NodeResult(error=f"Sort error: {str(e)}")
    
    def _get_input_data(self) -> List[Dict[str, Any]]:
        """
        Get input data from node parameters.
        In a real workflow, this would come from the previous node's output.
        """
        input_data = self.data.parameters.get('input_data', [])
        
        # Handle string JSON input
        if isinstance(input_data, str) and input_data.strip():
            try:
                input_data = json.loads(input_data)
            except json.JSONDecodeError:
                input_data = []
        
        # Ensure it's a list
        if not isinstance(input_data, list):
            input_data = []
            
        return input_data
    
    async def _sort_by_fields(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Sort items by specified fields with configurable order and data types.
        """
        sort_fields = self.data.parameters.get('sort_fields', [])
        
        if not sort_fields:
            raise ValueError("No sort fields specified for simple sort mode")
        
        # Parse sort fields configuration
        sort_criteria = []
        for field_config in sort_fields:
            if isinstance(field_config, dict):
                field_name = field_config.get('field_name', '')
                sort_order = field_config.get('sort_order', 'ascending')
                data_type = field_config.get('data_type', 'auto')
            else:
                # Handle simple string field names
                field_name = str(field_config)
                sort_order = 'ascending'
                data_type = 'auto'
            
            if not field_name:
                continue
                
            sort_criteria.append({
                'field_name': field_name,
                'sort_order': sort_order,
                'data_type': data_type
            })
        
        if not sort_criteria:
            raise ValueError("No valid sort fields found")
        
        # Get options
        options = self.data.parameters.get('options', {})
        case_sensitive = options.get('case_sensitive', False)
        null_handling = options.get('null_handling', 'last')
        
        # Create comparison function
        def compare_items(a: Dict[str, Any], b: Dict[str, Any]) -> int:
            for criteria in sort_criteria:
                field_name = criteria['field_name']
                sort_order = criteria['sort_order']
                data_type = criteria['data_type']
                
                # Get field values (support dot notation)
                val_a = self._get_nested_value(a, field_name)
                val_b = self._get_nested_value(b, field_name)
                
                # Handle null values
                if val_a is None and val_b is None:
                    continue
                elif val_a is None:
                    result = 1 if null_handling == 'last' else -1
                elif val_b is None:
                    result = -1 if null_handling == 'last' else 1
                else:
                    # Convert values based on data type
                    val_a = self._convert_value(val_a, data_type, case_sensitive)
                    val_b = self._convert_value(val_b, data_type, case_sensitive)
                    
                    # Compare values
                    if val_a < val_b:
                        result = -1
                    elif val_a > val_b:
                        result = 1
                    else:
                        continue  # Values are equal, check next criteria
                
                # Apply sort order
                if sort_order == 'descending':
                    result = -result
                
                return result
            
            return 0  # All criteria are equal
        
        # Sort the items
        sorted_items = sorted(items, key=cmp_to_key(compare_items))
        return sorted_items
    
    async def _sort_random(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Randomly shuffle the items.
        """
        shuffled_items = items.copy()
        random.shuffle(shuffled_items)
        return shuffled_items
    
    async def _sort_by_expression(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Sort items using a custom expression.
        """
        expression = str(self.data.parameters.get('expression', ''))
        
        if not expression.strip():
            raise ValueError("Sort expression is required for expression sort mode")
        
        # Create comparison function using the expression
        def compare_items(a: Dict[str, Any], b: Dict[str, Any]) -> int:
            try:
                # Create a safe execution environment
                local_vars = {
                    'a': a,
                    'b': b,
                    'abs': abs,
                    'len': len,
                    'str': str,
                    'int': int,
                    'float': float,
                    'bool': bool,
                    'min': min,
                    'max': max,
                }
                
                # Execute the expression
                exec(expression, {"__builtins__": {}}, local_vars)
                
                # Get the return value (should be set by the expression)
                result = local_vars.get('return', 0)
                
                # Ensure result is an integer
                if isinstance(result, (int, float)):
                    return int(result)
                else:
                    return 0
                    
            except Exception:
                # If expression fails, treat items as equal
                return 0
        
        # Sort the items
        sorted_items = sorted(items, key=cmp_to_key(compare_items))
        return sorted_items

    def _get_nested_value(self, obj: Dict[str, Any], field_path: str) -> Any:
        """
        Get a nested value from an object using dot notation.

        Args:
            obj: The object to get the value from
            field_path: The field path (e.g., 'user.name' or 'id')

        Returns:
            The value at the specified path, or None if not found
        """
        try:
            value = obj
            for key in field_path.split('.'):
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            return value
        except (KeyError, TypeError, AttributeError):
            return None

    def _convert_value(self, value: Any, data_type: str, case_sensitive: bool = False) -> Any:
        """
        Convert a value to the appropriate type for comparison.

        Args:
            value: The value to convert
            data_type: The target data type ('auto', 'string', 'number', 'boolean', 'date')
            case_sensitive: Whether string comparisons should be case sensitive

        Returns:
            The converted value
        """
        if value is None:
            return None

        try:
            if data_type == 'string' or data_type == 'auto':
                str_value = str(value)
                return str_value if case_sensitive else str_value.lower()
            elif data_type == 'number':
                if isinstance(value, (int, float)):
                    return value
                else:
                    return float(str(value))
            elif data_type == 'boolean':
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                else:
                    return bool(value)
            elif data_type == 'date':
                # For date comparison, try to convert to timestamp
                from datetime import datetime
                if isinstance(value, str):
                    # Try common date formats
                    for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%Y-%m-%dT%H:%M:%S']:
                        try:
                            return datetime.strptime(value, fmt).timestamp()
                        except ValueError:
                            continue
                    # If no format matches, treat as string
                    return str(value).lower() if not case_sensitive else str(value)
                else:
                    return str(value).lower() if not case_sensitive else str(value)
            else:
                # Auto-detect type
                if isinstance(value, (int, float)):
                    return value
                elif isinstance(value, bool):
                    return value
                else:
                    str_value = str(value)
                    return str_value if case_sensitive else str_value.lower()
        except (ValueError, TypeError):
            # If conversion fails, fall back to string
            str_value = str(value)
            return str_value if case_sensitive else str_value.lower()

    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a sort node request.

        Performs node-specific validation for sort nodes:
        - For simple mode: validates that sort fields are specified
        - For expression mode: validates that expression is provided
        - Validates field names and data types

        Args:
            request: The node request to validate

        Returns:
            ValidationResult: The validation result with any errors found
        """
        # Run base validation first
        result = self.base_validate(request)

        # If base validation failed, no need to continue
        if not result.valid:
            return result

        errors = result.errors or []
        parameters = request.parameters

        # Get the sort type
        sort_type = str(parameters.get("sort_type", "simple"))

        if sort_type == "simple":
            # Validate sort fields
            sort_fields = parameters.get("sort_fields", [])
            if not sort_fields:
                errors.append(ValidationError(
                    parameter="sort_fields",
                    message="At least one sort field must be specified for simple sort mode"
                ))
            else:
                # Validate each sort field
                for i, field_config in enumerate(sort_fields):
                    if isinstance(field_config, dict):
                        field_name = field_config.get('field_name', '')
                        if not field_name or not field_name.strip():
                            errors.append(ValidationError(
                                parameter=f"sort_fields[{i}].field_name",
                                message="Field name cannot be empty"
                            ))

                        sort_order = field_config.get('sort_order', 'ascending')
                        if sort_order not in ['ascending', 'descending']:
                            errors.append(ValidationError(
                                parameter=f"sort_fields[{i}].sort_order",
                                message="Sort order must be 'ascending' or 'descending'"
                            ))

                        data_type = field_config.get('data_type', 'auto')
                        if data_type not in ['auto', 'string', 'number', 'boolean', 'date']:
                            errors.append(ValidationError(
                                parameter=f"sort_fields[{i}].data_type",
                                message="Data type must be one of: auto, string, number, boolean, date"
                            ))

        elif sort_type == "expression":
            # Validate expression
            expression = parameters.get("expression", "")
            if not expression or not str(expression).strip():
                errors.append(ValidationError(
                    parameter="expression",
                    message="Sort expression is required for expression sort mode"
                ))

        elif sort_type not in ["simple", "random", "expression"]:
            errors.append(ValidationError(
                parameter="sort_type",
                message="Sort type must be one of: simple, random, expression"
            ))

        return ValidationResult(valid=len(errors) == 0, errors=errors if errors else None)
