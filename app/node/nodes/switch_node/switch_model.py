from typing import ClassVar
from app.node.node_base.node_models import (
    NodeConnectionType, 
    NodeParameter, 
    NodeParameterOption, 
    PropertyTypes, 
    NodeTypeDescription,
    DisplayOptions,
    PropertyTypeOptions
)


class SwitchNodeDescription(NodeTypeDescription):
    """
    Description for the Switch Node.

    This node routes data to different outputs based on configurable rules or expressions.
    Supports multiple output connections for complex workflow routing scenarios.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "switch"

    @classmethod
    def create(cls) -> "SwitchNodeDescription":
        """
        Factory method to create a standard Switch node description.

        Returns:
            SwitchNodeDescription: A configured description for Switch nodes
        """
        return cls(
            name="switch",
            display_name="Switch",
            description="Routes data to different outputs based on configurable rules or expressions.",
            icon="code-branch",
            icon_color="#506000",
            group=["organization"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main, NodeConnectionType.Main, NodeConnectionType.Main, NodeConnectionType.Main],
            output_names=["0", "1", "2", "3"],
            parameters=[
                NodeParameter(
                    name="mode",
                    display_name="Mode",
                    description="How data should be routed",
                    type=PropertyTypes.OPTIONS,
                    default="rules",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Rules", 
                            value="rules",
                            description="Build a matching rule for each output"
                        ),
                        NodeParameterOption(
                            name="Expression", 
                            value="expression",
                            description="Write an expression to return the output index"
                        )
                    ]
                ),
                # Expression mode parameters
                NodeParameter(
                    name="number_outputs",
                    display_name="Number of Outputs",
                    description="How many outputs to create",
                    type=PropertyTypes.NUMBER,
                    default=4,
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["expression"]
                        }
                    )
                ),
                NodeParameter(
                    name="output_expression",
                    display_name="Output Index Expression",
                    description="Expression that returns the output index (0-based). Must return a number.",
                    type=PropertyTypes.STRING,
                    default="0",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["expression"]
                        }
                    )
                ),
                # Rules mode parameters
                NodeParameter(
                    name="rules",
                    display_name="Routing Rules",
                    description="List of routing rules to evaluate",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    ),
                    type_options=PropertyTypeOptions(
                        multiple_values=True,
                        multiple_value_button_text="Add Routing Rule"
                    )
                ),
                # Rule condition parameters (for each rule)
                NodeParameter(
                    name="rule_value1",
                    display_name="Value 1",
                    description="First value to compare",
                    type=PropertyTypes.STRING,
                    default="",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    )
                ),
                NodeParameter(
                    name="rule_operation",
                    display_name="Operation",
                    description="Comparison operation to perform",
                    type=PropertyTypes.OPTIONS,
                    default="equal",
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    ),
                
                    options=[
                        NodeParameterOption(name="Equal", value="equal"),
                        NodeParameterOption(name="Not Equal", value="notEqual"),
                        NodeParameterOption(name="Greater Than", value="greaterThan"),
                        NodeParameterOption(name="Less Than", value="lessThan"),
                        NodeParameterOption(name="Greater Than or Equal", value="greaterThanOrEqual"),
                        NodeParameterOption(name="Less Than or Equal", value="lessThanOrEqual"),
                        NodeParameterOption(name="Contains", value="contains"),
                        NodeParameterOption(name="Not Contains", value="notContains"),
                        NodeParameterOption(name="Starts With", value="startsWith"),
                        NodeParameterOption(name="Ends With", value="endsWith"),
                        NodeParameterOption(name="Is Empty", value="isEmpty"),
                        NodeParameterOption(name="Is Not Empty", value="isNotEmpty"),
                        NodeParameterOption(name="Regex Match", value="regex")
                    ]
                ),
                NodeParameter(
                    name="rule_value2",
                    display_name="Value 2",
                    description="Second value to compare",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"],
                            "rule_operation": ["equal", "notEqual", "greaterThan", "lessThan", 
                                             "greaterThanOrEqual", "lessThanOrEqual", "contains", 
                                             "notContains", "startsWith", "endsWith", "regex"]
                        }
                    )
                ),
                NodeParameter(
                    name="rule_output_index",
                    display_name="Output Index",
                    description="Which output to route to if this rule matches (0-based)",
                    type=PropertyTypes.NUMBER,
                    default=0,
                    required=True,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    )
                ),
                # Options
                NodeParameter(
                    name="fallback_output",
                    display_name="Fallback Output",
                    description="Output to use when no rules match (-1 for no output)",
                    type=PropertyTypes.NUMBER,
                    default=-1,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    )
                ),
                NodeParameter(
                    name="case_sensitive",
                    display_name="Case Sensitive",
                    description="Whether string comparisons should be case sensitive",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False
                ),
                NodeParameter(
                    name="ignore_whitespace",
                    display_name="Ignore Whitespace",
                    description="Whether to ignore leading/trailing whitespace in string comparisons",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False
                ),
                NodeParameter(
                    name="all_matching_outputs",
                    display_name="Send to All Matching Outputs",
                    description="Whether to send data to all outputs that match (not just the first one)",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "mode": ["rules"]
                        }
                    )
                )
            ]
        )
