"""
Webhook Authentication

This module provides authentication utilities for webhook endpoints
including various authentication methods and security checks.
"""

import base64
import hmac
import hashlib
import jwt
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from fastapi import Request, HTTPException, status
from app.utils.logging import get_logger


logger = get_logger("app.webhook.auth")


class WebhookAuthenticationError(Exception):
    """Custom exception for webhook authentication errors."""
    
    def __init__(self, message: str, status_code: int = 401):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class WebhookAuthenticator:
    """
    Webhook authentication handler supporting multiple authentication methods.
    """
    
    def __init__(self, auth_config: Dict[str, Any]):
        """
        Initialize the authenticator with configuration.
        
        Args:
            auth_config: Authentication configuration
        """
        self.auth_config = auth_config
        self.auth_method = auth_config.get("authentication_method", "none")
    
    async def authenticate(self, request: Request) -> <PERSON>ple[bool, Optional[Dict[str, Any]]]:
        """
        Authenticate the webhook request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Tuple of (is_authenticated, auth_data)
            
        Raises:
            WebhookAuthenticationError: If authentication fails
        """
        try:
            if self.auth_method == "none":
                return True, None
            
            elif self.auth_method == "basic":
                return await self._authenticate_basic(request)
            
            elif self.auth_method == "header":
                return await self._authenticate_header(request)
            
            elif self.auth_method == "jwt":
                return await self._authenticate_jwt(request)
            
            else:
                logger.warning(f"Unknown authentication method: {self.auth_method}")
                raise WebhookAuthenticationError(
                    f"Unsupported authentication method: {self.auth_method}",
                    status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
        except WebhookAuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}", exc_info=True)
            raise WebhookAuthenticationError(
                "Authentication failed due to internal error",
                status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    async def _authenticate_basic(self, request: Request) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Perform basic authentication.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Tuple of (is_authenticated, auth_data)
        """
        auth_header = request.headers.get("authorization", "")
        
        if not auth_header.startswith("Basic "):
            raise WebhookAuthenticationError("Basic authentication required")
        
        try:
            # Decode credentials
            encoded_credentials = auth_header[6:]  # Remove "Basic "
            decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
            username, password = decoded_credentials.split(":", 1)
            
            # Validate credentials
            expected_username = self.auth_config.get("username", "")
            expected_password = self.auth_config.get("password", "")
            
            if username != expected_username or password != expected_password:
                raise WebhookAuthenticationError("Invalid credentials")
            
            auth_data = {
                "method": "basic",
                "username": username,
                "authenticated_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Basic authentication successful for user: {username}")
            return True, auth_data
            
        except ValueError as e:
            raise WebhookAuthenticationError("Invalid basic authentication format")
        except Exception as e:
            logger.error(f"Basic authentication error: {str(e)}")
            raise WebhookAuthenticationError("Basic authentication failed")
    
    async def _authenticate_header(self, request: Request) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Perform header-based authentication.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Tuple of (is_authenticated, auth_data)
        """
        header_name = self.auth_config.get("header_name", "").lower()
        expected_value = self.auth_config.get("header_value", "")
        
        if not header_name:
            raise WebhookAuthenticationError("Header authentication not configured properly")
        
        actual_value = request.headers.get(header_name, "")
        
        if not actual_value:
            raise WebhookAuthenticationError(f"Missing authentication header: {header_name}")
        
        if actual_value != expected_value:
            raise WebhookAuthenticationError("Invalid authentication header value")
        
        auth_data = {
            "method": "header",
            "header_name": header_name,
            "authenticated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Header authentication successful for header: {header_name}")
        return True, auth_data
    
    async def _authenticate_jwt(self, request: Request) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Perform JWT authentication.
        
        Args:
            request: FastAPI request object
            
        Returns:
            Tuple of (is_authenticated, auth_data)
        """
        auth_header = request.headers.get("authorization", "")
        
        if not auth_header.startswith("Bearer "):
            raise WebhookAuthenticationError("JWT Bearer token required")
        
        token = auth_header[7:]  # Remove "Bearer "
        
        try:
            secret = self.auth_config.get("jwt_secret", "")
            algorithm = self.auth_config.get("jwt_algorithm", "HS256")
            
            if not secret:
                raise WebhookAuthenticationError("JWT secret not configured")
            
            # Decode and verify token
            payload = jwt.decode(token, secret, algorithms=[algorithm])
            
            auth_data = {
                "method": "jwt",
                "payload": payload,
                "algorithm": algorithm,
                "authenticated_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"JWT authentication successful for subject: {payload.get('sub', 'unknown')}")
            return True, auth_data
            
        except jwt.ExpiredSignatureError:
            raise WebhookAuthenticationError("JWT token has expired")
        except jwt.InvalidTokenError as e:
            raise WebhookAuthenticationError(f"Invalid JWT token: {str(e)}")
        except Exception as e:
            logger.error(f"JWT authentication error: {str(e)}")
            raise WebhookAuthenticationError("JWT authentication failed")


def validate_webhook_signature(
    request: Request,
    payload: bytes,
    secret: str,
    signature_header: str = "x-webhook-signature",
    algorithm: str = "sha256"
) -> bool:
    """
    Validate webhook signature for additional security.
    
    Args:
        request: FastAPI request object
        payload: Raw request payload
        secret: Webhook secret for signature validation
        signature_header: Header containing the signature
        algorithm: Hash algorithm to use
        
    Returns:
        bool: True if signature is valid
    """
    try:
        signature = request.headers.get(signature_header, "")
        
        if not signature:
            logger.warning("No signature header found")
            return False
        
        # Remove algorithm prefix if present (e.g., "sha256=")
        if "=" in signature:
            signature = signature.split("=", 1)[1]
        
        # Calculate expected signature
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            getattr(hashlib, algorithm)
        ).hexdigest()
        
        # Use secure comparison to prevent timing attacks
        is_valid = hmac.compare_digest(signature, expected_signature)
        
        if is_valid:
            logger.info("Webhook signature validation successful")
        else:
            logger.warning("Webhook signature validation failed")
        
        return is_valid
        
    except Exception as e:
        logger.error(f"Signature validation error: {str(e)}")
        return False


def check_webhook_rate_limit(
    client_ip: str,
    webhook_path: str,
    rate_limit_config: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Check if the webhook request is within rate limits.
    
    Args:
        client_ip: Client IP address
        webhook_path: Webhook path
        rate_limit_config: Rate limiting configuration
        
    Returns:
        bool: True if within rate limits
    """
    # This is a simplified implementation
    # In production, you would use Redis or similar for distributed rate limiting
    
    if not rate_limit_config:
        return True  # No rate limiting configured
    
    max_requests = rate_limit_config.get("max_requests", 100)
    time_window = rate_limit_config.get("time_window", 3600)  # 1 hour
    
    # TODO: Implement actual rate limiting logic with Redis
    # For now, always return True
    logger.info(f"Rate limit check for {client_ip} on {webhook_path}: OK")
    return True


def get_client_info(request: Request) -> Dict[str, Any]:
    """
    Extract client information from the request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Dict containing client information
    """
    # Get client IP (considering proxies)
    client_ip = request.client.host if request.client else "unknown"
    
    # Check for forwarded headers
    forwarded_for = request.headers.get("x-forwarded-for")
    if forwarded_for:
        client_ip = forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("x-real-ip")
    if real_ip:
        client_ip = real_ip
    
    return {
        "ip": client_ip,
        "user_agent": request.headers.get("user-agent", ""),
        "host": request.headers.get("host", ""),
        "origin": request.headers.get("origin", ""),
        "referer": request.headers.get("referer", ""),
        "forwarded_for": request.headers.get("x-forwarded-for", ""),
        "real_ip": request.headers.get("x-real-ip", ""),
        "timestamp": datetime.utcnow().isoformat()
    }
