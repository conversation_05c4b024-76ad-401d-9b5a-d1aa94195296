"""
Webhook Node Implementation

This module implements the webhook trigger node for receiving HTTP requests
and starting workflows based on incoming webhook data.
"""

import json
import logging
import base64
from typing import Dict, Any
from urllib.parse import parse_qs

from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationResult
from app.node.nodes.webhook.webhook_model import WebhookNodeDescription
from app.node.node_utils.workflow_defn import node_defn


# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='webhook', is_activity=True)
class WebhookNode(Node):
    """
    Webhook Trigger Node
    
    Provides webhook functionality for receiving HTTP requests and triggering
    workflows based on incoming data. Supports various authentication methods,
    response modes, and data processing options.
    """
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return WebhookNodeDescription.create()
    
    def __init__(self):
        super().__init__()
        self.webhook_registry = {}  # Store active webhooks
    
    def _validate_ip_whitelist(self, ip_whitelist: str, client_ip: str) -> bool:
        """
        Validate if the client IP is in the whitelist.
        
        Args:
            ip_whitelist: Comma-separated list of allowed IPs
            client_ip: Client's IP address
            
        Returns:
            bool: True if IP is allowed, False otherwise
        """
        if not ip_whitelist or ip_whitelist.strip() == "":
            return True  # No whitelist means all IPs are allowed
        
        allowed_ips = [ip.strip() for ip in ip_whitelist.split(",")]
        return client_ip in allowed_ips
    
    def _is_bot_request(self, user_agent: str) -> bool:
        """
        Check if the request is from a bot or crawler.
        
        Args:
            user_agent: User-Agent header value
            
        Returns:
            bool: True if request is from a bot
        """
        if not user_agent:
            return False
        
        bot_indicators = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python-requests', 'postman', 'insomnia', 'facebookexternalhit',
            'twitterbot', 'linkedinbot', 'whatsapp', 'telegram'
        ]
        
        user_agent_lower = user_agent.lower()
        return any(indicator in user_agent_lower for indicator in bot_indicators)
    
    def _parse_request_data(self, request_data: Dict[str, Any], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and structure the incoming request data.
        
        Args:
            request_data: Raw request data
            options: Webhook options
            
        Returns:
            Dict containing structured request data
        """
        parsed_data = {
            "headers": request_data.get("headers", {}),
            "query": request_data.get("query", {}),
            "params": request_data.get("params", {}),
            "method": request_data.get("method", "GET"),
            "url": request_data.get("url", ""),
            "timestamp": request_data.get("timestamp"),
            "body": {}
        }
        
        # Parse body based on content type
        body = request_data.get("body")
        content_type = request_data.get("headers", {}).get("content-type", "")
        
        if body:
            if "application/json" in content_type:
                try:
                    parsed_data["body"] = json.loads(body) if isinstance(body, str) else body
                except json.JSONDecodeError:
                    parsed_data["body"] = {"raw": body}
            elif "application/x-www-form-urlencoded" in content_type:
                if isinstance(body, str):
                    parsed_data["body"] = dict(parse_qs(body, keep_blank_values=True))
                else:
                    parsed_data["body"] = body
            elif "multipart/form-data" in content_type:
                # Handle multipart form data
                parsed_data["body"] = body
                if options.get("binary_data"):
                    parsed_data["files"] = request_data.get("files", {})
            else:
                parsed_data["body"] = {"raw": body}
        
        # Include raw body if requested
        if options.get("raw_body"):
            if isinstance(body, str):
                parsed_data["raw_body"] = body
            elif body:
                parsed_data["raw_body"] = base64.b64encode(
                    body if isinstance(body, bytes) else str(body).encode()
                ).decode()
        
        return parsed_data
    
    def _create_response(self, data: NodeData, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create the webhook response based on configuration.
        
        Args:
            data: Node configuration data
            webhook_data: Processed webhook data
            
        Returns:
            Dict containing response configuration
        """
        response_mode = data.parameters.get("response_mode", "immediately")
        response_code = data.parameters.get("response_code", 200)
        response_data = data.parameters.get("response_data", {"message": "Webhook received successfully"})
        response_headers = data.parameters.get("response_headers", {})
        
        # Convert headers list to dict if needed
        headers_dict = {}
        if isinstance(response_headers, list):
            for header in response_headers:
                if isinstance(header, dict) and "name" in header and "value" in header:
                    headers_dict[header["name"]] = header["value"]
        elif isinstance(response_headers, dict):
            headers_dict = response_headers
        
        # Add default headers
        headers_dict.setdefault("Content-Type", "application/json")
        
        response = {
            "status_code": response_code,
            "headers": headers_dict,
            "data": response_data
        }
        
        if response_mode == "immediately":
            response["immediate"] = True
        elif response_mode == "on_completion":
            response["immediate"] = False
            response["data"] = webhook_data  # Return the processed webhook data
        
        return response
    
    async def process_webhook(self, request_data: Dict[str, Any], node_config: NodeData) -> NodeResult:
        """
        Process an incoming webhook request.
        
        Args:
            request_data: Raw request data from the webhook endpoint
            node_config: Node configuration
            
        Returns:
            NodeResult with processed data and response configuration
        """
        try:
            # Get options
            options = node_config.parameters.get("options", {})
            
            # Validate IP whitelist
            client_ip = request_data.get("client_ip", "")
            ip_whitelist = options.get("ip_whitelist", "")
            if not self._validate_ip_whitelist(ip_whitelist, client_ip):
                logger.warning(f"Webhook request blocked: IP {client_ip} not in whitelist")
                return NodeResult(
                    error="IP address not allowed"
                )
            
            # Check for bot requests if configured
            if options.get("ignore_bots", False):
                user_agent = request_data.get("headers", {}).get("user-agent", "")
                if self._is_bot_request(user_agent):
                    logger.info("Webhook request ignored: Bot detected")
                    return NodeResult(
                        error="Bot request ignored"
                    )
            
            # Validate HTTP method
            allowed_methods = node_config.parameters.get("http_method", ["GET", "POST"])
            request_method = request_data.get("method", "GET").upper()
            if request_method not in allowed_methods:
                logger.warning(f"Webhook request blocked: Method {request_method} not allowed")
                return NodeResult(
                    error=f"HTTP method {request_method} not allowed"
                )
            
            # Parse request data
            webhook_data = self._parse_request_data(request_data, options)
            
            # Create response configuration
            response_config = self._create_response(node_config, webhook_data)
            
            logger.info(f"Webhook processed successfully for path: {node_config.parameters.get('webhook_path')}")

            # Include HTTP response configuration in the result
            webhook_data["_http_response"] = response_config

            return NodeResult(
                result=webhook_data,
                next_connection_index=0
            )
            
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
            return NodeResult(
                error=f"Webhook processing failed: {str(e)}"
            )
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the webhook node (this is called when the webhook is triggered).

        Args:
            data: Node execution data containing webhook request information

        Returns:
            NodeResult with the processed webhook data
        """
        # For webhook nodes, the actual processing happens in process_webhook
        # This method is called when the webhook is triggered
        webhook_path = data.parameters.get("webhook_path")
        logger.info(f"Webhook node executed for path: {webhook_path}")

        # Return the webhook data that was passed to this node
        return NodeResult(
            result=data.parameters.get("webhook_data", {}),
            next_connection_index=0
        )

    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a webhook node request with comprehensive checks.

        Args:
            request: The node request to validate

        Returns:
            ValidationResult with any validation errors
        """
        # Perform base validation first
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result

        # Add webhook-specific validation
        errors = []

        # Validate webhook path
        webhook_path = request.parameters.get("webhook_path")
        if not webhook_path:
            errors.append({
                "parameter": "webhook_path",
                "message": "Webhook path is required"
            })
        elif not webhook_path.replace("-", "").replace("_", "").isalnum():
            errors.append({
                "parameter": "webhook_path",
                "message": "Webhook path must contain only letters, numbers, hyphens, and underscores"
            })

        # Validate HTTP methods
        http_methods = request.parameters.get("http_method", [])
        if not http_methods:
            errors.append({
                "parameter": "http_method",
                "message": "At least one HTTP method must be selected"
            })

        # Validate response code
        response_code = request.parameters.get("response_code")
        if response_code is not None:
            try:
                code = int(response_code)
                if not 100 <= code <= 599:
                    errors.append({
                        "parameter": "response_code",
                        "message": "Response code must be between 100 and 599"
                    })
            except (ValueError, TypeError):
                errors.append({
                    "parameter": "response_code",
                    "message": "Response code must be a valid number"
                })

        # Validate IP whitelist format
        options = request.parameters.get("options", {})
        if isinstance(options, dict):
            ip_whitelist = options.get("ip_whitelist", "")
            if ip_whitelist:
                # Basic IP validation (simplified)
                ips = [ip.strip() for ip in ip_whitelist.split(",")]
                for ip in ips:
                    if ip and not self._is_valid_ip_format(ip):
                        errors.append({
                            "parameter": "options.ip_whitelist",
                            "message": f"Invalid IP address format: {ip}"
                        })

        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors if errors else None
        )

    def _is_valid_ip_format(self, ip: str) -> bool:
        """
        Basic IP address format validation.

        Args:
            ip: IP address string to validate

        Returns:
            bool: True if IP format is valid
        """
        try:
            parts = ip.split(".")
            if len(parts) != 4:
                return False
            for part in parts:
                num = int(part)
                if not 0 <= num <= 255:
                    return False
            return True
        except (ValueError, AttributeError):
            return False
