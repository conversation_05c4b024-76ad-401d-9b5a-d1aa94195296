"""
Webhook Utilities

This module provides utility functions for webhook operations including
URL generation, authentication, and request processing.
"""

import secrets
import string
import hashlib
import hmac
import base64
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

# from app.core.config import settings

DEFAULT_BASE_URL = "http://localhost:8000"
DEFAULT_API_PATH = "/api/v1"

def get_settings_api_path():
    """Get API path from settings safely."""
    try:
        from app.core.config import settings
        return settings.API_V1_STR
    except Exception:
        return DEFAULT_API_PATH


def generate_webhook_id(length: int = 16) -> str:
    """
    Generate a secure random webhook identifier.
    
    Args:
        length: Length of the generated ID
        
    Returns:
        str: Random webhook ID
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_webhook_path(prefix: str = "webhook", include_timestamp: bool = False) -> str:
    """
    Generate a descriptive webhook path with random components.
    
    Args:
        prefix: Prefix for the webhook path
        include_timestamp: Whether to include timestamp in the path
        
    Returns:
        str: Generated webhook path
    """
    random_part = generate_webhook_id(12)
    if include_timestamp:
        timestamp = datetime.utcnow().strftime("%Y%m%d")
        return f"{prefix}-{timestamp}-{random_part}"
    else:
        return f"{prefix}-{random_part}"


def generate_webhook_url(webhook_path: str, base_url: Optional[str] = None) -> str:
    """
    Generate the full webhook URL.

    Args:
        webhook_path: The webhook path
        base_url: Base URL (defaults to application base URL)

    Returns:
        str: Complete webhook URL
    """
    if base_url is None:
        base_url = "http://localhost:8000"  # Default base URL

    api_path = get_settings_api_path()
    webhook_endpoint = f"{api_path}/webhooks/trigger/{webhook_path}"
    return urljoin(base_url, webhook_endpoint)


def validate_webhook_signature(
    payload: bytes,
    signature: str,
    secret: str,
    algorithm: str = "sha256"
) -> bool:
    """
    Validate webhook signature for security.
    
    Args:
        payload: Raw request payload
        signature: Signature from request headers
        secret: Webhook secret
        algorithm: Hash algorithm to use
        
    Returns:
        bool: True if signature is valid
    """
    try:
        # Remove algorithm prefix if present (e.g., "sha256=")
        if "=" in signature:
            signature = signature.split("=", 1)[1]
        
        # Calculate expected signature
        expected_signature = hmac.new(
            secret.encode('utf-8'),
            payload,
            getattr(hashlib, algorithm)
        ).hexdigest()
        
        # Use secure comparison to prevent timing attacks
        return hmac.compare_digest(signature, expected_signature)
        
    except Exception:
        return False


def create_webhook_jwt_token(
    webhook_id: str,
    secret: str,
    expiry_hours: int = 24,
    additional_claims: Optional[Dict[str, Any]] = None
) -> str:
    """
    Create a JWT token for webhook authentication.
    
    Args:
        webhook_id: Webhook identifier
        secret: JWT secret
        expiry_hours: Token expiry in hours
        additional_claims: Additional claims to include
        
    Returns:
        str: JWT token
    """
    now = datetime.utcnow()
    payload = {
        "webhook_id": webhook_id,
        "iat": now,
        "exp": now + timedelta(hours=expiry_hours),
        "iss": "cerebro-webhook"
    }
    
    if additional_claims:
        payload.update(additional_claims)
    
    return jwt.encode(payload, secret, algorithm="HS256")


def verify_webhook_jwt_token(token: str, secret: str) -> Optional[Dict[str, Any]]:
    """
    Verify and decode a webhook JWT token.
    
    Args:
        token: JWT token to verify
        secret: JWT secret
        
    Returns:
        Dict containing token payload if valid, None otherwise
    """
    try:
        payload = jwt.decode(token, secret, algorithms=["HS256"])
        return payload
    except jwt.InvalidTokenError:
        return None


def sanitize_webhook_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    Sanitize webhook headers by removing sensitive information.
    
    Args:
        headers: Original headers dictionary
        
    Returns:
        Dict with sanitized headers
    """
    sensitive_headers = {
        "authorization",
        "cookie",
        "x-api-key",
        "x-auth-token",
        "x-access-token"
    }
    
    sanitized = {}
    for key, value in headers.items():
        if key.lower() not in sensitive_headers:
            sanitized[key] = value
        else:
            sanitized[key] = "[REDACTED]"
    
    return sanitized


def extract_webhook_metadata(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract useful metadata from webhook request.
    
    Args:
        request_data: Raw request data
        
    Returns:
        Dict containing extracted metadata
    """
    headers = request_data.get("headers", {})
    
    metadata = {
        "user_agent": headers.get("user-agent", ""),
        "content_type": headers.get("content-type", ""),
        "content_length": headers.get("content-length", "0"),
        "host": headers.get("host", ""),
        "origin": headers.get("origin", ""),
        "referer": headers.get("referer", ""),
        "x_forwarded_for": headers.get("x-forwarded-for", ""),
        "x_real_ip": headers.get("x-real-ip", ""),
        "timestamp": request_data.get("timestamp", datetime.utcnow().isoformat()),
        "method": request_data.get("method", "GET"),
        "query_params": request_data.get("query", {}),
        "path": request_data.get("path", "")
    }
    
    return metadata


def format_webhook_response(
    data: Any,
    status_code: int = 200,
    headers: Optional[Dict[str, str]] = None,
    content_type: str = "application/json"
) -> Dict[str, Any]:
    """
    Format webhook response in a standardized way.
    
    Args:
        data: Response data
        status_code: HTTP status code
        headers: Additional headers
        content_type: Response content type
        
    Returns:
        Dict containing formatted response
    """
    response_headers = {"Content-Type": content_type}
    if headers:
        response_headers.update(headers)
    
    return {
        "status_code": status_code,
        "headers": response_headers,
        "data": data,
        "timestamp": datetime.utcnow().isoformat()
    }


def is_webhook_path_valid(path: str) -> bool:
    """
    Validate webhook path format.
    
    Args:
        path: Webhook path to validate
        
    Returns:
        bool: True if path is valid
    """
    if not path:
        return False
    
    # Check length
    if len(path) < 3 or len(path) > 100:
        return False
    
    # Check characters (alphanumeric, hyphens, underscores only)
    allowed_chars = set(string.ascii_letters + string.digits + "-_")
    if not all(c in allowed_chars for c in path):
        return False
    
    # Must start and end with alphanumeric
    if not (path[0].isalnum() and path[-1].isalnum()):
        return False
    
    # No consecutive special characters
    if "--" in path or "__" in path or "-_" in path or "_-" in path:
        return False
    
    return True


def get_webhook_test_url(webhook_path: str) -> str:
    """
    Generate test URL for webhook during development.

    Args:
        webhook_path: Webhook path

    Returns:
        str: Test webhook URL
    """
    base_url = "http://localhost:8000"  # Default base URL
    api_path = get_settings_api_path()
    test_endpoint = f"{api_path}/webhooks/test/{webhook_path}"
    return f"{base_url}{test_endpoint}"
    # return urljoin(base_url, test_endpoint)


def create_webhook_documentation(webhook_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate documentation for a webhook endpoint.
    
    Args:
        webhook_config: Webhook configuration
        
    Returns:
        Dict containing webhook documentation
    """
    webhook_path = webhook_config.get("webhook_path", "")
    http_methods = webhook_config.get("http_method", ["GET", "POST"])
    
    doc = {
        "webhook_path": webhook_path,
        "webhook_url": generate_webhook_url(webhook_path),
        "test_url": get_webhook_test_url(webhook_path),
        "supported_methods": http_methods,
        "authentication": webhook_config.get("authentication", "none"),
        "response_mode": webhook_config.get("response_mode", "immediately"),
        "description": f"Webhook endpoint for triggering workflow",
        "examples": {
            "curl": f"curl -X POST {generate_webhook_url(webhook_path)} -H 'Content-Type: application/json' -d '{{\"test\": \"data\"}}'",
            "javascript": f"""
fetch('{generate_webhook_url(webhook_path)}', {{
    method: 'POST',
    headers: {{
        'Content-Type': 'application/json'
    }},
    body: JSON.stringify({{test: 'data'}})
}})
.then(response => response.json())
.then(data => console.log(data));
            """.strip()
        }
    }
    
    return doc
