"""
PostgreSQL CREATE TABLE Operation

This module implements the CREATE TABLE operation for PostgreSQL nodes,
providing capabilities for creating database tables with proper schema definition.
"""

import json
import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class CreateTableOperation(BaseOperation):
    """
    PostgreSQL CREATE TABLE operation implementation.
    
    Provides capabilities for creating database tables including:
    - Column definitions with data types and constraints
    - Primary key and foreign key constraints
    - Index creation
    - IF NOT EXISTS support
    - Proper schema handling
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a CREATE TABLE operation on PostgreSQL.
        
        Args:
            data: Node execution data containing table creation parameters
            
        Returns:
            NodeResult: Creation results or error information
        """
        try:
            # Extract parameters
            table_schema = data.parameters.get('table_schema', 'public')
            table_name = data.parameters.get('table_name')
            columns = data.parameters.get('columns', [])
            if_not_exists = data.parameters.get('if_not_exists', True)
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not table_name:
                return NodeResult(error="Table name is required for CREATE TABLE operation")
            
            if not columns:
                return NodeResult(error="Column definitions are required for CREATE TABLE operation")
            
            # Validate table name
            if not CreateTableOperation.validate_table_name(table_name):
                return NodeResult(error=f"Invalid table name: {table_name}")
            
            # Parse column definitions
            parsed_columns = CreateTableOperation._parse_columns(columns)
            if not parsed_columns:
                return NodeResult(error="Invalid or empty column definitions")
            
            # Build the CREATE TABLE query
            query_builder = CreateTableQueryBuilder(table_schema, table_name)
            query_builder.set_if_not_exists(if_not_exists)
            
            for column_def in parsed_columns:
                query_builder.add_column(column_def)
            
            # Build query
            query = query_builder.build()
            
            logger.info(f"Executing CREATE TABLE query: {query}")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction:
                    async with connection.transaction():
                        result = await CreateTableOperation._execute_create_table(
                            connection, query, timeout
                        )
                else:
                    result = await CreateTableOperation._execute_create_table(
                        connection, query, timeout
                    )
                
                # Format results
                result_data = {
                    "operation": "CREATE TABLE",
                    "table_name": f"{table_schema}.{table_name}",
                    "status": "success",
                    "message": f"Table {table_schema}.{table_name} created successfully",
                    "columns_created": len(parsed_columns),
                    "data": []
                }
                
                logger.info(f"CREATE TABLE operation completed: {table_schema}.{table_name}")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = CreateTableOperation.format_error(e, "CREATE TABLE")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    def _parse_columns(columns: Any) -> Optional[List[Dict[str, Any]]]:
        """
        Parse and validate column definitions.
        
        Args:
            columns: Raw column definitions (list or JSON string)
            
        Returns:
            Parsed column definitions as list of dicts, or None if invalid
        """
        if isinstance(columns, str):
            try:
                columns = json.loads(columns)
            except json.JSONDecodeError:
                logger.error("Invalid JSON format in column definitions")
                return None
        
        if not isinstance(columns, list):
            logger.error("Column definitions must be an array")
            return None
        
        parsed_columns = []
        for i, column in enumerate(columns):
            if not isinstance(column, dict):
                logger.error(f"Column definition {i} must be an object")
                return None
            
            # Validate required fields
            if 'name' not in column:
                logger.error(f"Column definition {i} missing required 'name' field")
                return None
            
            if 'type' not in column:
                logger.error(f"Column definition {i} missing required 'type' field")
                return None
            
            # Validate column name
            if not CreateTableOperation._is_valid_column_name(column['name']):
                logger.error(f"Invalid column name: {column['name']}")
                return None
            
            parsed_columns.append(column)
        
        return parsed_columns
    
    @staticmethod
    def _is_valid_column_name(name: str) -> bool:
        """
        Validate column name for SQL injection prevention.
        
        Args:
            name: Column name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not name or not isinstance(name, str):
            return False
        
        # Allow alphanumeric and underscore, must start with letter or underscore
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    async def _execute_create_table(connection, query: str, timeout: int):
        """
        Execute the CREATE TABLE query.
        
        Args:
            connection: Database connection
            query: CREATE TABLE query string
            timeout: Query timeout
            
        Returns:
            Query result
        """
        if timeout and timeout > 0:
            return await connection.execute(query, timeout=timeout)
        else:
            return await connection.execute(query)


class CreateTableQueryBuilder:
    """
    Builder class for constructing CREATE TABLE queries safely.
    
    Provides a fluent interface for building CREATE TABLE queries with
    proper column definitions and constraints.
    """
    
    def __init__(self, schema: str, table_name: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table_name: Table name
        """
        self.schema = schema
        self.table_name = table_name
        self.columns = []
        self.if_not_exists = True
    
    def set_if_not_exists(self, if_not_exists: bool) -> 'CreateTableQueryBuilder':
        """
        Set whether to use IF NOT EXISTS clause.
        
        Args:
            if_not_exists: Whether to use IF NOT EXISTS
            
        Returns:
            CreateTableQueryBuilder: Self for method chaining
        """
        self.if_not_exists = if_not_exists
        return self
    
    def add_column(self, column_def: Dict[str, Any]) -> 'CreateTableQueryBuilder':
        """
        Add a column definition.
        
        Args:
            column_def: Column definition dictionary
            
        Returns:
            CreateTableQueryBuilder: Self for method chaining
        """
        self.columns.append(column_def)
        return self
    
    def build(self) -> str:
        """
        Build the CREATE TABLE query.
        
        Returns:
            CREATE TABLE query string
        """
        if not self.columns:
            raise ValueError("No columns defined for CREATE TABLE")
        
        # Escape schema and table name
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table_name)
        
        # Build CREATE TABLE clause
        create_clause = "CREATE TABLE"
        if self.if_not_exists:
            create_clause += " IF NOT EXISTS"
        
        create_clause += f" {escaped_schema}.{escaped_table}"
        
        # Build column definitions
        column_definitions = []
        primary_keys = []
        
        for column in self.columns:
            column_def = self._build_column_definition(column)
            column_definitions.append(column_def)
            
            # Track primary key columns
            if column.get('primary_key', False):
                primary_keys.append(BaseOperation.escape_identifier(column['name']))
        
        # Add primary key constraint if multiple columns
        if len(primary_keys) > 1:
            pk_constraint = f"PRIMARY KEY ({', '.join(primary_keys)})"
            column_definitions.append(pk_constraint)
        
        # Combine all parts
        columns_clause = ",\n    ".join(column_definitions)
        query = f"{create_clause} (\n    {columns_clause}\n)"
        
        return query
    
    def _build_column_definition(self, column: Dict[str, Any]) -> str:
        """
        Build a single column definition.
        
        Args:
            column: Column definition dictionary
            
        Returns:
            Column definition string
        """
        name = BaseOperation.escape_identifier(column['name'])
        data_type = self._validate_data_type(column['type'])
        
        definition = f"{name} {data_type}"
        
        # Add constraints
        if column.get('not_null', False):
            definition += " NOT NULL"
        
        if column.get('unique', False):
            definition += " UNIQUE"
        
        if column.get('primary_key', False) and len([c for c in self.columns if c.get('primary_key', False)]) == 1:
            definition += " PRIMARY KEY"
        
        if 'default' in column:
            default_value = column['default']
            if isinstance(default_value, str):
                definition += f" DEFAULT '{default_value}'"
            else:
                definition += f" DEFAULT {default_value}"
        
        if 'check' in column:
            definition += f" CHECK ({column['check']})"
        
        return definition
    
    def _validate_data_type(self, data_type: str) -> str:
        """
        Validate and normalize PostgreSQL data type.
        
        Args:
            data_type: Data type string
            
        Returns:
            Validated data type
        """
        if not data_type or not isinstance(data_type, str):
            raise ValueError("Data type must be a non-empty string")
        
        # List of common PostgreSQL data types
        valid_types = [
            'SMALLINT', 'INTEGER', 'BIGINT', 'DECIMAL', 'NUMERIC', 'REAL', 'DOUBLE PRECISION',
            'SMALLSERIAL', 'SERIAL', 'BIGSERIAL',
            'VARCHAR', 'CHAR', 'TEXT',
            'BYTEA',
            'TIMESTAMP', 'TIMESTAMP WITH TIME ZONE', 'DATE', 'TIME', 'TIME WITH TIME ZONE', 'INTERVAL',
            'BOOLEAN',
            'POINT', 'LINE', 'LSEG', 'BOX', 'PATH', 'POLYGON', 'CIRCLE',
            'CIDR', 'INET', 'MACADDR', 'MACADDR8',
            'BIT', 'BIT VARYING',
            'TSVECTOR', 'TSQUERY',
            'UUID',
            'XML',
            'JSON', 'JSONB',
            'ARRAY'
        ]
        
        # Normalize the type (uppercase, handle parameterized types)
        normalized_type = data_type.upper().strip()
        
        # Handle parameterized types like VARCHAR(255), DECIMAL(10,2)
        import re
        base_type_match = re.match(r'^([A-Z\s]+)(\([^)]+\))?', normalized_type)
        if base_type_match:
            base_type = base_type_match.group(1).strip()
            
            # Check if base type is valid
            if base_type in valid_types or any(base_type.startswith(vt) for vt in valid_types):
                return normalized_type
        
        # If not found in common types, allow it but log a warning
        logger.warning(f"Uncommon data type used: {data_type}")
        return normalized_type


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute CREATE TABLE operation."""
    return await CreateTableOperation.execute(data)
