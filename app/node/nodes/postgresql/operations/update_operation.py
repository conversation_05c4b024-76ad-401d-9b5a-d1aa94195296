"""
PostgreSQL UPDATE Operation

This module implements the UPDATE operation for PostgreSQL nodes,
providing comprehensive update capabilities with conditional logic and data validation.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class UpdateOperation(BaseOperation):
    """
    PostgreSQL UPDATE operation implementation.
    
    Provides comprehensive UPDATE capabilities including:
    - Conditional updates with WHERE clauses
    - Data validation and type conversion
    - Transaction handling
    - RETURNING clause support
    - Batch update support
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute an UPDATE operation on PostgreSQL.
        
        Args:
            data: Node execution data containing update parameters
            
        Returns:
            NodeResult: Update results or error information
        """
        try:
            # Extract parameters
            schema = data.parameters.get('schema', 'public')
            table = data.parameters.get('table')
            update_data = data.parameters.get('data')
            where_clause = data.parameters.get('where_clause')
            return_fields = data.parameters.get('return_fields', '*')
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not table:
                return NodeResult(error="Table name is required for UPDATE operation")
            
            if not update_data:
                return NodeResult(error="Data is required for UPDATE operation")
            
            # Validate table name
            if not UpdateOperation.validate_table_name(table):
                return NodeResult(error=f"Invalid table name: {table}")
            
            # Parse and validate update data
            parsed_data = UpdateOperation._parse_update_data(update_data)
            if not parsed_data:
                return NodeResult(error="Invalid or empty update data")
            
            # Build the UPDATE query
            query_builder = UpdateQueryBuilder(schema, table)
            query_builder.set_update_data(parsed_data)
            query_builder.set_returning_fields(return_fields)
            
            if where_clause:
                query_builder.set_where_clause(where_clause)
            
            # Build query
            query, params = query_builder.build()
            
            logger.info(f"Executing UPDATE query: {query}")
            logger.debug(f"Query parameters: {params}")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction:
                    async with connection.transaction():
                        result_records = await UpdateOperation._execute_update(
                            connection, query, params, timeout
                        )
                else:
                    result_records = await UpdateOperation._execute_update(
                        connection, query, params, timeout
                    )
                
                # Format results
                result_data = UpdateOperation.format_results(result_records, "UPDATE")
                result_data["updated_count"] = len(result_records) if result_records else 0
                
                logger.info(f"UPDATE operation completed: {result_data['updated_count']} records updated")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = UpdateOperation.format_error(e, "UPDATE")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    def _parse_update_data(data: Any) -> Optional[Dict[str, Any]]:
        """
        Parse and validate update data.
        
        Args:
            data: Raw update data (dict or JSON string)
            
        Returns:
            Parsed data as dict, or None if invalid
        """
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                logger.error("Invalid JSON format in update data")
                return None
        
        if isinstance(data, dict):
            if not data:
                logger.error("Update data cannot be empty")
                return None
            return data
        else:
            logger.error("Update data must be an object")
            return None
    
    @staticmethod
    async def _execute_update(connection, query: str, params: List[Any], timeout: int):
        """
        Execute the UPDATE query with proper parameter binding.
        
        Args:
            connection: Database connection
            query: UPDATE query string
            params: Query parameters
            timeout: Query timeout
            
        Returns:
            Query results
        """
        if timeout and timeout > 0:
            return await connection.fetch(query, *params, timeout=timeout)
        else:
            return await connection.fetch(query, *params)


class UpdateQueryBuilder:
    """
    Builder class for constructing UPDATE queries safely.
    
    Provides a fluent interface for building UPDATE queries with
    proper parameter binding and WHERE clause support.
    """
    
    def __init__(self, schema: str, table: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table: Table name
        """
        self.schema = schema
        self.table = table
        self.update_data = {}
        self.where_clause = None
        self.returning_fields = '*'
        self.parameters = []
    
    def set_update_data(self, data: Dict[str, Any]) -> 'UpdateQueryBuilder':
        """
        Set data to update.
        
        Args:
            data: Dictionary of column-value pairs to update
            
        Returns:
            UpdateQueryBuilder: Self for method chaining
        """
        if data and isinstance(data, dict):
            self.update_data = data
        
        return self
    
    def set_where_clause(self, where_clause: str) -> 'UpdateQueryBuilder':
        """
        Set WHERE clause for conditional updates.
        
        Args:
            where_clause: WHERE condition
            
        Returns:
            UpdateQueryBuilder: Self for method chaining
        """
        if where_clause and where_clause.strip():
            self.where_clause = where_clause.strip()
        
        return self
    
    def set_returning_fields(self, fields: str) -> 'UpdateQueryBuilder':
        """
        Set fields to return from UPDATE.
        
        Args:
            fields: Comma-separated field names or '*'
            
        Returns:
            UpdateQueryBuilder: Self for method chaining
        """
        if fields and fields.strip():
            self.returning_fields = fields.strip()
        
        return self
    
    def build(self) -> tuple[str, List[Any]]:
        """
        Build the UPDATE query.
        
        Returns:
            Tuple of (query_string, parameters)
        """
        if not self.update_data:
            raise ValueError("No update data provided")
        
        # Escape table name
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table)
        
        # Build SET clause
        set_clauses = []
        param_index = 1
        
        for column, value in self.update_data.items():
            # Validate and escape column name
            if not self._is_valid_column_name(column):
                raise ValueError(f"Invalid column name: {column}")
            
            escaped_column = BaseOperation.escape_identifier(column)
            set_clauses.append(f"{escaped_column} = ${param_index}")
            self.parameters.append(value)
            param_index += 1
        
        set_clause = ", ".join(set_clauses)
        
        # Build base UPDATE query
        query = f"UPDATE {escaped_schema}.{escaped_table} SET {set_clause}"
        
        # Add WHERE clause
        if self.where_clause:
            query += f" WHERE {self.where_clause}"
        
        # Add RETURNING clause
        if self.returning_fields and self.returning_fields != '':
            if self.returning_fields == '*':
                query += " RETURNING *"
            else:
                # Parse and escape returning fields
                return_cols = [col.strip() for col in self.returning_fields.split(',')]
                escaped_return_cols = []
                
                for col in return_cols:
                    if self._is_valid_column_name(col):
                        escaped_return_cols.append(BaseOperation.escape_identifier(col))
                    else:
                        logger.warning(f"Skipping invalid return field: {col}")
                
                if escaped_return_cols:
                    query += f" RETURNING {', '.join(escaped_return_cols)}"
        
        return query, self.parameters
    
    def _is_valid_column_name(self, column: str) -> bool:
        """
        Validate column name for SQL injection prevention.
        
        Args:
            column: Column name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not column or not isinstance(column, str):
            return False
        
        # Allow alphanumeric, underscore, and dots for table.column
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$'
        return bool(re.match(pattern, column))


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute UPDATE operation."""
    return await UpdateOperation.execute(data)
