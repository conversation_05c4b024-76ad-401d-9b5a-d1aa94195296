"""
PostgreSQL EXECUTE QUERY Operation

This module implements the EXECUTE QUERY operation for PostgreSQL nodes,
providing capabilities for executing custom SQL queries with parameter binding.
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional, Union

from app.node.node_base.node import Node<PERSON><PERSON><PERSON>
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class ExecuteQueryOperation(BaseOperation):
    """
    PostgreSQL EXECUTE QUERY operation implementation.
    
    Provides capabilities for executing custom SQL queries including:
    - Parameterized queries with proper binding
    - Support for SELECT, INSERT, UPDATE, DELETE, and DDL statements
    - Query validation and safety checks
    - Transaction handling
    - Result formatting for different query types
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a custom SQL query on PostgreSQL.
        
        Args:
            data: Node execution data containing query parameters
            
        Returns:
            NodeResult: Query results or error information
        """
        try:
            # Extract parameters
            query = data.parameters.get('query')
            query_parameters = data.parameters.get('query_parameters', {})
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            # Validate required parameters
            if not query:
                return NodeResult(error="SQL query is required for EXECUTE QUERY operation")
            
            if not isinstance(query, str):
                return NodeResult(error="Query must be a string")
            
            # Parse query parameters
            parsed_params = ExecuteQueryOperation._parse_query_parameters(query_parameters)
            
            # Validate and prepare the query
            query_processor = QueryProcessor(query, parsed_params)
            # Perform basic query validation
            validation_result = query_processor.validate()
            if not validation_result['valid']:
                return NodeResult(error=f"Query validation failed: {validation_result['error']}")
            
            # Process the query and parameters
            processed_query, processed_params = query_processor.process()
            logger.info(f"Executing custom query: {processed_query[:100]}...")
            logger.debug(f"Query parameters: {processed_params}")
            
            # Determine query type for appropriate execution method
            query_type = query_processor.get_query_type()
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction and query_type in ['INSERT', 'UPDATE', 'DELETE', 'DDL']:
                    async with connection.transaction():
                        result_records = await ExecuteQueryOperation._execute_query(
                            connection, processed_query, processed_params, timeout, query_type
                        )
                else:
                    result_records = await ExecuteQueryOperation._execute_query(
                        connection, processed_query, processed_params, timeout, query_type
                    )
                
                # Format results based on query type
                result_data = ExecuteQueryOperation._format_query_results(
                    result_records, query_type, processed_query
                )
                logger.info(f"EXECUTE QUERY operation completed: {query_type} query executed successfully")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = ExecuteQueryOperation.format_error(e, "EXECUTE QUERY")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    def _parse_query_parameters(params: Any) -> Dict[str, Any]:
        """
        Parse and validate query parameters.
        
        Args:
            params: Raw query parameters (dict or JSON string)
            
        Returns:
            Parsed parameters as dict
        """
        if not params:
            return {}
        
        if isinstance(params, str):
            try:
                params = json.loads(params)
            except json.JSONDecodeError:
                logger.warning("Invalid JSON format in query parameters, using empty dict")
                return {}
        
        if isinstance(params, dict):
            return params
        else:
            logger.warning("Query parameters must be an object, using empty dict")
            return {}
    
    @staticmethod
    async def _execute_query(connection, query: str, params: List[Any], timeout: int, query_type: str):
        """
        Execute the SQL query with proper parameter binding.
        
        Args:
            connection: Database connection
            query: SQL query string
            params: Query parameters
            timeout: Query timeout
            query_type: Type of query (SELECT, INSERT, etc.)
            
        Returns:
            Query results
        """
        if query_type == 'SELECT':
            # Use fetch for SELECT queries
            if timeout and timeout > 0:
                return await connection.fetch(query, *params, timeout=timeout)
            else:
                return await connection.fetch(query, *params)
        else:
            # Use execute for non-SELECT queries
            if timeout and timeout > 0:
                result = await connection.execute(query, *params, timeout=timeout)
                return result
            else:
                result = await connection.execute(query, *params)
                return result
    
    @staticmethod
    def _format_query_results(results: Any, query_type: str, query: str) -> Dict[str, Any]:
        """
        Format query results based on query type.
        
        Args:
            results: Raw query results
            query_type: Type of query executed
            query: Original query string
            
        Returns:
            Formatted results dictionary
        """
        if query_type == 'SELECT':
            # Format SELECT results
            return ExecuteQueryOperation.format_results(results, "EXECUTE QUERY (SELECT)")
        else:
            # For non-SELECT queries, results is usually a status string
            affected_rows = 0
            if isinstance(results, str):
                # Parse affected rows from status string (e.g., "INSERT 0 5", "UPDATE 3", "DELETE 2")
                match = re.search(r'(\d+)$', results)
                if match:
                    affected_rows = int(match.group(1))
            
            return {
                "operation": f"EXECUTE QUERY ({query_type})",
                "query_type": query_type,
                "rows_affected": affected_rows,
                "status": results if isinstance(results, str) else str(results),
                "data": []
            }


class QueryProcessor:
    """
    Processor for validating and preparing SQL queries.
    
    Provides query validation, parameter binding, and safety checks
    for custom SQL query execution.
    """
    
    def __init__(self, query: str, parameters: Dict[str, Any]):
        """
        Initialize the query processor.
        
        Args:
            query: SQL query string
            parameters: Query parameters
        """
        self.query = query.strip()
        self.parameters = parameters
        self.query_type = None
    
    def validate(self) -> Dict[str, Any]:
        """
        Validate the SQL query for basic safety and syntax.
        
        Returns:
            Dictionary with validation result
        """
        if not self.query:
            return {"valid": False, "error": "Query cannot be empty"}
        
        # Check for potentially dangerous operations
        dangerous_patterns = [
            r'\bDROP\s+DATABASE\b',
            r'\bDROP\s+SCHEMA\b',
            r'\bTRUNCATE\b',
            r'\bALTER\s+SYSTEM\b',
            r'\bSET\s+ROLE\b',
            r'\bCREATE\s+ROLE\b',
            r'\bDROP\s+ROLE\b'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, self.query, re.IGNORECASE):
                return {"valid": False, "error": f"Potentially dangerous operation detected: {pattern}"}
        
        # Basic syntax validation (very basic)
        if self.query.count('(') != self.query.count(')'):
            return {"valid": False, "error": "Unmatched parentheses in query"}
        
        if self.query.count("'") % 2 != 0:
            return {"valid": False, "error": "Unmatched single quotes in query"}
        
        return {"valid": True, "error": None}
    
    def get_query_type(self) -> str:
        """
        Determine the type of SQL query.
        
        Returns:
            Query type (SELECT, INSERT, UPDATE, DELETE, DDL, etc.)
        """
        if self.query_type:
            return self.query_type
        
        # Extract first significant word
        first_word = re.match(r'\s*(\w+)', self.query, re.IGNORECASE)
        if first_word:
            word = first_word.group(1).upper()
            if word in ['SELECT', 'WITH']:
                self.query_type = 'SELECT'
            elif word in ['INSERT']:
                self.query_type = 'INSERT'
            elif word in ['UPDATE']:
                self.query_type = 'UPDATE'
            elif word in ['DELETE']:
                self.query_type = 'DELETE'
            elif word in ['CREATE', 'ALTER', 'DROP']:
                self.query_type = 'DDL'
            else:
                self.query_type = 'OTHER'
        else:
            self.query_type = 'OTHER'
        
        return self.query_type
    
    def process(self) -> tuple[str, List[Any]]:
        """
        Processes PostgreSQL-style queries with parameters in the form {"$1": ..., "$2": ...}

        Returns:
            Tuple of (query string, list of parameters in correct order)
        """
        if not self.parameters:
            return self.query, []

        processed_query = self.query

        # Match $1, $2, ..., $n in order of appearance in query
        placeholder_pattern = r'\$(\d+)'  # Match $1, $2, etc.
        matches = re.findall(placeholder_pattern, processed_query)

        seen = set()
        ordered_param_keys = []
        for match in matches:
            key = f"${match}"
            if key not in seen:
                seen.add(key)
                ordered_param_keys.append(key)

        param_list = []
        for key in ordered_param_keys:
            if key not in self.parameters:
                raise ValueError(f"Missing parameter for placeholder {key}")
            param_list.append(self.parameters[key])

        return processed_query, param_list


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute EXECUTE QUERY operation."""
    return await ExecuteQueryOperation.execute(data)
