"""
PostgreSQL DROP TABLE Operation

This module implements the DROP TABLE operation for PostgreSQL nodes,
providing capabilities for dropping database tables with safety checks.
"""

import logging
from typing import Dict, Any, List

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class DropTableOperation(BaseOperation):
    """
    PostgreSQL DROP TABLE operation implementation.
    
    Provides capabilities for dropping database tables including:
    - IF EXISTS support to prevent errors
    - CASCADE and RESTRICT options
    - Safety checks and confirmations
    - Proper schema handling
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a DROP TABLE operation on PostgreSQL.
        
        Args:
            data: Node execution data containing table drop parameters
            
        Returns:
            NodeResult: Drop results or error information
        """
        try:
            # Extract parameters
            table_schema = data.parameters.get('table_schema', 'public')
            table_name = data.parameters.get('table_name')
            if_exists = data.parameters.get('if_exists', True)
            cascade = data.parameters.get('cascade', False)
            confirm_drop = data.parameters.get('confirm_drop', False)
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not table_name:
                return NodeResult(error="Table name is required for DROP TABLE operation")
            
            # Validate table name
            if not DropTableOperation.validate_table_name(table_name):
                return NodeResult(error=f"Invalid table name: {table_name}")
            
            # Safety check: require explicit confirmation for DROP TABLE
            if not confirm_drop:
                return NodeResult(
                    error="DROP TABLE operation requires explicit confirmation. "
                          "Set 'confirm_drop' to true to proceed with dropping the table."
                )
            
            # Build the DROP TABLE query
            query_builder = DropTableQueryBuilder(table_schema, table_name)
            query_builder.set_if_exists(if_exists)
            query_builder.set_cascade(cascade)
            
            # Build query
            query = query_builder.build()
            logger.info(f"Executing DROP TABLE query: {query}")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction:
                    async with connection.transaction():
                        result = await DropTableOperation._execute_drop_table(
                            connection, query, timeout
                        )
                else:
                    result = await DropTableOperation._execute_drop_table(
                        connection, query, timeout
                    )
                
                # Format results
                result_data = {
                    "operation": "DROP TABLE",
                    "table_name": f"{table_schema}.{table_name}",
                    "status": "success",
                    "message": f"Table {table_schema}.{table_name} dropped successfully",
                    "cascade": cascade,
                    "data": []
                }
                logger.info(f"DROP TABLE operation completed: {table_schema}.{table_name}")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = DropTableOperation.format_error(e, "DROP TABLE")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _execute_drop_table(connection, query: str, timeout: int):
        """
        Execute the DROP TABLE query.
        
        Args:
            connection: Database connection
            query: DROP TABLE query string
            timeout: Query timeout
            
        Returns:
            Query result
        """
        if timeout and timeout > 0:
            return await connection.execute(query, timeout=timeout)
        else:
            return await connection.execute(query)


class DropTableQueryBuilder:
    """
    Builder class for constructing DROP TABLE queries safely.
    
    Provides a fluent interface for building DROP TABLE queries with
    proper safety options and cascade handling.
    """
    
    def __init__(self, schema: str, table_name: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table_name: Table name
        """
        self.schema = schema
        self.table_name = table_name
        self.if_exists = True
        self.cascade = False
    
    def set_if_exists(self, if_exists: bool) -> 'DropTableQueryBuilder':
        """
        Set whether to use IF EXISTS clause.
        
        Args:
            if_exists: Whether to use IF EXISTS
            
        Returns:
            DropTableQueryBuilder: Self for method chaining
        """
        self.if_exists = if_exists
        return self
    
    def set_cascade(self, cascade: bool) -> 'DropTableQueryBuilder':
        """
        Set whether to use CASCADE option.
        
        Args:
            cascade: Whether to use CASCADE
            
        Returns:
            DropTableQueryBuilder: Self for method chaining
        """
        self.cascade = cascade
        return self
    
    def build(self) -> str:
        """
        Build the DROP TABLE query.
        
        Returns:
            DROP TABLE query string
        """
        # Escape schema and table name
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table_name)
        
        # Build DROP TABLE clause
        drop_clause = "DROP TABLE"
        if self.if_exists:
            drop_clause += " IF EXISTS"
        
        drop_clause += f" {escaped_schema}.{escaped_table}"
        
        # Add CASCADE or RESTRICT
        if self.cascade:
            drop_clause += " CASCADE"
        else:
            drop_clause += " RESTRICT"
        
        return drop_clause


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute DROP TABLE operation."""
    return await DropTableOperation.execute(data)
