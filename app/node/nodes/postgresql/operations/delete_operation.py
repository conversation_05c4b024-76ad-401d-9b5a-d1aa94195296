"""
PostgreSQL DELETE Operation

This module implements the DELETE operation for PostgreSQL nodes,
providing comprehensive delete capabilities with conditional logic and safety checks.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class DeleteOperation(BaseOperation):
    """
    PostgreSQL DELETE operation implementation.
    
    Provides comprehensive DELETE capabilities including:
    - Conditional deletion with WHERE clauses
    - Safety checks to prevent accidental full table deletion
    - Transaction handling
    - RETURNING clause support
    - Cascade options
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a DELETE operation on PostgreSQL.
        
        Args:
            data: Node execution data containing delete parameters
            
        Returns:
            NodeResult: Delete results or error information
        """
        try:
            # Extract parameters
            schema = data.parameters.get('schema', 'public')
            table = data.parameters.get('table')
            where_clause = data.parameters.get('where_clause')
            return_fields = data.parameters.get('return_fields', '*')
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            confirm_delete_all = data.parameters.get('confirm_delete_all', False)
            
            # Validate required parameters
            if not table:
                return NodeResult(error="Table name is required for DELETE operation")
            
            # Validate table name
            if not DeleteOperation.validate_table_name(table):
                return NodeResult(error=f"Invalid table name: {table}")
            
            # Safety check: require WHERE clause or explicit confirmation for full table deletion
            if not where_clause and not confirm_delete_all:
                return NodeResult(
                    error="DELETE operation requires either a WHERE clause or explicit confirmation "
                          "to delete all records. Set 'confirm_delete_all' to true to delete all records."
                )
            
            # Build the DELETE query
            query_builder = DeleteQueryBuilder(schema, table)
            query_builder.set_returning_fields(return_fields)
            
            if where_clause:
                query_builder.set_where_clause(where_clause)
            
            # Build query
            query, params = query_builder.build()
            
            logger.info(f"Executing DELETE query: {query}")
            logger.debug(f"Query parameters: {params}")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction:
                    async with connection.transaction():
                        result_records = await DeleteOperation._execute_delete(
                            connection, query, params, timeout
                        )
                else:
                    result_records = await DeleteOperation._execute_delete(
                        connection, query, params, timeout
                    )
                
                # Format results
                result_data = DeleteOperation.format_results(result_records, "DELETE")
                result_data["deleted_count"] = len(result_records) if result_records else 0
                
                logger.info(f"DELETE operation completed: {result_data['deleted_count']} records deleted")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = DeleteOperation.format_error(e, "DELETE")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    async def _execute_delete(connection, query: str, params: List[Any], timeout: int):
        """
        Execute the DELETE query with proper parameter binding.
        
        Args:
            connection: Database connection
            query: DELETE query string
            params: Query parameters
            timeout: Query timeout
            
        Returns:
            Query results
        """
        if timeout and timeout > 0:
            return await connection.fetch(query, *params, timeout=timeout)
        else:
            return await connection.fetch(query, *params)


class DeleteQueryBuilder:
    """
    Builder class for constructing DELETE queries safely.
    
    Provides a fluent interface for building DELETE queries with
    proper parameter binding and WHERE clause support.
    """
    
    def __init__(self, schema: str, table: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table: Table name
        """
        self.schema = schema
        self.table = table
        self.where_clause = None
        self.returning_fields = '*'
        self.parameters = []
    
    def set_where_clause(self, where_clause: str) -> 'DeleteQueryBuilder':
        """
        Set WHERE clause for conditional deletion.
        
        Args:
            where_clause: WHERE condition
            
        Returns:
            DeleteQueryBuilder: Self for method chaining
        """
        if where_clause and where_clause.strip():
            self.where_clause = where_clause.strip()
        
        return self
    
    def set_returning_fields(self, fields: str) -> 'DeleteQueryBuilder':
        """
        Set fields to return from DELETE.
        
        Args:
            fields: Comma-separated field names or '*'
            
        Returns:
            DeleteQueryBuilder: Self for method chaining
        """
        if fields and fields.strip():
            self.returning_fields = fields.strip()
        
        return self
    
    def build(self) -> tuple[str, List[Any]]:
        """
        Build the DELETE query.
        
        Returns:
            Tuple of (query_string, parameters)
        """
        # Escape table name
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table)
        
        # Build base DELETE query
        query = f"DELETE FROM {escaped_schema}.{escaped_table}"
        
        # Add WHERE clause
        if self.where_clause:
            query += f" WHERE {self.where_clause}"
        
        # Add RETURNING clause
        if self.returning_fields and self.returning_fields != '':
            if self.returning_fields == '*':
                query += " RETURNING *"
            else:
                # Parse and escape returning fields
                return_cols = [col.strip() for col in self.returning_fields.split(',')]
                escaped_return_cols = []
                
                for col in return_cols:
                    if self._is_valid_column_name(col):
                        escaped_return_cols.append(BaseOperation.escape_identifier(col))
                    else:
                        logger.warning(f"Skipping invalid return field: {col}")
                
                if escaped_return_cols:
                    query += f" RETURNING {', '.join(escaped_return_cols)}"
        
        return query, self.parameters
    
    def _is_valid_column_name(self, column: str) -> bool:
        """
        Validate column name for SQL injection prevention.
        
        Args:
            column: Column name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not column or not isinstance(column, str):
            return False
        
        # Allow alphanumeric, underscore, and dots for table.column
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$'
        return bool(re.match(pattern, column))


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute DELETE operation."""
    return await DeleteOperation.execute(data)
