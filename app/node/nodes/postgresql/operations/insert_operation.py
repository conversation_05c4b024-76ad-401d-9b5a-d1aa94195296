"""
PostgreSQL INSERT Operation

This module implements the INSERT operation for PostgreSQL nodes,
providing comprehensive insert capabilities with conflict resolution and batch processing.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Union

from app.node.node_base.node import NodeResult
from app.node.node_base.node_models import NodeData
from .base_operation import BaseOperation, PostgreSQLConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class InsertOperation(BaseOperation):
    """
    PostgreSQL INSERT operation implementation.
    
    Provides comprehensive INSERT capabilities including:
    - Single and batch inserts
    - Conflict resolution (ON CONFLICT)
    - Data validation and type conversion
    - Transaction handling
    - RETURNING clause support
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute an INSERT operation on PostgreSQL.
        
        Args:
            data: Node execution data containing insert parameters
            
        Returns:
            NodeResult: Insert results or error information
        """
        try:
            # Extract parameters
            schema = data.parameters.get('schema', 'public')
            table = data.parameters.get('table')
            insert_data = data.parameters.get('data')
            on_conflict = data.parameters.get('on_conflict', 'error')
            return_fields = data.parameters.get('return_fields', '*')
            use_transaction = data.parameters.get('transaction', True)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not table:
                return NodeResult(error="Table name is required for INSERT operation")
            
            if not insert_data:
                return NodeResult(error="Data is required for INSERT operation")
            
            # Validate table name
            if not InsertOperation.validate_table_name(table):
                return NodeResult(error=f"Invalid table name: {table}")
            
            # Parse and validate insert data
            parsed_data = InsertOperation._parse_insert_data(insert_data)
            if not parsed_data:
                return NodeResult(error="Invalid or empty insert data")
            
            # Determine if this is a batch insert
            is_batch = isinstance(parsed_data, list)
            records_to_insert = parsed_data if is_batch else [parsed_data]
            
            # Build the INSERT query
            query_builder = InsertQueryBuilder(schema, table)
            query_builder.set_conflict_resolution(on_conflict)
            query_builder.set_returning_fields(return_fields)
            
            # Validate all records have the same structure
            if not InsertOperation._validate_record_structure(records_to_insert):
                return NodeResult(error="All records must have the same column structure")
            
            # Build query for the first record to get column structure
            query, param_placeholders = query_builder.build_for_records(records_to_insert)
            
            logger.info(f"Executing INSERT query: {query}")
            logger.debug(f"Inserting {len(records_to_insert)} record(s)")
            
            # Execute query
            async with PostgreSQLConnectionManager.get_connection(data) as connection:
                if use_transaction:
                    async with connection.transaction():
                        result_records = await InsertOperation._execute_insert(
                            connection, query, records_to_insert, timeout
                        )
                else:
                    result_records = await InsertOperation._execute_insert(
                        connection, query, records_to_insert, timeout
                    )
                
                # Format results
                result_data = InsertOperation.format_results(result_records, "INSERT")
                result_data["inserted_count"] = len(records_to_insert)
                
                logger.info(f"INSERT operation completed: {result_data['inserted_count']} records inserted")
                
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_msg = InsertOperation.format_error(e, "INSERT")
            logger.error(error_msg, exc_info=True)
            return NodeResult(error=error_msg)
    
    @staticmethod
    def _parse_insert_data(data: Any) -> Union[Dict[str, Any], List[Dict[str, Any]], None]:
        """
        Parse and validate insert data.
        
        Args:
            data: Raw insert data (dict, list, or JSON string)
            
        Returns:
            Parsed data as dict or list of dicts, or None if invalid
        """
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                logger.error("Invalid JSON format in insert data")
                return None
        
        if isinstance(data, dict):
            return data
        elif isinstance(data, list):
            # Validate that all items are dictionaries
            if all(isinstance(item, dict) for item in data):
                return data
            else:
                logger.error("All items in batch insert must be objects")
                return None
        else:
            logger.error("Insert data must be an object or array of objects")
            return None
    
    @staticmethod
    def _validate_record_structure(records: List[Dict[str, Any]]) -> bool:
        """
        Validate that all records have the same column structure.
        
        Args:
            records: List of records to validate
            
        Returns:
            bool: True if all records have the same structure
        """
        if not records:
            return False
        
        # Get column set from first record
        first_columns = set(records[0].keys())
        
        # Check that all records have the same columns
        for record in records[1:]:
            if set(record.keys()) != first_columns:
                return False
        
        return True
    
    @staticmethod
    async def _execute_insert(connection, query: str, records: List[Dict[str, Any]], timeout: int):
        """
        Execute the INSERT query with proper parameter binding.
        
        Args:
            connection: Database connection
            query: INSERT query string
            records: Records to insert
            timeout: Query timeout
            
        Returns:
            Query results
        """
        if len(records) == 1:
            # Single record insert
            record = records[0]
            values = list(record.values())
            
            if timeout and timeout > 0:
                return await connection.fetch(query, *values, timeout=timeout)
            else:
                return await connection.fetch(query, *values)
        else:
            # Batch insert using executemany
            values_list = [list(record.values()) for record in records]
            
            if timeout and timeout > 0:
                return await connection.fetch(query, *values_list[0], timeout=timeout)
            else:
                # For batch inserts, we need to execute multiple times
                results = []
                for values in values_list:
                    result = await connection.fetch(query, *values)
                    results.extend(result)
                return results


class InsertQueryBuilder:
    """
    Builder class for constructing INSERT queries safely.
    
    Provides a fluent interface for building INSERT queries with
    proper parameter binding and conflict resolution.
    """
    
    def __init__(self, schema: str, table: str):
        """
        Initialize the query builder.
        
        Args:
            schema: Database schema name
            table: Table name
        """
        self.schema = schema
        self.table = table
        self.conflict_resolution = 'error'
        self.returning_fields = '*'
    
    def set_conflict_resolution(self, on_conflict: str) -> 'InsertQueryBuilder':
        """
        Set conflict resolution strategy.
        
        Args:
            on_conflict: Conflict resolution strategy ('error', 'ignore', 'update')
            
        Returns:
            InsertQueryBuilder: Self for method chaining
        """
        if on_conflict in ['error', 'ignore', 'update']:
            self.conflict_resolution = on_conflict
        
        return self
    
    def set_returning_fields(self, fields: str) -> 'InsertQueryBuilder':
        """
        Set fields to return from INSERT.
        
        Args:
            fields: Comma-separated field names or '*'
            
        Returns:
            InsertQueryBuilder: Self for method chaining
        """
        if fields and fields.strip():
            self.returning_fields = fields.strip()
        
        return self
    
    def build_for_records(self, records: List[Dict[str, Any]]) -> tuple[str, List[str]]:
        """
        Build INSERT query for the given records.
        
        Args:
            records: Records to insert
            
        Returns:
            Tuple of (query_string, parameter_placeholders)
        """
        if not records:
            raise ValueError("No records provided for INSERT")
        
        # Get columns from first record
        columns = list(records[0].keys())
        
        # Escape table and column names
        escaped_schema = BaseOperation.escape_identifier(self.schema)
        escaped_table = BaseOperation.escape_identifier(self.table)
        escaped_columns = [BaseOperation.escape_identifier(col) for col in columns]
        
        # Build column list
        columns_clause = f"({', '.join(escaped_columns)})"
        
        # Build VALUES clause with parameter placeholders
        placeholders = [f"${i+1}" for i in range(len(columns))]
        values_clause = f"VALUES ({', '.join(placeholders)})"
        
        # Build base INSERT query
        query = f"INSERT INTO {escaped_schema}.{escaped_table} {columns_clause} {values_clause}"
        
        # Add conflict resolution
        if self.conflict_resolution == 'ignore':
            query += " ON CONFLICT DO NOTHING"
        elif self.conflict_resolution == 'update':
            # For update on conflict, we need to specify which columns to update
            # This is a simplified version - in production you'd want more control
            update_clauses = [f"{col} = EXCLUDED.{col}" for col in escaped_columns]
            query += f" ON CONFLICT DO UPDATE SET {', '.join(update_clauses)}"
        
        # Add RETURNING clause
        if self.returning_fields and self.returning_fields != '':
            if self.returning_fields == '*':
                query += " RETURNING *"
            else:
                # Parse and escape returning fields
                return_cols = [col.strip() for col in self.returning_fields.split(',')]
                escaped_return_cols = [BaseOperation.escape_identifier(col) for col in return_cols]
                query += f" RETURNING {', '.join(escaped_return_cols)}"
        
        return query, placeholders


# Export the execute function for use by the main node
async def execute(data: NodeData) -> NodeResult:
    """Execute INSERT operation."""
    return await InsertOperation.execute(data)
