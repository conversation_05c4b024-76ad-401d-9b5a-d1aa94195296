"""
PostgreSQL Node Implementation

This module implements the PostgreSQL database node for executing database operations
including SELECT, INSERT, UPDATE, DELETE, and custom SQL queries.
"""

import logging
from typing import Dict, Any, Optional

from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import (
    NodeData, 
    NodeRequest, 
    NodeTypeDescription, 
    ValidationResult,
    ValidationError
)
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.postgresql.postgresql_model import PostgreSQLNodeDescription
from app.node.nodes.postgresql.operations import (
    select_operation,
    insert_operation,
    update_operation,
    delete_operation,
    execute_query_operation,
    create_table_operation,
    drop_table_operation
)

# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='postgresql', is_activity=True)
class PostgreSQLNode(Node):
    """
    PostgreSQL Database Node
    
    Provides comprehensive database operations for PostgreSQL including:
    - SELECT queries with filtering, sorting, and pagination
    - INSERT operations with conflict resolution
    - UPDATE operations with conditional logic
    - DELETE operations with safety checks
    - Custom SQL query execution
    - DDL operations (CREATE/DROP TABLE)
    """
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return PostgreSQLNodeDescription.create()
    
    def __init__(self):
        super().__init__()
        self.connection_pool = None

    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the PostgreSQL node's database operations.
        
        Args:
            data: Node execution data containing parameters and credentials
            
        Returns:
            NodeResult: Result of the database operation or error information
        """
        try:
            # await self.get_credential('b0b0b017-c67f-475b-8e2c-23e9b488b47b')  # Example UUID
            # Validate input data
            if not data or not data.parameters:
                return NodeResult(error="Invalid node data: parameters are required")
            
            # Get operation type
            operation = data.parameters.get('operation', 'select')
            if not isinstance(operation, str):
                return NodeResult(error="Operation must be a string")
                    
            # Validate credentials
            if not data.credentials or 'postgresql' not in data.credentials:
                return NodeResult(error="PostgreSQL credentials are required")
            
            # Route to appropriate operation handler
            operation_handlers = {
                'select': select_operation.execute,
                'insert': insert_operation.execute,
                'update': update_operation.execute,
                'delete': delete_operation.execute,
                'execute_query': execute_query_operation.execute,
                'create_table': create_table_operation.execute,
                'drop_table': drop_table_operation.execute
            }
            
            handler = operation_handlers.get(operation)
            if not handler:
                return NodeResult(error=f"Unsupported operation: {operation}")
            
            # Execute the operation
            logger.info(f"Executing PostgreSQL {operation} operation")
            result = await handler(data)
            
            logger.info(f"PostgreSQL {operation} operation completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"PostgreSQL node execution failed: {str(e)}", exc_info=True)
            return NodeResult(error=f"PostgreSQL node execution failed: {str(e)}")
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a PostgreSQL node request.
        
        Args:
            request: The node request to validate
            
        Returns:
            ValidationResult: Validation result with any errors found
        """
        # Start with base validation
        base_result = self.base_validate(request)
        if not base_result.valid:
            return base_result
        
        errors = []
        
        # Validate operation parameter
        operation = request.parameters.get('operation')
        if not operation:
            errors.append(ValidationError(
                parameter="operation",
                message="Operation is required"
            ))
        elif operation not in ['select', 'insert', 'update', 'delete', 'execute_query', 'create_table', 'drop_table']:
            errors.append(ValidationError(
                parameter="operation",
                message=f"Invalid operation: {operation}. Must be one of: select, insert, update, delete, execute_query, create_table, drop_table"
            ))
        
        # Validate credentials
        if not request.credentials or 'postgresql' not in request.credentials:
            errors.append(ValidationError(
                parameter="credentials",
                message="PostgreSQL credentials are required"
            ))
        
        # Operation-specific validation
        if operation and not errors:
            operation_errors = self._validate_operation_parameters(operation, request.parameters)
            errors.extend(operation_errors)
        
        return ValidationResult(
            valid=len(errors) == 0,
            errors=errors if errors else None
        )
    
    def _validate_operation_parameters(self, operation: str, parameters: Dict[str, Any]) -> list[ValidationError]:
        """
        Validate operation-specific parameters.
        
        Args:
            operation: The operation type
            parameters: Request parameters
            
        Returns:
            List of validation errors
        """
        errors = []
        
        if operation in ['select', 'insert', 'update', 'delete']:
            # Table operations require table name
            if not parameters.get('table'):
                errors.append(ValidationError(
                    parameter="table",
                    message="Table name is required for table operations"
                ))
        
        elif operation == 'execute_query':
            # Custom query operations require SQL query
            if not parameters.get('query'):
                errors.append(ValidationError(
                    parameter="query",
                    message="SQL query is required for execute_query operation"
                ))
        
        elif operation in ['create_table', 'drop_table']:
            # DDL operations require table name
            if not parameters.get('table_name'):
                errors.append(ValidationError(
                    parameter="table_name",
                    message="Table name is required for DDL operations"
                ))
        
        return errors
