"""
PostgreSQL Node Model

This module defines the PostgreSQL node description and parameter models
for database operations.
"""

from typing import List, Optional
from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    PropertyTypes,
    PropertyTypeOptions,
    NodeGroupType,
    NodeConnectionType,
    NodeCredentialDescription
)


class PostgreSQLNodeDescription:
    """
    PostgreSQL node description factory.
    
    Provides standardized node description for PostgreSQL database operations
    with comprehensive parameter definitions and validation rules.
    """
    
    @classmethod
    def create(cls) -> NodeTypeDescription:
        """
        Factory method to create a standard PostgreSQL node description.
        
        Returns:
            NodeTypeDescription: A configured description for PostgreSQL nodes
        """
        return NodeTypeDescription(
            name="postgresql",
            display_name="PostgreSQL",
            description="Execute database operations on PostgreSQL including SELECT, INSERT, UPDATE, DELETE, and custom queries",
            icon="🐘",
            icon_color="#336791",
            group=['database'],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            credentials=[
                NodeCredentialDescription(
                    name="postgresql",
                    display_name="PostgreSQL",
                    required=True
                )
            ],
            parameters=[
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="The database operation to perform",
                    type=PropertyTypes.OPTIONS,
                    required=True,
                    default="select",
                    options=[
                        {"name": "Select", "value": "select", "description": "Query data from tables"},
                        {"name": "Insert", "value": "insert", "description": "Insert new records into tables"},
                        {"name": "Update", "value": "update", "description": "Update existing records in tables"},
                        {"name": "Delete", "value": "delete", "description": "Delete records from tables"},
                        {"name": "Execute Query", "value": "execute_query", "description": "Execute custom SQL queries"},
                        # {"name": "Create Table", "value": "create_table", "description": "Create new database tables"}, TODO: Implement create_table operation on version 2.0
                        {"name": "Drop Table", "value": "drop_table", "description": "Drop existing database tables"}
                    ]
                ),
                
                # Table selection parameters (for table operations)
                NodeParameter(
                    name="schema",
                    display_name="Schema",
                    description="Database schema name (default: public)",
                    type=PropertyTypes.STRING,
                    required=False,
                    default="public",
                    display_options={
                        "show": {
                            "operation": ["select", "insert", "update", "delete"]
                        }
                    }
                ),
                NodeParameter(
                    name="table",
                    display_name="Table",
                    description="Database table name",
                    type=PropertyTypes.STRING,
                    required=True,
                    display_options={
                        "show": {
                            "operation": ["select", "insert", "update", "delete"]
                        }
                    }
                ),
                
                # Custom query parameters
                NodeParameter(
                    name="query",
                    display_name="SQL Query",
                    description="Custom SQL query to execute",
                    type=PropertyTypes.STRING,
                    required=True,
                    type_options=PropertyTypeOptions(
                        editor="sql",
                        rows=5
                    ),
                    display_options={
                        "show": {
                            "operation": ["execute_query"]
                        }
                    }
                ),
                NodeParameter(
                    name="query_parameters",
                    display_name="Query Parameters",
                    description="Parameters for parameterized queries (JSON object)",
                    type=PropertyTypes.JSON,
                    required=False,
                    display_options={
                        "show": {
                            "operation": ["execute_query"]
                        }
                    }
                ),
                
                # DDL operation parameters
                NodeParameter(
                    name="table_name",
                    display_name="Table Name",
                    description="Name of the table to create or drop",
                    type=PropertyTypes.STRING,
                    required=True,
                    display_options={
                        "show": {
                            "operation": ["create_table", "drop_table"]
                        }
                    }
                ),
                NodeParameter(
                    name="table_schema",
                    display_name="Table Schema",
                    description="Schema for the table (default: public)",
                    type=PropertyTypes.STRING,
                    required=False,
                    default="public",
                    display_options={
                        "show": {
                            "operation": ["create_table", "drop_table"]
                        }
                    }
                ),
                
                # SELECT operation specific parameters
                NodeParameter(
                    name="columns",
                    display_name="Columns",
                    description="Columns to select (comma-separated, * for all)",
                    type=PropertyTypes.STRING,
                    required=False,
                    default="*",
                    display_options={
                        "show": {
                            "operation": ["select"]
                        }
                    }
                ),
                NodeParameter(
                    name="where_clause",
                    display_name="WHERE Clause",
                    description="WHERE condition for filtering results",
                    type=PropertyTypes.STRING,
                    required=False,
                    display_options={
                        "show": {
                            "operation": ["select", "update", "delete"]
                        }
                    }
                ),
                NodeParameter(
                    name="order_by",
                    display_name="ORDER BY",
                    description="ORDER BY clause for sorting results",
                    type=PropertyTypes.STRING,
                    required=False,
                    display_options={
                        "show": {
                            "operation": ["select"]
                        }
                    }
                ),
                NodeParameter(
                    name="limit",
                    display_name="Limit",
                    description="Maximum number of records to return",
                    type=PropertyTypes.NUMBER,
                    required=False,
                    type_options=PropertyTypeOptions(
                        min_value=1,
                        number_precision=0
                    ),
                    display_options={
                        "show": {
                            "operation": ["select"]
                        }
                    }
                ),
                NodeParameter(
                    name="offset",
                    display_name="Offset",
                    description="Number of records to skip",
                    type=PropertyTypes.NUMBER,
                    required=False,
                    type_options=PropertyTypeOptions(
                        min_value=0,
                        number_precision=0
                    ),
                    display_options={
                        "show": {
                            "operation": ["select"]
                        }
                    }
                ),
                
                # INSERT/UPDATE operation parameters
                NodeParameter(
                    name="data",
                    display_name="Data",
                    description="Data to insert or update (JSON object or array)",
                    type=PropertyTypes.JSON,
                    required=True,
                    display_options={
                        "show": {
                            "operation": ["insert", "update"]
                        }
                    }
                ),
                NodeParameter(
                    name="on_conflict",
                    display_name="On Conflict",
                    description="Action to take when conflicts occur during INSERT",
                    type=PropertyTypes.OPTIONS,
                    required=False,
                    default="error",
                    options=[
                        {"name": "Error", "value": "error", "description": "Raise an error on conflict"},
                        {"name": "Ignore", "value": "ignore", "description": "Ignore conflicting records"},
                        {"name": "Update", "value": "update", "description": "Update conflicting records"}
                    ],
                    display_options={
                        "show": {
                            "operation": ["insert"]
                        }
                    }
                ),
                
                # Connection and execution options
                NodeParameter(
                    name="return_fields",
                    display_name="Return Fields",
                    description="Fields to return from INSERT/UPDATE operations",
                    type=PropertyTypes.STRING,
                    required=False,
                    default="*",
                    display_options={
                        "show": {
                            "operation": ["insert", "update"]
                        }
                    }
                ),
                NodeParameter(
                    name="transaction",
                    display_name="Use Transaction",
                    description="Execute operation within a transaction",
                    type=PropertyTypes.BOOLEAN,
                    required=False,
                    default=True
                ),
                NodeParameter(
                    name="timeout",
                    display_name="Query Timeout",
                    description="Query timeout in seconds (0 for no timeout)",
                    type=PropertyTypes.NUMBER,
                    required=False,
                    default=30,
                    type_options=PropertyTypeOptions(
                        min_value=0,
                        max_value=300,
                        number_precision=0
                    )
                )
            ]
        )
