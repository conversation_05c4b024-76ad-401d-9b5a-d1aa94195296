"""
MongoDB Node Package

This package provides MongoDB database integration for the workflow system.
It includes comprehensive CRUD operations, aggregation pipelines, and
atomic find-and-modify operations.

Features:
- Full CRUD operations (Create, Read, Update, Delete)
- Aggregation pipelines for complex data processing
- Atomic find-and-modify operations
- Document counting with query filtering
- Flexible credential management (connection string or parameters)
- Comprehensive error handling and logging
- Type hints and documentation

Usage:
    The MongoDB node can be used in workflows to perform database operations
    on MongoDB collections. It supports both simple operations like find/insert
    and complex aggregation pipelines.

Example Operations:
    - find: Query documents with filtering, sorting, and projection
    - insert: Insert single or multiple documents
    - update: Update documents with optional upsert
    - delete: Delete documents with safety checks
    - aggregate: Execute aggregation pipelines
    - findOneAndUpdate: Atomically find and update a document
    - findOneAndReplace: Atomically find and replace a document
    - count: Count documents with optional filtering
"""

from .mongodb_node import MongoDBNode
from .mongodb_model import MongoDBNodeDescription

# Export main components
__all__ = [
    'MongoDBNode',
    'MongoDBNodeDescription'
]

# Package metadata
__version__ = '1.0.0'
__author__ = 'Workflow System'
__description__ = 'MongoDB database integration for workflow system'
