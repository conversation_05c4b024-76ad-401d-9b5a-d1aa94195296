"""
MongoDB Node Model

This module defines the MongoDB node description and parameter models
for database operations.
"""

from typing import List, Optional
from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    PropertyTypes,
    PropertyTypeOptions,
    NodeGroupType,
    NodeConnectionType,
    NodeCredentialDescription,
    DisplayOptions,
    NodeParameterOption
)


class MongoDBNodeDescription:
    """
    MongoDB node description factory.
    
    Provides standardized node description for MongoDB database operations
    with comprehensive parameter definitions and validation rules.
    """
    
    @classmethod
    def create(cls) -> NodeTypeDescription:
        """
        Factory method to create a standard MongoDB node description.
        
        Returns:
            NodeTypeDescription: A configured description for MongoDB nodes
        """
        return NodeTypeDescription(
            name="mongodb",
            display_name="MongoDB",
            description="Execute database operations on MongoDB including find, insert, update, delete, and aggregation",
            icon="🍃",
            icon_color="#47A248",
            group=['database'],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main],
            credentials=[
                NodeCredentialDescription(
                    name="mongodb",
                    display_name="MongoDB",
                    required=True
                )
            ],
            parameters=[
                # Operation selection
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="The database operation to perform",
                    type=PropertyTypes.OPTIONS,
                    required=True,
                    default="find",
                    options=[
                        NodeParameterOption(name="Find", value="find", description="Find documents in collection"),
                        NodeParameterOption(name="Insert", value="insert", description="Insert documents into collection"),
                        NodeParameterOption(name="Update", value="update", description="Update documents in collection"),
                        NodeParameterOption(name="Delete", value="delete", description="Delete documents from collection"),
                        NodeParameterOption(name="Aggregate", value="aggregate", description="Perform aggregation pipeline"),
                        NodeParameterOption(name="Find One and Update", value="findOneAndUpdate", description="Find and update a single document"),
                        NodeParameterOption(name="Find One and Replace", value="findOneAndReplace", description="Find and replace a single document"),
                        NodeParameterOption(name="Count", value="count", description="Count documents in collection")
                    ]
                ),
                
                # Collection name (required for all operations)
                NodeParameter(
                    name="collection",
                    display_name="Collection",
                    description="MongoDB collection name",
                    type=PropertyTypes.STRING,
                    required=True,
                    placeholder="users"
                ),
                
                # Query parameters (for find, update, delete operations)
                NodeParameter(
                    name="query",
                    display_name="Query (JSON Format)",
                    description="MongoDB query filter in JSON format",
                    type=PropertyTypes.JSON,
                    required=False,
                    default="{}",
                    placeholder='{"name": "John", "age": {"$gte": 18}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["find", "update", "delete", "findOneAndUpdate", "findOneAndReplace", "count"]
                        }
                    )
                ),
                
                # Aggregation pipeline (for aggregate operation)
                NodeParameter(
                    name="pipeline",
                    display_name="Aggregation Pipeline (JSON Format)",
                    description="MongoDB aggregation pipeline in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="[]",
                    placeholder='[{"$match": {"status": "active"}}, {"$group": {"_id": "$category", "count": {"$sum": 1}}}]',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["aggregate"]
                        }
                    )
                ),
                
                # Document data (for insert operations)
                NodeParameter(
                    name="document",
                    display_name="Document (JSON Format)",
                    description="Document to insert in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"name": "John", "email": "<EMAIL>", "age": 30}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["insert"]
                        }
                    )
                ),
                
                # Update data (for update operations)
                NodeParameter(
                    name="update",
                    display_name="Update (JSON Format)",
                    description="Update operations in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"$set": {"status": "updated"}, "$inc": {"views": 1}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "findOneAndUpdate"]
                        }
                    )
                ),
                
                # Replacement document (for replace operations)
                NodeParameter(
                    name="replacement",
                    display_name="Replacement Document (JSON Format)",
                    description="Replacement document in JSON format",
                    type=PropertyTypes.JSON,
                    required=True,
                    default="{}",
                    placeholder='{"name": "Jane", "email": "<EMAIL>", "age": 25}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["findOneAndReplace"]
                        }
                    )
                ),
                
                # Options for various operations
                NodeParameter(
                    name="options",
                    display_name="Options",
                    description="Additional options for the operation",
                    type=PropertyTypes.JSON,
                    required=False,
                    default="{}",
                    placeholder='{"limit": 10, "sort": {"name": 1}, "projection": {"name": 1, "email": 1}}',
                    display_options=DisplayOptions(
                        show={
                            "operation": ["find", "update", "delete", "findOneAndUpdate", "findOneAndReplace"]
                        }
                    )
                ),
                
                # Upsert option (for update operations)
                NodeParameter(
                    name="upsert",
                    display_name="Upsert",
                    description="Create document if it doesn't exist",
                    type=PropertyTypes.BOOLEAN,
                    required=False,
                    default=False,
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "findOneAndUpdate", "findOneAndReplace"]
                        }
                    )
                ),
                
                # Multi option (for update/delete operations)
                NodeParameter(
                    name="multi",
                    display_name="Update Multiple",
                    description="Update multiple documents that match the query",
                    type=PropertyTypes.BOOLEAN,
                    required=False,
                    default=False,
                    display_options=DisplayOptions(
                        show={
                            "operation": ["update", "delete"]
                        }
                    )
                ),
                
                # Timeout option
                NodeParameter(
                    name="timeout",
                    display_name="Timeout (seconds)",
                    description="Operation timeout in seconds",
                    type=PropertyTypes.NUMBER,
                    required=False,
                    default=30,
                    placeholder="30"
                )
            ]
        )
