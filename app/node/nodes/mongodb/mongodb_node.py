"""
MongoDB Node

This module implements the main MongoDB node class that provides database
operations for MongoDB including CRUD operations and aggregation.
"""

import logging
from typing import Dict, Any, Callable
from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import NodeData, NodeRequest, NodeTypeDescription, ValidationError, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
# from app.node.nodes.mongodb.mongodb_model import MongoDBNodeDescription
from app.node.nodes.wait.wait_model import WaitNodeDescription

from .mongodb_model import MongoDBNodeDescription
from .operations.find_operation import FindOperation
from .operations.insert_operation import InsertOperation
from .operations.update_operation import UpdateOperation
from .operations.delete_operation import DeleteOperation
from .operations.aggregate_operation import AggregateOperation
from .operations.find_and_modify_operation import FindAndModifyOperation
from .operations.count_operation import CountOperation

# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='mongodb', is_activity=True)
class MongoDBNode(Node):
    """
    MongoDB database node implementation.
    
    Provides comprehensive MongoDB database operations including:
    - CRUD operations (Create, Read, Update, Delete)
    - Aggregation pipelines for complex data processing
    - Find and modify operations for atomic updates
    - Document counting with query filtering
    - Proper credential management and error handling
    
    Supports both connection string and individual parameter configurations
    for flexible deployment across different environments.
    """
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return MongoDBNodeDescription.create()
    
    def __init__(self):
        """Initialize MongoDB node with operation handlers."""
        super().__init__()
        # Map operations to their corresponding handler classes
        self.operation_handlers: Dict[str, Callable] = {
            'find': FindOperation.execute,
            'insert': InsertOperation.execute,
            'update': UpdateOperation.execute,
            'delete': DeleteOperation.execute,
            'aggregate': AggregateOperation.execute,
            'findOneAndUpdate': FindAndModifyOperation.execute,
            'findOneAndReplace': FindAndModifyOperation.execute,
            'count': CountOperation.execute
        }
        
    def validate(self, data: NodeRequest) -> ValidationResult:
        """
        Validate the node data before execution.
        
        Args:
            data: Node execution data to validate
            
        Returns:
            ValidationResult: Validation result or error information
        """
        try:
            errors = []
            # Check if operation is provided
            operation = data.parameters.get('operation')
            if not operation:
                errors.append(ValidationError(
                    parameter="operation",
                    message="Operation type is required"
                ))
            else:
                # Validate operation type
                if operation not in self.operation_handlers:
                    available_operations = ', '.join(self.operation_handlers.keys())
                    errors.append(ValidationError(
                        parameter="operation",
                        message=f"Unsupported operation: {operation}. Available operations: {available_operations}"
                    ))
            
            # Check for credentials
            if not data.credentials or 'mongodb' not in data.credentials:
                errors.append(ValidationError(
                    parameter="credentials",
                    message="MongoDB credentials are required"
                ))
            
            # Check for collection name (required for most operations)
            if operation and operation in ['find', 'insert', 'update', 'delete', 'count', 'findOneAndUpdate', 'findOneAndReplace']:
                if not data.parameters.get('collection'):
                    errors.append(ValidationError(
                        parameter="collection",
                        message="Collection name is required for this operation"
                    ))
            
            # Operation-specific validation
            if operation and not errors:
                operation_errors = self._validate_operation_parameters(operation, data.parameters)
                errors.extend(operation_errors)
            
            # Return validation result
            return ValidationResult(
                valid=len(errors) == 0,
                errors=errors if errors else None
            )
            
        except Exception as e:
            error_message = f"MongoDB node validation failed: {str(e)}"
            logger.error(error_message)
            return ValidationResult(
                valid=False,
                errors=[ValidationError(
                    parameter="validation",
                    message=error_message
                )]
            )

    def _validate_operation_parameters(self, operation: str, parameters: Dict[str, Any]) -> list:
        """
        Validate operation-specific parameters.
        
        Args:
            operation: The operation type
            parameters: Request parameters
            
        Returns:
            List of validation errors
        """
        from app.node.node_base.node_models import ValidationError
        
        errors = []
        
        try:
            if operation == 'insert':
                # Insert operations require documents
                if not parameters.get('documents') and not parameters.get('document'):
                    errors.append(ValidationError(
                        parameter="documents",
                        message="Documents or document is required for insert operation"
                    ))
            
            elif operation in ['update', 'findOneAndUpdate']:
                # Update operations require filter and update data
                if not parameters.get('filter'):
                    errors.append(ValidationError(
                        parameter="filter",
                        message="Filter is required for update operations"
                    ))
                if not parameters.get('update'):
                    errors.append(ValidationError(
                        parameter="update",
                        message="Update data is required for update operations"
                    ))
            
            elif operation == 'delete':
                # Delete operations require filter
                if not parameters.get('filter'):
                    errors.append(ValidationError(
                        parameter="filter",
                        message="Filter is required for delete operation"
                    ))
            
            elif operation == 'aggregate':
                # Aggregation operations require pipeline
                if not parameters.get('pipeline'):
                    errors.append(ValidationError(
                        parameter="pipeline",
                        message="Pipeline is required for aggregate operation"
                    ))
            
            elif operation == 'findOneAndReplace':
                # Replace operations require filter and replacement document
                if not parameters.get('filter'):
                    errors.append(ValidationError(
                        parameter="filter",
                        message="Filter is required for findOneAndReplace operation"
                    ))
                if not parameters.get('replacement'):
                    errors.append(ValidationError(
                        parameter="replacement",
                        message="Replacement document is required for findOneAndReplace operation"
                    ))
            
        except Exception as e:
            errors.append(ValidationError(
                parameter="operation_validation",
                message=f"Operation validation failed: {str(e)}"
            ))
        
        return errors
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute MongoDB operation based on the specified operation type.
        
        Args:
            data: Node execution data containing operation parameters and credentials
            
        Returns:
            NodeResult: Operation results or error information
        """
        try:
            # Extract operation type
            operation = data.parameters.get('operation')
            if not operation:
                return NodeResult(error="Operation type is required")
            
            # Validate operation type
            if operation not in self.operation_handlers:
                available_operations = ', '.join(self.operation_handlers.keys())
                return NodeResult(
                    error=f"Unsupported operation: {operation}. "
                          f"Available operations: {available_operations}"
                )
        #    # Validate required parameters
        #     if 'database' not in data.parameters:
        #         return NodeResult(error="Database name is required for MongoDB operations") 
            # Get and validate credentials
            credentials_result = await self._get_credentials(data)
            if credentials_result.error:
                return credentials_result
            
            # Update data with processed credentials
            data.credentials = credentials_result.result
            
            logger.info(f"Executing MongoDB {operation.upper()} operation")
            
            # Execute the operation
            handler = self.operation_handlers[operation]
            result = await handler(data)
            
            if result.error:
                logger.error(f"MongoDB {operation.upper()} operation failed: {result.error}")
            else:
                logger.info(f"MongoDB {operation.upper()} operation completed successfully")
            
            return result
            
        except Exception as e:
            error_message = f"MongoDB node execution failed: {str(e)}"
            logger.error(error_message)
            return NodeResult(error=error_message)
    
    async def _get_credentials(self, data: NodeData) -> NodeResult:
        """
        Extract and validate MongoDB credentials from node data.
        
        Args:
            data: Node execution data
            
        Returns:
            NodeResult: Processed credentials or error information
        """
        try:
            # Check if credentials are provided
            if not data.credentials or 'mongodb' not in data.credentials:
                return NodeResult(error="MongoDB credentials are required")
            
            # Get credential data
            credential_name = data.credentials['mongodb']
            
            # Retrieve credential from credential provider
            credential = await self.get_credential(credential_name.id)
            if not credential:
                return NodeResult(error=f"MongoDB credential '{credential_name}' not found")
            
            # Handle both object and dictionary credential formats
            if hasattr(credential, 'data') and credential.data:
                # Object format with data attribute
                credential_data = credential.data
            elif isinstance(credential, dict):
                # Dictionary format
                credential_data = credential
            else:
                return NodeResult(error="Invalid MongoDB credential format")
            
            # Validate credential data contains required information
            config_type = credential_data.get('configuration_type', 'connection_string')
            
            if config_type == 'connection_string':
                if 'connection_string' not in credential_data or not credential_data['connection_string']:
                    return NodeResult(error="MongoDB credential missing connection string")
            elif config_type == 'parameters':
                required_params = ['host', 'database']
                missing_params = [param for param in required_params if param not in credential_data]
                if missing_params:
                    return NodeResult(error=f"MongoDB credential missing required parameters: {', '.join(missing_params)}")
            else:
                return NodeResult(error=f"Unsupported MongoDB configuration type: {config_type}")
            
            # Return the processed credential data
            return NodeResult(result={'mongodb': credential_data})
            
        except Exception as e:
            error_message = f"Failed to process MongoDB credentials: {str(e)}"
            logger.error(error_message)
            return NodeResult(error=error_message)
    
    def get_supported_operations(self) -> list[str]:
        """
        Get list of supported MongoDB operations.
        
        Returns:
            List of supported operation names
        """
        return list(self.operation_handlers.keys())
    
    def get_operation_description(self, operation: str) -> str:
        """
        Get description for a specific operation.
        
        Args:
            operation: Operation name
            
        Returns:
            Operation description
        """
        descriptions = {
            'find': 'Find documents in collection with optional filtering, sorting, and projection',
            'insert': 'Insert single or multiple documents into collection',
            'update': 'Update documents in collection with optional upsert',
            'delete': 'Delete documents from collection with safety checks',
            'aggregate': 'Execute aggregation pipeline for complex data processing',
            'findOneAndUpdate': 'Atomically find and update a single document',
            'findOneAndReplace': 'Atomically find and replace a single document',
            'count': 'Count documents in collection with optional query filtering'
        }
        
        return descriptions.get(operation, f"MongoDB {operation} operation")
