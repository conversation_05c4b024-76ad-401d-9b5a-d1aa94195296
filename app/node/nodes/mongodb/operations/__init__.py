"""
MongoDB Operations Package

This package contains all MongoDB operation implementations including
CRUD operations, aggregation, and specialized find-and-modify operations.

Each operation is implemented as a separate module following the established
patterns for error handling, result formatting, and connection management.

Available Operations:
    - FindOperation: Query documents with filtering and sorting
    - InsertOperation: Insert single or multiple documents
    - UpdateOperation: Update documents with optional upsert
    - DeleteOperation: Delete documents with safety checks
    - AggregateOperation: Execute aggregation pipelines
    - FindAndModifyOperation: Atomic find-and-modify operations
    - CountOperation: Count documents with optional filtering

Base Classes:
    - BaseOperation: Common functionality for all operations
    - MongoDBConnectionManager: Connection management utilities
"""

from .base_operation import BaseOperation, MongoDBConnectionManager
from .find_operation import FindOperation
from .insert_operation import InsertOperation
from .update_operation import UpdateOperation
from .delete_operation import DeleteOperation
from .aggregate_operation import AggregateOperation
from .find_and_modify_operation import FindAndModifyOperation
from .count_operation import CountOperation

# # Export all operation classes
__all__ = [
    'BaseOperation',
    'MongoDBConnectionManager',
    'FindOperation',
    'InsertOperation',
    'UpdateOperation',
    'DeleteOperation',
    'AggregateOperation',
    'FindAndModifyOperation',
    'CountOperation'
]
