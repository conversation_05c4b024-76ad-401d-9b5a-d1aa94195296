"""
MongoDB Find and Modify Operations

This module implements MongoDB find and modify operations including
findOneAndUpdate, findOneAndReplace, and findOneAndDelete.
"""

import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class FindAndModifyOperation(BaseOperation):
    """
    MongoDB FIND AND MODIFY operations implementation.
    
    Provides atomic find and modify operations including:
    - findOneAndUpdate: Find and update a single document
    - findOneAndReplace: Find and replace a single document
    - findOneAndDelete: Find and delete a single document
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a FIND AND MODIFY operation on MongoDB.
        
        Args:
            data: Node execution data containing operation parameters
            
        Returns:
            NodeResult: Operation results or error information
        """
        try:
            # Extract parameters
            operation = data.parameters.get('operation')
            collection_name = data.parameters.get('collection')
            query = data.parameters.get('query', '{}')
            options = data.parameters.get('options', '{}')
            upsert = data.parameters.get('upsert', False)
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for FIND AND MODIFY operation")
            
            if not FindAndModifyOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_query = FindAndModifyOperation.parse_json_parameter(query, 'query')
                parsed_options = FindAndModifyOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Operation-specific parameter extraction and validation
            if operation == 'findOneAndUpdate':
                update = data.parameters.get('update', '{}')
                try:
                    parsed_update = FindAndModifyOperation.parse_json_parameter(update, 'update')
                except ValueError as e:
                    return NodeResult(error=str(e))
                
                if not parsed_update:
                    return NodeResult(error="Update data is required for findOneAndUpdate operation")
                
                # If no update operators are present, wrap in $set
                if not any(key.startswith('$') for key in parsed_update.keys()):
                    parsed_update = {'$set': parsed_update}
                
                return await FindAndModifyOperation._execute_find_one_and_update(
                    data, collection_name, parsed_query, parsed_update, parsed_options, upsert
                )
            
            elif operation == 'findOneAndReplace':
                replacement = data.parameters.get('replacement', '{}')
                try:
                    parsed_replacement = FindAndModifyOperation.parse_json_parameter(replacement, 'replacement')
                except ValueError as e:
                    return NodeResult(error=str(e))
                
                if not parsed_replacement:
                    return NodeResult(error="Replacement document is required for findOneAndReplace operation")
                
                return await FindAndModifyOperation._execute_find_one_and_replace(
                    data, collection_name, parsed_query, parsed_replacement, parsed_options, upsert
                )
            
            else:
                return NodeResult(error=f"Unsupported find and modify operation: {operation}")
                
        except Exception as e:
            error_message = FindAndModifyOperation.format_error(e, f"FIND_AND_MODIFY_{operation.upper()}")
            logger.error(f"MongoDB {operation} operation failed: {error_message}")
            return NodeResult(error=error_message)
    
    @staticmethod
    async def _execute_find_one_and_update(
        data: NodeData, 
        collection_name: str, 
        query: Dict[str, Any], 
        update: Dict[str, Any], 
        options: Dict[str, Any], 
        upsert: bool
    ) -> NodeResult:
        """Execute findOneAndUpdate operation."""
        
        logger.info(f"Executing MongoDB findOneAndUpdate on collection: {collection_name}")
        logger.debug(f"Query: {query}")
        logger.debug(f"Update: {update}")
        
        async with MongoDBConnectionManager.get_connection(data) as database:
            collection = database[collection_name]
            
            # Extract options
            projection = options.get('projection')
            sort = options.get('sort')
            return_document = options.get('returnDocument', 'before')  # 'before' or 'after'
            array_filters = options.get('arrayFilters')
            
            # Prepare operation options
            op_options = {
                'upsert': upsert,
                'return_document': return_document == 'after'  # pymongo uses boolean
            }
            
            if projection:
                op_options['projection'] = projection
            
            if sort:
                # Convert sort dict to list of tuples for pymongo
                sort_list = [(field, direction) for field, direction in sort.items()]
                op_options['sort'] = sort_list
            
            if array_filters:
                op_options['array_filters'] = array_filters
            
            # Execute operation
            result = await collection.find_one_and_update(query, update, **op_options)
            
            # Convert ObjectId to string if present
            if result and '_id' in result:
                result['_id'] = str(result['_id'])
            
            # Format results
            result_data = FindAndModifyOperation.format_results(
                {"document": result, "modified": result is not None},
                "findOneAndUpdate",
                collection=collection_name,
                query=query,
                update=update,
                upsert=upsert,
                return_document=return_document
            )
            
            logger.info(f"MongoDB findOneAndUpdate completed successfully")
            return NodeResult(result=result_data)
    
    @staticmethod
    async def _execute_find_one_and_replace(
        data: NodeData, 
        collection_name: str, 
        query: Dict[str, Any], 
        replacement: Dict[str, Any], 
        options: Dict[str, Any], 
        upsert: bool
    ) -> NodeResult:
        """Execute findOneAndReplace operation."""
        
        logger.info(f"Executing MongoDB findOneAndReplace on collection: {collection_name}")
        logger.debug(f"Query: {query}")
        logger.debug(f"Replacement: {replacement}")
        
        async with MongoDBConnectionManager.get_connection(data) as database:
            collection = database[collection_name]
            
            # Extract options
            projection = options.get('projection')
            sort = options.get('sort')
            return_document = options.get('returnDocument', 'before')  # 'before' or 'after'
            
            # Prepare operation options
            op_options = {
                'upsert': upsert,
                'return_document': return_document == 'after'  # pymongo uses boolean
            }
            
            if projection:
                op_options['projection'] = projection
            
            if sort:
                # Convert sort dict to list of tuples for pymongo
                sort_list = [(field, direction) for field, direction in sort.items()]
                op_options['sort'] = sort_list
            
            # Execute operation
            result = await collection.find_one_and_replace(query, replacement, **op_options)
            
            # Convert ObjectId to string if present
            if result and '_id' in result:
                result['_id'] = str(result['_id'])
            
            # Format results
            result_data = FindAndModifyOperation.format_results(
                {"document": result, "replaced": result is not None},
                "findOneAndReplace",
                collection=collection_name,
                query=query,
                replacement=replacement,
                upsert=upsert,
                return_document=return_document
            )
            
            logger.info(f"MongoDB findOneAndReplace completed successfully")
            return NodeResult(result=result_data)
