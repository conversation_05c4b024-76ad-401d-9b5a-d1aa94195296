"""
MongoDB Find Operation

This module implements the MongoDB find operation for querying documents
from collections with support for filtering, sorting, projection, and pagination.
"""

from datetime import datetime
import logging
from typing import Dict, Any, List, Optional

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class FindOperation(BaseOperation):
    """
    MongoDB FIND operation implementation.
    
    Provides comprehensive document querying capabilities including:
    - Query filtering with MongoDB query operators
    - Field projection for selective data retrieval
    - Sorting with multiple fields and directions
    - Pagination with limit and skip
    - Proper error handling and result formatting
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute a FIND operation on MongoDB.
        
        Args:
            data: Node execution data containing query parameters
            
        Returns:
            NodeResult: Query results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            query = data.parameters.get('query', '{}')
            options = data.parameters.get('options', '{}')
            timeout = data.parameters.get('timeout', 30)
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for FIND operation")
            
            if not FindOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_query = FindOperation.parse_json_parameter(query, 'query')
                parsed_options = FindOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Extract options
            limit = parsed_options.get('limit', 0)
            skip = parsed_options.get('skip', 0)
            sort = parsed_options.get('sort', {})
            projection = parsed_options.get('projection', {})
            
            # Validate numeric options
            if not isinstance(limit, int) or limit < 0:
                return NodeResult(error="Limit must be a non-negative integer")
            
            if not isinstance(skip, int) or skip < 0:
                return NodeResult(error="Skip must be a non-negative integer")
            
            logger.info(f"Executing MongoDB FIND on collection: {collection_name}")
            logger.debug(f"Query: {parsed_query}")
            logger.debug(f"Options: {parsed_options}")
            
            # Execute query
            async with MongoDBConnectionManager.get_connection(data) as database:
                
                collection = database[collection_name]
                # Build the cursor
                cursor = collection.find(parsed_query)
                # Apply projection if specified
                if projection:
                    cursor = cursor.project(projection)
                
                # Apply sorting if specified
                if sort:
                    # Convert sort dict to list of tuples for pymongo
                    sort_list = [(field, direction) for field, direction in sort.items()]
                    cursor = cursor.sort(sort_list)
                
                # Apply skip if specified
                if skip > 0:
                    cursor = cursor.skip(skip)
                
                # Apply limit if specified
                if limit > 0:
                    cursor = cursor.limit(limit)
                
                # Execute query and convert to list
                documents = await cursor.to_list(length=None)
                
                # Convert ObjectId to string and datetime to ISO format for JSON serialization
                for doc in documents:
                    for key, value in doc.items():
                        if key == '_id':
                            doc[key] = str(value)
                        elif isinstance(value, datetime):
                            doc[key] = value.isoformat()
                # Format results
                result_data = FindOperation.format_results(
                    documents, 
                    "find",
                    collection=collection_name,
                    query=parsed_query,
                    options=parsed_options
                )
                
                logger.info(f"MongoDB FIND completed successfully. Found {len(documents)} documents")
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = FindOperation.format_error(e, "FIND")
            logger.error(f"MongoDB FIND operation failed: {error_message}")
            return NodeResult(error=error_message)
