"""
MongoDB Insert Operation

This module implements the MongoDB insert operation for adding documents
to collections with support for single and bulk inserts.
"""

import logging
from typing import Dict, Any, List, Optional, Union

from app.node.node_base.node_models import NodeData
from app.node.node_base.node import NodeResult
from .base_operation import BaseOperation, MongoDBConnectionManager

# Set up logging
logger = logging.getLogger(__name__)


class InsertOperation(BaseOperation):
    """
    MongoDB INSERT operation implementation.
    
    Provides comprehensive document insertion capabilities including:
    - Single document insertion
    - Bulk document insertion
    - Ordered and unordered bulk operations
    - Proper error handling for duplicate keys
    - Result formatting with inserted IDs
    """
    
    @staticmethod
    async def execute(data: NodeData) -> NodeResult:
        """
        Execute an INSERT operation on MongoDB.
        
        Args:
            data: Node execution data containing insert parameters
            
        Returns:
            NodeResult: Insert results or error information
        """
        try:
            # Extract parameters
            collection_name = data.parameters.get('collection')
            document = data.parameters.get('document', '{}')
            options = data.parameters.get('options', '{}')
            timeout = data.parameters.get('timeout', 30)
            
            # Validate required parameters
            if not collection_name:
                return NodeResult(error="Collection name is required for INSERT operation")
            
            if not InsertOperation.validate_collection_name(collection_name):
                return NodeResult(error=f"Invalid collection name: {collection_name}")
            
            # Parse JSON parameters
            try:
                parsed_document = InsertOperation.parse_json_parameter(document, 'document')
                parsed_options = InsertOperation.parse_json_parameter(options, 'options')
            except ValueError as e:
                return NodeResult(error=str(e))
            
            # Validate document parameter
            if not parsed_document:
                return NodeResult(error="Document data is required for INSERT operation")
            
            # Determine if this is a single document or bulk insert
            is_bulk_insert = isinstance(parsed_document, list)
            documents_to_insert = parsed_document if is_bulk_insert else [parsed_document]
            
            # Validate all documents are dictionaries
            for i, doc in enumerate(documents_to_insert):
                if not isinstance(doc, dict):
                    return NodeResult(error=f"Document at index {i} must be a valid JSON object")
            
            # Extract options
            ordered = parsed_options.get('ordered', True)
            bypass_document_validation = parsed_options.get('bypass_document_validation', False)
            
            logger.info(f"Executing MongoDB INSERT on collection: {collection_name}")
            logger.debug(f"Inserting {len(documents_to_insert)} document(s)")
            logger.debug(f"Bulk insert: {is_bulk_insert}, Ordered: {ordered}")
            
            # Execute insert operation
            async with MongoDBConnectionManager.get_connection(data) as database:
                collection = database[collection_name]
                
                if is_bulk_insert:
                    # Bulk insert
                    if len(documents_to_insert) == 1:
                        # Single document insert (even if passed as array)
                        result = await collection.insert_one(
                            documents_to_insert[0],
                            bypass_document_validation=bypass_document_validation
                        )
                        insert_result = {
                            "inserted_ids": [str(result.inserted_id)],
                            "inserted_count": 1
                        }
                    else:
                        # Multiple documents insert
                        result = await collection.insert_many(
                            documents_to_insert,
                            ordered=ordered,
                            bypass_document_validation=bypass_document_validation
                        )
                        insert_result = {
                            "inserted_ids": [str(id) for id in result.inserted_ids],
                            "inserted_count": len(result.inserted_ids)
                        }
                else:
                    # Single document insert
                    result = await collection.insert_one(
                        documents_to_insert[0],
                        bypass_document_validation=bypass_document_validation
                    )
                    insert_result = {
                        "inserted_ids": [str(result.inserted_id)],
                        "inserted_count": 1
                    }
                
                # Format results
                result_data = InsertOperation.format_results(
                    insert_result,
                    "insert",
                    collection=collection_name,
                    bulk_insert=is_bulk_insert,
                    ordered=ordered if is_bulk_insert else None
                )
                
                logger.info(f"MongoDB INSERT completed successfully. Inserted {insert_result['inserted_count']} document(s)")
                return NodeResult(result=result_data)
                
        except Exception as e:
            error_message = InsertOperation.format_error(e, "INSERT")
            logger.error(f"MongoDB INSERT operation failed: {error_message}")
            return NodeResult(error=error_message)
