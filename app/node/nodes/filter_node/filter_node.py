"""
Filter Node Implementation

This module implements the filter node functionality for filtering workflow items
based on various criteria including simple conditions, multiple conditions,
and custom expressions.
"""

import json
from typing import Any, Dict, List, Tuple

from app.node.node_base.node import Node, NodeResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node_models import (
    NodeData, 
    NodeTypeDescription, 
    NodeRequest, 
    ValidationResult,
    ValidationError
)
from app.node.nodes.filter_node.filter_model import FilterNodeDescription
from app.node.nodes.filter_node.filter_utils import (
    evaluate_condition,
    evaluate_multiple_conditions,
    safe_eval_expression,
    get_nested_value
)


@node_defn(type='filter', is_activity=False)
class FilterNode(Node):
    """
    Filter Node for data filtering in workflows.
    
    This node filters workflow items based on configurable conditions,
    supporting simple conditions, multiple conditions with AND/OR logic,
    and custom Python expressions.
    """

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return FilterNodeDescription.create()
    
    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Filter node's filtering logic.
        
        Args:
            data: Node execution data containing parameters
            
        Returns:
            NodeResult with filtered data and appropriate connection index
        """
        self.data = data
        if not self.data or not self.data.parameters:
            return NodeResult(error="Invalid node data")

        try:
            # Get filter mode
            filter_mode = str(self.data.parameters.get('filter_mode', 'simple'))
            
            # Get input data
            input_data = self._get_input_data()
            
            if not isinstance(input_data, list):
                return NodeResult(error="Input data must be a list of items")
            
            if len(input_data) == 0:
                return NodeResult(
                    result={
                        "kept": [],
                        "discarded": [],
                        "total_items": 0,
                        "kept_count": 0,
                        "discarded_count": 0
                    },
                    next_connection_index=0
                )
            
            # Filter data based on mode
            if filter_mode == 'simple':
                kept_items, discarded_items = await self._filter_simple_condition(input_data)
            elif filter_mode == 'multiple':
                kept_items, discarded_items = await self._filter_multiple_conditions(input_data)
            elif filter_mode == 'expression':
                kept_items, discarded_items = await self._filter_by_expression(input_data)
            else:
                return NodeResult(error=f"Unknown filter mode: {filter_mode}")
            
            # Prepare result
            result = {
                "kept": kept_items,
                "discarded": discarded_items,
                "total_items": len(input_data),
                "kept_count": len(kept_items),
                "discarded_count": len(discarded_items)
            }
            
            # Determine next connection index based on whether we have kept items
            # 0 = kept items output, 1 = discarded items output
            next_connection_index = 0 if len(kept_items) > 0 else 1
            
            return NodeResult(
                result=result,
                next_connection_index=next_connection_index
            )
            
        except Exception as e:
            return NodeResult(error=f"Filter error: {str(e)}")
    
    def _get_input_data(self) -> List[Dict[str, Any]]:
        """
        Get input data from node parameters.
        In a real workflow, this would come from the previous node's output.
        """
        input_data = self.data.parameters.get('input_data', [])
        
        # Handle string JSON input
        if isinstance(input_data, str) and input_data.strip():
            try:
                input_data = json.loads(input_data)
            except json.JSONDecodeError:
                input_data = []
        
        # Ensure it's a list
        if not isinstance(input_data, list):
            input_data = []
            
        return input_data
    
    async def _filter_simple_condition(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Filter items using a simple condition."""
        field_name = str(self.data.parameters.get('field_name', ''))
        operation = str(self.data.parameters.get('operation', 'equal'))
        filter_value = self.data.parameters.get('filter_value', '')
        
        # Get options
        case_sensitive = bool(self.data.parameters.get('case_sensitive', True))
        ignore_whitespace = bool(self.data.parameters.get('ignore_whitespace', False))
        
        if not field_name:
            raise ValueError("Field name is required for simple condition filtering")
        
        kept_items = []
        discarded_items = []
        
        condition = {
            'field_name': field_name,
            'operation': operation,
            'filter_value': filter_value
        }
        
        for item in items:
            try:
                if evaluate_condition(item, condition, case_sensitive, ignore_whitespace):
                    kept_items.append(item)
                else:
                    discarded_items.append(item)
            except Exception as e:
                # If evaluation fails, discard the item
                discarded_items.append(item)
        
        return kept_items, discarded_items
    
    async def _filter_multiple_conditions(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Filter items using multiple conditions."""
        conditions = self.data.parameters.get('conditions', [])
        combine_operation = str(self.data.parameters.get('combine_operation', 'AND'))

        # Handle JSON string input for conditions
        if isinstance(conditions, str) and conditions.strip():
            try:
                conditions = json.loads(conditions)
            except json.JSONDecodeError:
                conditions = []

        # Get options
        case_sensitive = bool(self.data.parameters.get('case_sensitive', True))
        ignore_whitespace = bool(self.data.parameters.get('ignore_whitespace', False))

        if not conditions:
            raise ValueError("At least one condition is required for multiple condition filtering")
        
        kept_items = []
        discarded_items = []
        
        for item in items:
            try:
                if evaluate_multiple_conditions(item, conditions, combine_operation, case_sensitive, ignore_whitespace):
                    kept_items.append(item)
                else:
                    discarded_items.append(item)
            except Exception as e:
                # If evaluation fails, discard the item
                discarded_items.append(item)
        
        return kept_items, discarded_items
    
    async def _filter_by_expression(self, items: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Filter items using a custom Python expression."""
        expression = str(self.data.parameters.get('expression', ''))
        
        if not expression:
            raise ValueError("Expression is required for expression-based filtering")
        
        kept_items = []
        discarded_items = []
        
        for item in items:
            try:
                if safe_eval_expression(expression, item):
                    kept_items.append(item)
                else:
                    discarded_items.append(item)
            except Exception as e:
                # If evaluation fails, discard the item
                discarded_items.append(item)
        
        return kept_items, discarded_items
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate a filter node request.

        Args:
            request: The node request to validate

        Returns:
            ValidationResult: The validation result with any errors found
        """
        # Start with base validation
        result = self.base_validate(request)
        errors = result.errors.copy() if result.errors else []

        # Get filter mode
        filter_mode = request.parameters.get('filter_mode', 'simple')

        # Validate filter mode
        valid_modes = ['simple', 'multiple', 'expression']
        if filter_mode not in valid_modes:
            errors.append(ValidationError(parameter="filter_mode", message=f"Invalid filter mode. Must be one of: {', '.join(valid_modes)}"))
            return ValidationResult(valid=False, errors=errors)

        # Validate based on filter mode
        if filter_mode == 'simple':
            errors.extend(self._validate_simple_mode(request))
        elif filter_mode == 'multiple':
            errors.extend(self._validate_multiple_mode(request))
        elif filter_mode == 'expression':
            errors.extend(self._validate_expression_mode(request))

        return ValidationResult(valid=len(errors) == 0, errors=errors)

    def _validate_simple_mode(self, request: NodeRequest) -> List[ValidationError]:
        """Validate simple condition mode parameters."""
        errors = []

        # Validate field name
        field_name = request.parameters.get('field_name')
        if not field_name or (isinstance(field_name, str) and not field_name.strip()):
            errors.append(ValidationError(parameter="field_name", message="Field name is required for simple condition filtering"))

        # Validate operation
        operation = request.parameters.get('operation', 'equal')
        valid_operations = [
            'equal', 'notEqual', 'greaterThan', 'lessThan', 'greaterThanOrEqual',
            'lessThanOrEqual', 'contains', 'notContains', 'startsWith', 'endsWith',
            'isEmpty', 'isNotEmpty', 'regex'
        ]

        if operation not in valid_operations:
            errors.append(ValidationError(parameter="operation", message=f"Invalid operation. Must be one of: {', '.join(valid_operations)}"))

        # Validate filter value for operations that require it
        operations_requiring_value = [
            'equal', 'notEqual', 'greaterThan', 'lessThan', 'greaterThanOrEqual',
            'lessThanOrEqual', 'contains', 'notContains', 'startsWith', 'endsWith', 'regex'
        ]

        if operation in operations_requiring_value:
            filter_value = request.parameters.get('filter_value')
            if filter_value is None or (isinstance(filter_value, str) and filter_value == ''):
                errors.append(ValidationError(parameter="filter_value", message=f"Filter value is required for operation '{operation}'"))

        return errors

    def _validate_multiple_mode(self, request: NodeRequest) -> List[ValidationError]:
        """Validate multiple conditions mode parameters."""
        errors = []

        # Validate conditions list
        conditions = request.parameters.get('conditions', [])

        # Handle JSON string input for conditions
        if isinstance(conditions, str) and conditions.strip():
            try:
                conditions = json.loads(conditions)
            except json.JSONDecodeError:
                errors.append(ValidationError(parameter="conditions", message="Invalid JSON format for conditions"))
                return errors

        if not conditions or not isinstance(conditions, list):
            errors.append(ValidationError(parameter="conditions", message="At least one condition is required for multiple condition filtering"))
            return errors

        # Validate combine operation
        combine_operation = request.parameters.get('combine_operation', 'AND')
        if combine_operation not in ['AND', 'OR']:
            errors.append(ValidationError(parameter="combine_operation", message="Combine operation must be 'AND' or 'OR'"))

        # Validate each condition
        for i, condition in enumerate(conditions):
            if not isinstance(condition, dict):
                errors.append(ValidationError(parameter=f"conditions[{i}]", message="Each condition must be a dictionary"))
                continue

            # Validate field name for each condition
            field_name = condition.get('field_name')
            if not field_name or (isinstance(field_name, str) and not field_name.strip()):
                errors.append(ValidationError(parameter=f"conditions[{i}].field_name", message="Field name is required for each condition"))

            # Validate operation for each condition
            operation = condition.get('operation', 'equal')
            valid_operations = [
                'equal', 'notEqual', 'greaterThan', 'lessThan', 'greaterThanOrEqual',
                'lessThanOrEqual', 'contains', 'notContains', 'startsWith', 'endsWith',
                'isEmpty', 'isNotEmpty', 'regex'
            ]

            if operation not in valid_operations:
                errors.append(ValidationError(parameter=f"conditions[{i}].operation", message=f"Invalid operation in condition {i+1}"))

            # Validate filter value for operations that require it
            operations_requiring_value = [
                'equal', 'notEqual', 'greaterThan', 'lessThan', 'greaterThanOrEqual',
                'lessThanOrEqual', 'contains', 'notContains', 'startsWith', 'endsWith', 'regex'
            ]

            if operation in operations_requiring_value:
                filter_value = condition.get('filter_value')
                if filter_value is None or (isinstance(filter_value, str) and filter_value == ''):
                    errors.append(ValidationError(parameter=f"conditions[{i}].filter_value", message=f"Filter value is required for condition {i+1} with operation '{operation}'"))

        return errors

    def _validate_expression_mode(self, request: NodeRequest) -> List[ValidationError]:
        """Validate expression mode parameters."""
        errors = []

        # Validate expression
        expression = request.parameters.get('expression')
        if not expression or (isinstance(expression, str) and not expression.strip()):
            errors.append(ValidationError(parameter="expression", message="Expression is required for expression-based filtering"))
        elif isinstance(expression, str):
            # Basic syntax validation
            if 'return' not in expression:
                errors.append(ValidationError(parameter="expression", message="Expression must contain a 'return' statement"))

            # Check for potentially dangerous operations
            dangerous_keywords = ['import', 'exec', 'eval', 'open', 'file', '__']
            for keyword in dangerous_keywords:
                if keyword in expression:
                    errors.append(ValidationError(parameter="expression", message=f"Expression contains potentially dangerous keyword: '{keyword}'"))

        return errors
