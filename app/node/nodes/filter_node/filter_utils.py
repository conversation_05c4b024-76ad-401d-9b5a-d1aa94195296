"""
Filter Node Utility Functions

This module contains utility functions for the Filter node including
condition evaluation, data type conversion, and value comparison operations.
"""

import re
from typing import Any, Dict, List, Union
from decimal import Decimal


def get_nested_value(data: Dict[str, Any], field_path: str) -> Any:
    """
    Get a value from a nested dictionary using dot notation.
    
    Args:
        data: The dictionary to search in
        field_path: The field path using dot notation (e.g., 'user.profile.name')
        
    Returns:
        The value at the specified path, or None if not found
    """
    if not field_path:
        return None
        
    keys = field_path.split('.')
    current = data
    
    for key in keys:
        if isinstance(current, dict) and key in current:
            current = current[key]
        else:
            return None
            
    return current


def process_value(value: Any, case_sensitive: bool = True, ignore_whitespace: bool = False) -> Any:
    """
    Process a value for comparison by applying case sensitivity and whitespace options.
    
    Args:
        value: The value to process
        case_sensitive: Whether to preserve case for string values
        ignore_whitespace: Whether to strip whitespace from string values
        
    Returns:
        The processed value
    """
    if isinstance(value, str):
        processed = value
        
        if ignore_whitespace:
            processed = processed.strip()
            
        if not case_sensitive:
            processed = processed.lower()
            
        return processed
    
    return value


def convert_value_type(value: Any, target_type: str = "auto") -> Any:
    """
    Convert a value to the specified type for comparison.
    
    Args:
        value: The value to convert
        target_type: The target type ('string', 'number', 'boolean', 'auto')
        
    Returns:
        The converted value
    """
    if target_type == "auto":
        # Try to infer the best type
        if isinstance(value, (int, float, Decimal)):
            return float(value)
        elif isinstance(value, bool):
            return value
        elif isinstance(value, str):
            # Try to convert to number if it looks like one
            try:
                if '.' in value:
                    return float(value)
                else:
                    return int(value)
            except (ValueError, TypeError):
                return value
        else:
            return value
    
    elif target_type == "string":
        return str(value) if value is not None else ""
    
    elif target_type == "number":
        try:
            if isinstance(value, str) and '.' in value:
                return float(value)
            else:
                return float(value)
        except (ValueError, TypeError):
            return 0
    
    elif target_type == "boolean":
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(value, (int, float)):
            return bool(value)
        else:
            return bool(value)
    
    return value


def compare_values(val1: Any, val2: Any, operation: str) -> bool:
    """
    Compare two values using the specified operation.
    
    Args:
        val1: The first value (from the data item)
        val2: The second value (the filter value)
        operation: The comparison operation
        
    Returns:
        True if the comparison is successful, False otherwise
    """
    try:
        if operation == 'equal':
            return val1 == val2
        elif operation == 'notEqual':
            return val1 != val2
        elif operation == 'greaterThan':
            return val1 > val2
        elif operation == 'lessThan':
            return val1 < val2
        elif operation == 'greaterThanOrEqual':
            return val1 >= val2
        elif operation == 'lessThanOrEqual':
            return val1 <= val2
        elif operation == 'contains':
            return str(val2) in str(val1)
        elif operation == 'notContains':
            return str(val2) not in str(val1)
        elif operation == 'startsWith':
            return str(val1).startswith(str(val2))
        elif operation == 'endsWith':
            return str(val1).endswith(str(val2))
        elif operation == 'isEmpty':
            return not val1 or (isinstance(val1, str) and val1.strip() == '')
        elif operation == 'isNotEmpty':
            return bool(val1) and not (isinstance(val1, str) and val1.strip() == '')
        elif operation == 'regex':
            return bool(re.search(str(val2), str(val1)))
        else:
            raise ValueError(f"Unknown operation: {operation}")
            
    except Exception as e:
        raise ValueError(f"Comparison failed for operation '{operation}': {str(e)}")


def evaluate_condition(item: Dict[str, Any], condition: Dict[str, Any], 
                      case_sensitive: bool = True, ignore_whitespace: bool = False) -> bool:
    """
    Evaluate a single condition against a data item.
    
    Args:
        item: The data item to evaluate
        condition: The condition configuration
        case_sensitive: Whether string comparisons should be case sensitive
        ignore_whitespace: Whether to ignore whitespace in string comparisons
        
    Returns:
        True if the condition is met, False otherwise
    """
    field_name = condition.get('field_name', '')
    operation = condition.get('operation', 'equal')
    filter_value = condition.get('filter_value', '')
    
    # Get the field value from the item
    field_value = get_nested_value(item, field_name)
    
    # Process both values
    processed_field_value = process_value(field_value, case_sensitive, ignore_whitespace)
    processed_filter_value = process_value(filter_value, case_sensitive, ignore_whitespace)
    
    # For numeric comparisons, try to convert both values to numbers
    if operation in ['greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual']:
        try:
            processed_field_value = convert_value_type(processed_field_value, "number")
            processed_filter_value = convert_value_type(processed_filter_value, "number")
        except:
            # If conversion fails, keep original values
            pass
    
    # Perform the comparison
    return compare_values(processed_field_value, processed_filter_value, operation)


def evaluate_multiple_conditions(item: Dict[str, Any], conditions: List[Dict[str, Any]], 
                                combine_operation: str = "AND", case_sensitive: bool = True, 
                                ignore_whitespace: bool = False) -> bool:
    """
    Evaluate multiple conditions against a data item.
    
    Args:
        item: The data item to evaluate
        conditions: List of condition configurations
        combine_operation: How to combine conditions ('AND' or 'OR')
        case_sensitive: Whether string comparisons should be case sensitive
        ignore_whitespace: Whether to ignore whitespace in string comparisons
        
    Returns:
        True if the combined conditions are met, False otherwise
    """
    if not conditions:
        return True
    
    results = []
    for condition in conditions:
        if isinstance(condition, dict):
            result = evaluate_condition(item, condition, case_sensitive, ignore_whitespace)
            results.append(result)
    
    if combine_operation == 'AND':
        return all(results)
    else:  # OR
        return any(results)


def safe_eval_expression(expression: str, item: Dict[str, Any]) -> bool:
    """
    Safely evaluate a Python expression for filtering.

    Args:
        expression: The Python expression to evaluate
        item: The data item to make available in the expression

    Returns:
        True if the item should be kept, False if it should be discarded
    """
    try:
        # Create a safe execution environment
        safe_globals = {
            "__builtins__": {},
            "len": len,
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "abs": abs,
            "min": min,
            "max": max,
            "sum": sum,
            "any": any,
            "all": all,
            "isinstance": isinstance,
            "type": type,
            "item": item,
        }

        # Use eval for simple expressions
        result = eval(expression, safe_globals)
        return bool(result)

    except Exception as e:
        # If expression fails, discard the item by default
        raise ValueError(f"Expression evaluation failed: {str(e)}")
