"""
Filter Node Model

This module defines the model for the filter node, which provides data filtering
functionality for workflow items based on configurable conditions and criteria.
"""

from typing import ClassVar

from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    NodeParameterOption,
    PropertyTypes,
    NodeConnectionType,
    DisplayOptions,
    PropertyTypeOptions
)


class FilterNodeDescription(NodeTypeDescription):
    """
    Description for the Filter Node.
    
    This node filters workflow items based on configurable conditions,
    supporting multiple filter modes including simple conditions, multiple conditions,
    and custom expressions.
    """
    
    # Class variable to define the node type
    TYPE: ClassVar[str] = "filter"
    
    @classmethod
    def create(cls) -> 'FilterNodeDescription':
        """
        Create a FilterNodeDescription with all required parameters.
        
        Returns:
            FilterNodeDescription: A configured description for filter nodes
        """
        return cls(
            name="filter",
            display_name="Filter",
            description="Filter workflow items based on configurable conditions and criteria.",
            icon="filter",
            icon_color="#229eff",
            group=["transform"],
            version=1.0,
            inputs=[NodeConnectionType.Main],
            outputs=[NodeConnectionType.Main, NodeConnectionType.Main],
            output_names=["kept", "discarded"],
            parameters=[
                NodeParameter(
                    name="filter_mode",
                    display_name="Filter Mode",
                    description="The type of filtering to perform",
                    type=PropertyTypes.OPTIONS,
                    default="simple",
                    required=True,
                    options=[
                        NodeParameterOption(
                            name="Simple Condition", 
                            value="simple", 
                            description="Filter using a single condition with two values"
                        ),
                        NodeParameterOption(
                            name="Multiple Conditions", 
                            value="multiple", 
                            description="Filter using multiple conditions with AND/OR logic"
                        ),
                        NodeParameterOption(
                            name="Expression", 
                            value="expression", 
                            description="Filter using a custom Python expression"
                        )
                    ]
                ),
                # Simple condition parameters
                NodeParameter(
                    name="field_name",
                    display_name="Field Name",
                    description="The field name to filter on (supports dot notation for nested fields)",
                    type=PropertyTypes.STRING,
                    default="",
                    required=True,
                    display_options=DisplayOptions(
                        show={"filter_mode": ["simple"]}
                    )
                ),
                NodeParameter(
                    name="operation",
                    display_name="Operation",
                    description="The comparison operation to perform",
                    type=PropertyTypes.OPTIONS,
                    default="equal",
                    required=True,
                    display_options=DisplayOptions(
                        show={"filter_mode": ["simple"]}
                    ),
                    options=[
                        NodeParameterOption(name="Equal", value="equal", description="Field equals value"),
                        NodeParameterOption(name="Not Equal", value="notEqual", description="Field does not equal value"),
                        NodeParameterOption(name="Greater Than", value="greaterThan", description="Field is greater than value"),
                        NodeParameterOption(name="Less Than", value="lessThan", description="Field is less than value"),
                        NodeParameterOption(name="Greater Than or Equal", value="greaterThanOrEqual", description="Field is greater than or equal to value"),
                        NodeParameterOption(name="Less Than or Equal", value="lessThanOrEqual", description="Field is less than or equal to value"),
                        NodeParameterOption(name="Contains", value="contains", description="Field contains value"),
                        NodeParameterOption(name="Not Contains", value="notContains", description="Field does not contain value"),
                        NodeParameterOption(name="Starts With", value="startsWith", description="Field starts with value"),
                        NodeParameterOption(name="Ends With", value="endsWith", description="Field ends with value"),
                        NodeParameterOption(name="Is Empty", value="isEmpty", description="Field is empty or null"),
                        NodeParameterOption(name="Is Not Empty", value="isNotEmpty", description="Field is not empty"),
                        NodeParameterOption(name="Regex Match", value="regex", description="Field matches regular expression")
                    ]
                ),
                NodeParameter(
                    name="filter_value",
                    display_name="Filter Value",
                    description="The value to compare against",
                    type=PropertyTypes.STRING,
                    default="",
                    required=False,
                    display_options=DisplayOptions(
                        show={
                            "filter_mode": ["simple"],
                            "operation": ["equal", "notEqual", "greaterThan", "lessThan", 
                                        "greaterThanOrEqual", "lessThanOrEqual", "contains", 
                                        "notContains", "startsWith", "endsWith", "regex"]
                        }
                    )
                ),
                # Multiple conditions parameters
                NodeParameter(
                    name="conditions",
                    display_name="Conditions",
                    description="List of conditions to evaluate",
                    type=PropertyTypes.COLLECTION,
                    default={},
                    required=True,
                    display_options=DisplayOptions(
                        show={"filter_mode": ["multiple"]}
                    )
                ),
                NodeParameter(
                    name="combine_operation",
                    display_name="Combine Operation",
                    description="How to combine multiple conditions",
                    type=PropertyTypes.OPTIONS,
                    default="AND",
                    required=True,
                    display_options=DisplayOptions(
                        show={"filter_mode": ["multiple"]}
                    ),
                    options=[
                        NodeParameterOption(name="AND", value="AND", description="All conditions must be true"),
                        NodeParameterOption(name="OR", value="OR", description="At least one condition must be true")
                    ]
                ),
                # Expression parameters
                NodeParameter(
                    name="expression",
                    display_name="Filter Expression",
                    description="Custom Python expression for filtering. Use 'item' to reference the current data item. Return True to keep the item, False to discard it.",
                    type=PropertyTypes.STRING,
                    default="# Filter expression - return True to keep item, False to discard\n# Use 'item' to reference the current data item\n# Example: item.get('status') == 'active' and item.get('score', 0) > 50\n\nreturn item.get('active', False)",
                    required=True,
                    display_options=DisplayOptions(
                        show={"filter_mode": ["expression"]}
                    ),
                    type_options=PropertyTypeOptions(
                        editor="code",
                        editor_language="python"
                    )
                ),
                # Options
                NodeParameter(
                    name="case_sensitive",
                    display_name="Case Sensitive",
                    description="Whether string comparisons should be case sensitive",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False
                ),
                NodeParameter(
                    name="ignore_whitespace",
                    display_name="Ignore Whitespace",
                    description="Whether to ignore leading/trailing whitespace in string comparisons",
                    type=PropertyTypes.BOOLEAN,
                    default=False,
                    required=False
                ),
                NodeParameter(
                    name="include_discarded",
                    display_name="Include Discarded Items",
                    description="Whether to output discarded items to the second output",
                    type=PropertyTypes.BOOLEAN,
                    default=True,
                    required=False
                ),
                # Input data parameter for testing
                NodeParameter(
                    name="input_data",
                    display_name="Input Data",
                    description="Input data for testing (JSON array). In workflows, this comes from the previous node.",
                    type=PropertyTypes.STRING,
                    default="[]",
                    required=False,
                    type_options=PropertyTypeOptions(
                        editor="code",
                        editor_language="json"
                    )
                )
            ]
        )
