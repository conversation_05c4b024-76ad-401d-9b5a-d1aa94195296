from typing import Optional
from app.services.temporal_service import TemporalService

temporal_service = TemporalService()

async def connect_to_temporal() -> TemporalService:
    """
    Connect to the Temporal service.
    
    Returns:
        TemporalService: The connected Temporal service instance.
    """
    if not temporal_service.is_connected():
        await temporal_service.connect()
    return temporal_service


async def close_temporal_connection():
    """
    Close the Temporal service connection.
    """
    if temporal_service.is_connected():
        await temporal_service.disconnect()
        return True
    return False

async def get_temporal_service() -> TemporalService:
    """
    Dependency function that yields the Temporal service instance.
    
    Returns:
        TemporalService: The Temporal service instance.
    """
    if not temporal_service.is_connected():
        await connect_to_temporal()
    return temporal_service