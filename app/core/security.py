"""
Security utilities for authentication and authorization.
Implements JWT tokens, password hashing, and role-based access control.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import settings
from app.utils.encryption import Encryption, EncryptionError

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        
    Returns:
        str: Encoded JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "access"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT refresh token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        
    Returns:
        str: Encoded JWT refresh token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> Optional[str]:
    """
    Verify JWT token and return subject.
    
    Args:
        token: JWT token to verify
        token_type: Expected token type ('access' or 'refresh')
        
    Returns:
        Optional[str]: Token subject if valid, None otherwise
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        token_sub: Optional[str] = payload.get("sub")
        token_type_claim: Optional[str] = payload.get("type")
        
        if token_sub is None or token_type_claim != token_type:
            return None
            
        return token_sub
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash a password.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """
    Generate password reset token.

    Args:
        email: User email

    Returns:
        str: Password reset token
    """
    delta = timedelta(hours=24)  # 24 hours expiry
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email, "type": "password_reset"},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verify password reset token.
    
    Args:
        token: Password reset token
        
    Returns:
        Optional[str]: Email if token is valid, None otherwise
    """
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        return decoded_token.get("sub")
    except JWTError:
        return None


# Role-based access control utilities
class Permission:
    """Permission constants."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"


class Role:
    """Role constants."""
    OWNER = "owner"
    ADMIN = "admin"
    EDITOR = "editor"
    VIEWER = "viewer"


# Role permissions mapping
ROLE_PERMISSIONS = {
    Role.OWNER: [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
    Role.ADMIN: [Permission.READ, Permission.WRITE, Permission.DELETE],
    Role.EDITOR: [Permission.READ, Permission.WRITE],
    Role.VIEWER: [Permission.READ],
}


def has_permission(user_roles: list[str], required_permission: str) -> bool:
    """
    Check if user has required permission based on roles.
    
    Args:
        user_roles: List of user roles
        required_permission: Required permission
        
    Returns:
        bool: True if user has permission, False otherwise
    """
    for role in user_roles:
        if role in ROLE_PERMISSIONS:
            if required_permission in ROLE_PERMISSIONS[role]:
                return True
    return False


def has_role(user_roles: list[str], required_role: str) -> bool:
    """
    Check if user has required role.
    
    Args:
        user_roles: List of user roles
        required_role: Required role
        
    Returns:
        bool: True if user has role, False otherwise
    """
    return required_role in user_roles


def encrypt_credential_data(data: dict[str, Any]) -> str:
    """
    Encrypt sensitive credential data before storage.
    
    Args:
        data: JSON string containing credential data
        
    Returns:
        str: Encrypted data string
    """
    try:
        encryptor = Encryption()
        return encryptor.encrypt(data)
    except EncryptionError as e:
        raise ValueError(f"Failed to encrypt credential data: {str(e)}")
    


def decrypt_credential_data(encrypted_data: str) -> Dict[str, Any]:
    """
    Decrypt credential data for use.
    
    Args:
        encrypted_data: Encrypted data string
        
    Returns:
        str: Decrypted JSON string
    """
    try:
        encryptor = Encryption()
        return encryptor.decrypt(encrypted_data)
    except EncryptionError as e:
        raise ValueError(f"Failed to decrypt credential data: {str(e)}")
