"""
RabbitMQ consumer service for event-based messaging.
"""
import json
import asyncio
import aio_pika
from typing import Callable, Dict, Any, Optional
from app.utils.logging import get_logger

logger = get_logger(__name__)

class RabbitMQConsumer:
    """RabbitMQ consumer service for event-based messaging."""

    def __init__(self, rabbitmq_url: str):
        """Initialize the RabbitMQ consumer."""
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        self.handlers: Dict[str, Callable] = {}
        self.is_running = False
        self.tasks = []

    async def connect(self) -> None:
        """Connect to RabbitMQ."""
        logger.info("Connecting to RabbitMQ")
        self.connection = await aio_pika.connect_robust(self.rabbitmq_url)
        self.channel = await self.connection.channel()
        logger.info("Connected to RabbitMQ")

    async def setup_consumer(self, queue_name: str, callback: Callable) -> None:
        """Setup a consumer for a queue."""
        logger.info(f"Setting up consumer for queue {queue_name}")
        # Declare the queue
        queue = await self.channel.declare_queue(queue_name, durable=True)
        
        # Add the callback to the handlers
        self.handlers[queue_name] = callback
        
        # Start consuming
        await queue.consume(self._process_message)
        logger.info(f"Consumer set up for queue {queue_name}")

    async def _process_message(self, message: aio_pika.IncomingMessage) -> None:
        """Process a message from RabbitMQ."""
        async with message.process():
            queue_name = message.routing_key
            body = message.body.decode()
            logger.info(f"Received message from queue {queue_name}: {body}")
            
            # Parse the message
            try:
                payload = json.loads(body)
                # Call the appropriate handler
                if queue_name in self.handlers:
                    await self.handlers[queue_name](payload)
                else:
                    logger.warning(f"No handler for queue {queue_name}")
            except json.JSONDecodeError:
                logger.error(f"Failed to parse message: {body}")
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")

    async def start(self) -> None:
        """Start the consumer."""
        if self.is_running:
            return
        
        await self.connect()
        self.is_running = True
        logger.info("RabbitMQ consumer started")

    async def stop(self) -> None:
        """Stop the consumer."""
        if not self.is_running:
            return
        
        # Cancel all tasks
        for task in self.tasks:
            task.cancel()
        
        # Close the connection
        if self.connection:
            await self.connection.close()
            self.connection = None
        
        self.is_running = False
        logger.info("RabbitMQ consumer stopped")
