"""
Service for Tag business logic operations.
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.tag import Tag
from app.repositories.tag_repository import TagRepository
from app.services.base_service import BaseService
from app.schemas.tag import TagC<PERSON>, TagUpdate, TagList
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

logger = get_logger("services.tag")


class TagService(BaseService[Tag, TagCreate, TagUpdate, TagRepository]):
    """Service for Tag business logic operations."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize service with database session.
        
        Args:
            db: Database session
        """
        self.db = db
        self.tag_repository = TagRepository(db)
        super().__init__(self.tag_repository)
    
    async def create_tag(self, tag_data: TagCreate) -> Tag:
        """
        Create a new tag.
        
        Args:
            tag_data: Tag creation data
            
        Returns:
            Tag: Created tag
            
        Raises:
            ConflictError: If tag name already exists
        """
        try:
            # Check if tag name already exists
            existing_tag = await self.tag_repository.get_by_name(tag_data.name)
            if existing_tag:
                raise ConflictError(f"Tag with name '{tag_data.name}' already exists")
            
            # Create tag
            tag_dict = tag_data.model_dump()
            tag = Tag(**tag_dict)
            
            self.db.add(tag)
            await self.db.commit()
            await self.db.refresh(tag)
            
            logger.info(
                "Tag created",
                tag_id=tag.id,
                tag_name=tag.name,
                created_by=tag.created_by
            )
            
            return tag
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create tag: {str(e)}")
            raise
    
    async def get_tag_by_id(self, tag_id: int) -> Tag:
        """
        Get tag by ID.
        
        Args:
            tag_id: Tag ID
            
        Returns:
            Tag: Tag instance
            
        Raises:
            NotFoundError: If tag not found
        """
        tag = await self.tag_repository.get(tag_id)
        if not tag:
            raise NotFoundError(f"Tag with id {tag_id} not found")
        
        return tag
    
    async def get_tag_with_workflows(self, tag_id: int) -> Tag:
        """
        Get tag with its associated workflows.
        
        Args:
            tag_id: Tag ID
            
        Returns:
            Tag: Tag with workflows
            
        Raises:
            NotFoundError: If tag not found
        """
        tag = await self.tag_repository.get_with_workflows(tag_id)
        if not tag:
            raise NotFoundError(f"Tag with id {tag_id} not found")
        
        return tag
    
    async def update_tag(self, tag_id: int, tag_update: TagUpdate) -> Tag:
        """
        Update an existing tag.
        
        Args:
            tag_id: Tag ID
            tag_update: Tag update data
            
        Returns:
            Tag: Updated tag
            
        Raises:
            NotFoundError: If tag not found
            ConflictError: If new name already exists
        """
        try:
            # Get existing tag
            tag = await self.tag_repository.get(tag_id)
            if not tag:
                raise NotFoundError(f"Tag with id {tag_id} not found")
            
            # Check for name conflicts if name is being updated
            if tag_update.name and tag_update.name != tag.name:
                existing_tag = await self.tag_repository.get_by_name(tag_update.name)
                if existing_tag and existing_tag.id != tag_id:
                    raise ConflictError(f"Tag with name '{tag_update.name}' already exists")
            
            # Update tag
            update_dict = tag_update.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(tag, field, value)
            
            await self.db.commit()
            await self.db.refresh(tag)
            
            logger.info(
                "Tag updated",
                tag_id=tag_id,
                tag_name=tag.name,
                updated_by=tag_update.edited_by
            )
            
            return tag
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update tag {tag_id}: {str(e)}")
            raise
    
    async def delete_tag(self, tag_id: int) -> Tag:
        """
        Delete a tag with proper cascade handling.
        
        Args:
            tag_id: Tag ID
            
        Returns:
            Tag: Deleted tag
            
        Raises:
            NotFoundError: If tag not found
            ValidationError: If tag is still in use
        """
        try:
            # Get tag with workflows to check usage
            tag = await self.tag_repository.get_with_workflows(tag_id)
            if not tag:
                raise NotFoundError(f"Tag with id {tag_id} not found")
            
            # Check if tag is still in use
            if tag.usage_count > 0 or tag.workflows:
                raise ValidationError(
                    f"Cannot delete tag '{tag.name}' as it is still in use by {tag.usage_count} workflows"
                )
            
            # Delete tag
            await self.db.delete(tag)
            await self.db.commit()
            
            logger.info(
                "Tag deleted",
                tag_id=tag_id,
                tag_name=tag.name
            )
            
            return tag
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete tag {tag_id}: {str(e)}")
            raise
    
    async def get_tags_paginated(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        min_usage: Optional[int] = None,
        max_usage: Optional[int] = None,
        created_by: Optional[int] = None
    ) -> TagList:
        """
        Get paginated list of tags with optional filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Search term for tag names
            min_usage: Minimum usage count
            max_usage: Maximum usage count
            created_by: Filter by creator user ID
            
        Returns:
            TagList: Paginated tag list
        """
        if search:
            tags = await self.tag_repository.search_by_name(search, skip, limit)
            # For search, we don't have an exact count method, so we estimate
            total = len(tags) + skip if len(tags) == limit else skip + len(tags)
        elif min_usage is not None or max_usage is not None:
            tags = await self.tag_repository.get_tags_by_usage_range(
                min_usage or 0, max_usage, skip, limit
            )
            total = await self.tag_repository.count_by_usage_range(min_usage or 0, max_usage)
        elif created_by is not None:
            tags = await self.tag_repository.get_tags_by_creator(created_by, skip, limit)
            # For creator filter, we estimate total
            total = len(tags) + skip if len(tags) == limit else skip + len(tags)
        else:
            tags = await self.tag_repository.get_multi(skip, limit)
            total = await self.tag_repository.count()
        
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return TagList(
            tags=tags,
            total=total,
            page=page,
            size=limit
        )
    
    async def get_popular_tags(self, limit: int = 10, min_usage: int = 1) -> List[Tag]:
        """
        Get most popular tags by usage count.
        
        Args:
            limit: Maximum number of tags to return
            min_usage: Minimum usage count
            
        Returns:
            List[Tag]: List of popular tags
        """
        return await self.tag_repository.get_popular_tags(limit, min_usage)
    
    async def get_unused_tags(self, skip: int = 0, limit: int = 100) -> List[Tag]:
        """
        Get tags that are not being used.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tag]: List of unused tags
        """
        return await self.tag_repository.get_unused_tags(skip, limit)
    
    async def cleanup_unused_tags(self, dry_run: bool = True) -> List[Tag]:
        """
        Clean up unused tags (usage_count = 0).
        
        Args:
            dry_run: If True, only return tags that would be deleted
            
        Returns:
            List[Tag]: List of tags that were/would be deleted
        """
        unused_tags = await self.tag_repository.get_unused_tags(limit=1000)  # Get all unused
        
        if not dry_run:
            try:
                for tag in unused_tags:
                    await self.db.delete(tag)
                await self.db.commit()
                
                logger.info(f"Cleaned up {len(unused_tags)} unused tags")
            except Exception as e:
                await self.db.rollback()
                logger.error(f"Failed to cleanup unused tags: {str(e)}")
                raise
        
        return unused_tags
    
    async def get_tag_by_name(self, name: str) -> Optional[Tag]:
        """
        Get tag by name.
        
        Args:
            name: Tag name
            
        Returns:
            Optional[Tag]: Tag instance or None
        """
        return await self.tag_repository.get_by_name(name)
    
    async def tag_exists(self, name: str) -> bool:
        """
        Check if a tag with the given name exists.
        
        Args:
            name: Tag name
            
        Returns:
            bool: True if tag exists, False otherwise
        """
        return await self.tag_repository.exists_by_name(name)
