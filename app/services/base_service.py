"""
Base service class with common business logic patterns.
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar

from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.base_repository import BaseRepository
from app.utils.exceptions import NotFoundError, ValidationError

ModelType = TypeVar("ModelType")
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")
RepositoryType = TypeVar("RepositoryType", bound=BaseRepository)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType, RepositoryType]):
    """Base service with common business logic operations."""
    
    def __init__(self, repository: RepositoryType):
        """
        Initialize service with repository.
        
        Args:
            repository: Repository instance
        """
        self.repository = repository
    
    async def get_by_id(self, id: int) -> ModelType:
        """
        Get entity by ID.
        
        Args:
            id: Entity ID
            
        Returns:
            ModelType: Entity instance
            
        Raises:
            NotFoundError: If entity not found
        """
        entity = await self.repository.get(id)
        if not entity:
            raise NotFoundError(f"Entity with id {id} not found")
        return entity
    
    async def get_by_id_optional(self, id: int) -> Optional[ModelType]:
        """
        Get entity by ID, return None if not found.
        
        Args:
            id: Entity ID
            
        Returns:
            Optional[ModelType]: Entity instance or None
        """
        return await self.repository.get(id)
    
    async def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
    ) -> List[ModelType]:
        """
        Get multiple entities with pagination and filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Dictionary of field filters
            order_by: Field name to order by
            
        Returns:
            List[ModelType]: List of entities
        """
        return await self.repository.get_multi(
            skip=skip,
            limit=limit,
            filters=filters,
            order_by=order_by
        )
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count entities with optional filtering.
        
        Args:
            filters: Dictionary of field filters
            
        Returns:
            int: Number of entities
        """
        return await self.repository.count(filters)
    
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """
        Create new entity.
        
        Args:
            obj_in: Creation schema
            
        Returns:
            ModelType: Created entity
        """
        # Validate creation data
        await self._validate_create(obj_in)
        
        # Create entity
        entity = await self.repository.create(obj_in)
        
        # Post-creation hook
        await self._after_create(entity)
        
        return entity
    
    async def update(self, id: int, obj_in: UpdateSchemaType) -> ModelType:
        """
        Update existing entity.
        
        Args:
            id: Entity ID
            obj_in: Update schema
            
        Returns:
            ModelType: Updated entity
            
        Raises:
            NotFoundError: If entity not found
        """
        # Get existing entity
        entity = await self.get_by_id(id)
        
        # Validate update data
        await self._validate_update(entity, obj_in)
        
        # Update entity
        updated_entity = await self.repository.update(entity, obj_in)
        
        # Post-update hook
        await self._after_update(updated_entity)
        
        return updated_entity
    
    async def delete(self, id: int) -> ModelType:
        """
        Delete entity by ID.
        
        Args:
            id: Entity ID
            
        Returns:
            ModelType: Deleted entity
            
        Raises:
            NotFoundError: If entity not found
        """
        # Get existing entity
        entity = await self.get_by_id(id)
        
        # Validate deletion
        await self._validate_delete(entity)
        
        # Delete entity
        deleted_entity = await self.repository.delete(id)

        # Check if deletion was successful
        if deleted_entity is None:
            raise NotFoundError(f"Entity with id {id} could not be deleted")
    
        # Post-deletion hook
        await self._after_delete(deleted_entity)
        
        return deleted_entity
    
    async def exists(self, id: int) -> bool:
        """
        Check if entity exists by ID.
        
        Args:
            id: Entity ID
            
        Returns:
            bool: True if entity exists, False otherwise
        """
        return await self.repository.exists(id)
    
    # Validation hooks (to be overridden in subclasses)
    async def _validate_create(self, obj_in: CreateSchemaType) -> None:
        """
        Validate creation data.
        
        Args:
            obj_in: Creation schema
            
        Raises:
            ValidationError: If validation fails
        """
        pass
    
    async def _validate_update(self, entity: ModelType, obj_in: UpdateSchemaType) -> None:
        """
        Validate update data.
        
        Args:
            entity: Existing entity
            obj_in: Update schema
            
        Raises:
            ValidationError: If validation fails
        """
        pass
    
    async def _validate_delete(self, entity: ModelType) -> None:
        """
        Validate deletion.
        
        Args:
            entity: Entity to delete
            
        Raises:
            ValidationError: If validation fails
        """
        pass
    
    # Lifecycle hooks (to be overridden in subclasses)
    async def _after_create(self, entity: ModelType) -> None:
        """
        Hook called after entity creation.
        
        Args:
            entity: Created entity
        """
        pass
    
    async def _after_update(self, entity: ModelType) -> None:
        """
        Hook called after entity update.
        
        Args:
            entity: Updated entity
        """
        pass
    
    async def _after_delete(self, entity: ModelType) -> None:
        """
        Hook called after entity deletion.
        
        Args:
            entity: Deleted entity
        """
        pass
