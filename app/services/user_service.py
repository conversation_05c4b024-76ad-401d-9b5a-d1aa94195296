"""
User service for user management business logic.
"""

from typing import Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.repositories.role_repository import RoleRepository
from app.repositories.user_repository import UserRepository
from app.schemas.auth import UserRoleAssignment
from app.schemas.user import UserCreate, UserStats, UserUpdate
from app.services.base_service import BaseService
from app.utils.exceptions import ConflictError, NotFoundError, ValidationError
from app.utils.logging import get_logger

logger = get_logger("services.user")


class UserService(BaseService[User, UserCreate, UserUpdate, UserRepository]):
    """User service for user management operations."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize user service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.user_repo = UserRepository(db)
        self.role_repo = RoleRepository(db)
        super().__init__(self.user_repo)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.
        
        Args:
            email: User email address
            
        Returns:
            Optional[User]: User instance or None
        """
        return await self.user_repo.get_by_email(email)
    
    async def get_user_with_roles(self, user_id: int) -> User:
        """
        Get user with roles loaded.
        
        Args:
            user_id: User ID
            
        Returns:
            User: User instance with roles
            
        Raises:
            NotFoundError: If user not found
        """
        user = await self.user_repo.get_with_roles(user_id)
        if not user:
            raise NotFoundError(f"User with id {user_id} not found")
        return user
    
    async def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        Get active users.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of active users
        """
        return await self.user_repo.get_active_users(skip=skip, limit=limit)
    
    async def search_users(self, query: str, skip: int = 0, limit: int = 100) -> List[User]:
        """
        Search users by email or full name.
        
        Args:
            query: Search query
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of matching users
        """
        if not query or len(query.strip()) < 2:
            raise ValidationError("Search query must be at least 2 characters long")
        
        return await self.user_repo.search_users(query.strip(), skip=skip, limit=limit)
    
    async def get_users_by_role(self, role_name: str) -> List[User]:
        """
        Get users by role name.
        
        Args:
            role_name: Role name
            
        Returns:
            List[User]: List of users with the specified role
        """
        return await self.user_repo.get_users_by_role(role_name)
    
    async def get_user_statistics(self) -> UserStats:
        """
        Get user statistics.
        
        Returns:
            UserStats: User statistics
        """
        stats = await self.user_repo.get_user_stats()
        
        # Get users by role statistics
        roles = await self.role_repo.get_all_with_permissions()
        users_by_role = {}
        
        for role in roles:
            users = await self.get_users_by_role(role.name)
            users_by_role[role.name] = len(users)
        
        return UserStats(
            total_users=stats["total_users"],
            active_users=stats["active_users"],
            verified_users=stats["verified_users"],
            superusers=stats["superusers"],
            locked_users=stats["locked_users"],
            recent_registrations=stats["recent_registrations"],
            users_by_role=users_by_role,
        )
    
    async def assign_roles_to_user(self, assignment: UserRoleAssignment) -> User:
        """
        Assign roles to user.
        
        Args:
            assignment: User role assignment data
            
        Returns:
            User: Updated user with new roles
            
        Raises:
            NotFoundError: If user or role not found
        """
        user = await self.get_user_with_roles(assignment.user_id)
        
        # Clear existing roles
        user.roles.clear()
        
        # Assign new roles
        for role_id in assignment.role_ids:
            role = await self.role_repo.get(role_id)
            if not role:
                raise NotFoundError(f"Role with id {role_id} not found")
            user.roles.append(role)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info(
            "Roles assigned to user",
            user_id=user.id,
            role_ids=assignment.role_ids
        )
        
        return user
    
    async def add_role_to_user(self, user_id: int, role_id: int) -> User:
        """
        Add a single role to user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Returns:
            User: Updated user
            
        Raises:
            NotFoundError: If user or role not found
            ConflictError: If user already has the role
        """
        user = await self.get_user_with_roles(user_id)
        role = await self.role_repo.get(role_id)
        
        if not role:
            raise NotFoundError(f"Role with id {role_id} not found")
        
        if role in user.roles:
            raise ConflictError(f"User already has role '{role.name}'")
        
        user.roles.append(role)
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("Role added to user", user_id=user.id, role_id=role_id, role_name=role.name)
        
        return user
    
    async def remove_role_from_user(self, user_id: int, role_id: int) -> User:
        """
        Remove a role from user.
        
        Args:
            user_id: User ID
            role_id: Role ID
            
        Returns:
            User: Updated user
            
        Raises:
            NotFoundError: If user or role not found
            ValidationError: If user doesn't have the role
        """
        user = await self.get_user_with_roles(user_id)
        role = await self.role_repo.get(role_id)
        
        if not role:
            raise NotFoundError(f"Role with id {role_id} not found")
        
        if role not in user.roles:
            raise ValidationError(f"User doesn't have role '{role.name}'")
        
        user.roles.remove(role)
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("Role removed from user", user_id=user.id, role_id=role_id, role_name=role.name)
        
        return user
    
    async def activate_user(self, user_id: int) -> User:
        """
        Activate user account.
        
        Args:
            user_id: User ID
            
        Returns:
            User: Updated user
        """
        user = await self.get_by_id(user_id)
        user.is_active = True
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User activated", user_id=user.id)
        
        return user
    
    async def deactivate_user(self, user_id: int) -> User:
        """
        Deactivate user account.
        
        Args:
            user_id: User ID
            
        Returns:
            User: Updated user
        """
        user = await self.get_by_id(user_id)
        user.is_active = False
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User deactivated", user_id=user.id)
        
        return user
    
    async def verify_user(self, user_id: int) -> User:
        """
        Verify user account.
        
        Args:
            user_id: User ID
            
        Returns:
            User: Updated user
        """
        user = await self.get_by_id(user_id)
        user.is_verified = True
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User verified", user_id=user.id)
        
        return user
    
    async def unlock_user(self, user_id: int) -> User:
        """
        Unlock user account.
        
        Args:
            user_id: User ID
            
        Returns:
            User: Updated user
        """
        await self.user_repo.unlock_user(user_id)
        user = await self.get_by_id(user_id)
        
        logger.info("User unlocked", user_id=user.id)
        
        return user
    
    # Validation hooks
    async def _validate_create(self, obj_in: UserCreate) -> None:
        """
        Validate user creation data.
        
        Args:
            obj_in: User creation data
            
        Raises:
            ConflictError: If user already exists
        """
        existing_user = await self.user_repo.get_by_email(obj_in.email)
        if existing_user:
            raise ConflictError("User with this email already exists")
    
    async def _validate_update(self, entity: User, obj_in: UserUpdate) -> None:
        """
        Validate user update data.
        
        Args:
            entity: Existing user
            obj_in: Update data
            
        Raises:
            ConflictError: If email already exists for another user
        """
        if obj_in.email and obj_in.email != entity.email:
            existing_user = await self.user_repo.get_by_email(obj_in.email)
            if existing_user and existing_user.id != entity.id:
                raise ConflictError("User with this email already exists")
    
    async def _validate_delete(self, entity: User) -> None:
        """
        Validate user deletion.
        
        Args:
            entity: User to delete
            
        Raises:
            ValidationError: If user cannot be deleted
        """
        if entity.is_superuser:
            # Check if this is the last superuser
            superuser_count = await self.user_repo.count({"is_superuser": True})
            if superuser_count <= 1:
                raise ValidationError("Cannot delete the last superuser")
    
    # Lifecycle hooks
    async def _after_create(self, entity: User) -> None:
        """
        Hook called after user creation.
        
        Args:
            entity: Created user
        """
        logger.info("User created", user_id=entity.id, email=entity.email)
    
    async def _after_update(self, entity: User) -> None:
        """
        Hook called after user update.
        
        Args:
            entity: Updated user
        """
        logger.info("User updated", user_id=entity.id, email=entity.email)
    
    async def _after_delete(self, entity: User) -> None:
        """
        Hook called after user deletion.
        
        Args:
            entity: Deleted user
        """
        logger.info("User deleted", user_id=entity.id, email=entity.email)
