"""
Authentication service with comprehensive auth functionality.
"""

from datetime import datetime, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.security import (
    create_access_token,
    create_refresh_token,
    generate_password_reset_token,
    get_password_hash,
    verify_password,
    verify_password_reset_token,
    verify_token,
)
from app.models.user import User
from app.repositories.role_repository import RoleRepository
from app.repositories.user_repository import UserRepository
from app.schemas.auth import (
    ChangePasswordRequest,
    LoginRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    Token,
)
from app.schemas.user import UserCreate
from app.services.base_service import BaseService
from app.utils.exceptions import (
    AuthenticationError,
    ConflictError,
    NotFoundError,
    ValidationError,
)
from app.utils.logging import get_logger

logger = get_logger("services.auth")


class AuthService:
    """Authentication service for user authentication and authorization."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize authentication service.
        
        Args:
            db: Database session
        """
        self.db = db
        self.user_repo = UserRepository(db)
        self.role_repo = RoleRepository(db)
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            Optional[User]: Authenticated user or None
        """
        user = await self.user_repo.get_by_email(email)
        
        if not user:
            logger.warning("Authentication failed: user not found", email=email)
            return None
        
        if not user.is_active:
            logger.warning("Authentication failed: user inactive", email=email)
            return None
        
        if user.is_locked:
            logger.warning("Authentication failed: user locked", email=email)
            return None
        
        if not verify_password(password, user.hashed_password):
            logger.warning("Authentication failed: invalid password", email=email)
            await self.user_repo.increment_failed_login(user.id)
            await self.db.commit()
            return None
        
        # Reset failed login attempts on successful login
        await self.user_repo.update_last_login(user.id)
        await self.db.commit()
        
        logger.info("User authenticated successfully", email=email, user_id=user.id)
        return user
    
    async def login(self, login_data: LoginRequest) -> Token:
        """
        Login user and return JWT tokens.
        
        Args:
            login_data: Login request data
            
        Returns:
            Token: JWT access and refresh tokens
            
        Raises:
            AuthenticationError: If authentication fails
        """
        user = await self.authenticate_user(login_data.email, login_data.password)
        
        if not user:
            raise AuthenticationError("Invalid email or password")
        
        # Create tokens
        access_token = create_access_token(subject=str(user.id))
        refresh_token = create_refresh_token(subject=str(user.id))
        
        logger.info("User logged in successfully", user_id=user.id, email=user.email)
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=30 * 60,  # 30 minutes
        )
    
    async def refresh_token(self, refresh_token: str) -> Token:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            Token: New JWT tokens
            
        Raises:
            AuthenticationError: If refresh token is invalid
        """
        user_id = verify_token(refresh_token, "refresh")
        
        if not user_id:
            raise AuthenticationError("Invalid refresh token")
        
        user = await self.user_repo.get(int(user_id))
        
        if not user or not user.is_active or user.is_locked:
            raise AuthenticationError("User not found or inactive")
        
        # Create new tokens
        access_token = create_access_token(subject=str(user.id))
        new_refresh_token = create_refresh_token(subject=str(user.id))
        
        logger.info("Token refreshed successfully", user_id=user.id)
        
        return Token(
            access_token=access_token,
            refresh_token=new_refresh_token,
            token_type="bearer",
            expires_in=30 * 60,  # 30 minutes
        )
    
    async def register(self, user_data: UserCreate) -> User:
        """
        Register a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            User: Created user
            
        Raises:
            ConflictError: If user already exists
            ValidationError: If validation fails
        """
        # Check if user already exists
        existing_user = await self.user_repo.get_by_email(user_data.email)
        if existing_user:
            raise ConflictError("User with this email already exists")
        
        # Hash password
        hashed_password = get_password_hash(user_data.password)
        
        # Create user
        user_dict = user_data.model_dump(exclude={"password", "role_ids"})
        user_dict["hashed_password"] = hashed_password
        
        user = User(**user_dict)
        self.db.add(user)
        await self.db.flush()
        
        # Assign default role if no roles specified
        if not user_data.role_ids:
            default_role = await self.role_repo.get_default_role()
            if default_role:
                user.roles.append(default_role)
        else:
            # Assign specified roles
            for role_id in user_data.role_ids:
                role = await self.role_repo.get(role_id)
                if role:
                    user.roles.append(role)
        
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User registered successfully", user_id=user.id, email=user.email)
        return user
    
    async def request_password_reset(self, reset_data: PasswordResetRequest) -> str:
        """
        Request password reset for user.
        
        Args:
            reset_data: Password reset request data
            
        Returns:
            str: Password reset token
            
        Raises:
            NotFoundError: If user not found
        """
        user = await self.user_repo.get_by_email(reset_data.email)
        
        if not user:
            # Don't reveal if user exists or not for security
            logger.warning("Password reset requested for non-existent user", email=reset_data.email)
            # Still generate a token to prevent timing attacks
            return generate_password_reset_token(reset_data.email)
        
        if not user.is_active:
            raise ValidationError("User account is not active")
        
        reset_token = generate_password_reset_token(user.email)
        
        logger.info("Password reset requested", user_id=user.id, email=user.email)
        
        # In a real application, you would send this token via email
        # For now, we just return it
        return reset_token
    
    async def reset_password(self, reset_data: PasswordResetConfirm) -> bool:
        """
        Reset user password using reset token.
        
        Args:
            reset_data: Password reset confirmation data
            
        Returns:
            bool: True if password was reset successfully
            
        Raises:
            AuthenticationError: If reset token is invalid
            NotFoundError: If user not found
        """
        email = verify_password_reset_token(reset_data.token)
        
        if not email:
            raise AuthenticationError("Invalid or expired reset token")
        
        user = await self.user_repo.get_by_email(email)
        
        if not user:
            raise NotFoundError("User not found")
        
        if not user.is_active:
            raise ValidationError("User account is not active")
        
        # Update password
        user.hashed_password = get_password_hash(reset_data.new_password)
        user.failed_login_attempts = 0
        user.locked_until = None
        
        await self.db.commit()
        
        logger.info("Password reset successfully", user_id=user.id, email=user.email)
        return True
    
    async def change_password(self, user: User, change_data: ChangePasswordRequest) -> bool:
        """
        Change user password.
        
        Args:
            user: Current user
            change_data: Password change data
            
        Returns:
            bool: True if password was changed successfully
            
        Raises:
            AuthenticationError: If current password is invalid
        """
        # Verify current password
        if not verify_password(change_data.current_password, user.hashed_password):
            raise AuthenticationError("Current password is incorrect")
        
        # Update password
        user.hashed_password = get_password_hash(change_data.new_password)
        
        await self.db.commit()
        
        logger.info("Password changed successfully", user_id=user.id, email=user.email)
        return True
    
    async def logout(self, user: User) -> bool:
        """
        Logout user (placeholder for token blacklisting).
        
        Args:
            user: Current user
            
        Returns:
            bool: True if logout was successful
        """
        # In a real application, you might want to blacklist the token
        # For now, we just log the logout
        logger.info("User logged out", user_id=user.id, email=user.email)
        return True
