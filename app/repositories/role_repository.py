"""
Role and Permission repository for database operations.
"""

from typing import List, Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.role import Permission, Role
from app.repositories.base_repository import BaseRepository
from app.schemas.auth import RoleCreate, RoleUpdate


class RoleRepository(BaseRepository[Role, RoleCreate, RoleUpdate]):
    """Repository for Role model operations."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Role, db)
    
    async def get_by_name(self, name: str) -> Optional[Role]:
        """
        Get role by name.
        
        Args:
            name: Role name
            
        Returns:
            Optional[Role]: Role instance or None
        """
        result = await self.db.execute(
            select(Role)
            .options(selectinload(Role.permissions))
            .where(Role.name == name)
        )
        return result.scalar_one_or_none()
    
    async def get_with_permissions(self, role_id: int) -> Optional[Role]:
        """
        Get role with permissions loaded.
        
        Args:
            role_id: Role ID
            
        Returns:
            Optional[Role]: Role instance with permissions or None
        """
        result = await self.db.execute(
            select(Role)
            .options(selectinload(Role.permissions))
            .where(Role.id == role_id)
        )
        return result.scalar_one_or_none()
    
    async def get_all_with_permissions(self) -> List[Role]:
        """
        Get all roles with permissions loaded.
        
        Returns:
            List[Role]: List of roles with permissions
        """
        result = await self.db.execute(
            select(Role)
            .options(selectinload(Role.permissions))
            .order_by(Role.name)
        )
        return list(result.scalars().all())
    
    async def get_default_role(self) -> Optional[Role]:
        """
        Get the default role for new users.
        
        Returns:
            Optional[Role]: Default role or None
        """
        result = await self.db.execute(
            select(Role)
            .options(selectinload(Role.permissions))
            .where(Role.is_default == True)
        )
        return result.scalar_one_or_none()
    
    async def add_permission_to_role(self, role_id: int, permission_id: int) -> bool:
        """
        Add permission to role.
        
        Args:
            role_id: Role ID
            permission_id: Permission ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        role = await self.get_with_permissions(role_id)
        permission = await self.db.get(Permission, permission_id)
        
        if role and permission and permission not in role.permissions:
            role.permissions.append(permission)
            await self.db.flush()
            return True
        return False
    
    async def remove_permission_from_role(self, role_id: int, permission_id: int) -> bool:
        """
        Remove permission from role.
        
        Args:
            role_id: Role ID
            permission_id: Permission ID
            
        Returns:
            bool: True if successful, False otherwise
        """
        role = await self.get_with_permissions(role_id)
        permission = await self.db.get(Permission, permission_id)
        
        if role and permission and permission in role.permissions:
            role.permissions.remove(permission)
            await self.db.flush()
            return True
        return False


class PermissionRepository(BaseRepository[Permission, dict, dict]):
    """Repository for Permission model operations."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Permission, db)
    
    async def get_by_name(self, name: str) -> Optional[Permission]:
        """
        Get permission by name.
        
        Args:
            name: Permission name
            
        Returns:
            Optional[Permission]: Permission instance or None
        """
        result = await self.db.execute(
            select(Permission).where(Permission.name == name)
        )
        return result.scalar_one_or_none()
    
    async def get_by_resource_action(self, resource: str, action: str) -> Optional[Permission]:
        """
        Get permission by resource and action.
        
        Args:
            resource: Resource name
            action: Action name
            
        Returns:
            Optional[Permission]: Permission instance or None
        """
        result = await self.db.execute(
            select(Permission).where(
                Permission.resource == resource,
                Permission.action == action
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_resource(self, resource: str) -> List[Permission]:
        """
        Get all permissions for a resource.
        
        Args:
            resource: Resource name
            
        Returns:
            List[Permission]: List of permissions for the resource
        """
        result = await self.db.execute(
            select(Permission)
            .where(Permission.resource == resource)
            .order_by(Permission.action)
        )
        return list(result.scalars().all())
    
    async def create_permission(
        self,
        name: str,
        resource: str,
        action: str,
        description: Optional[str] = None
    ) -> Permission:
        """
        Create a new permission.
        
        Args:
            name: Permission name
            resource: Resource name
            action: Action name
            description: Permission description
            
        Returns:
            Permission: Created permission
        """
        permission = Permission(
            name=name,
            resource=resource,
            action=action,
            description=description
        )
        self.db.add(permission)
        await self.db.flush()
        await self.db.refresh(permission)
        return permission
