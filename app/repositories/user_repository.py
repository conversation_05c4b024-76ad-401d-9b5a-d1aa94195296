"""
User repository for database operations.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.security import get_password_hash
from app.models.user import User
from app.repositories.base_repository import BaseRepository
from app.schemas.user import UserCreate, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """Repository for User model operations."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(User, db)

    async def create(self, obj_in: UserCreate) -> User:
        """
        Create a new user with password hashing.

        Args:
            obj_in: User creation schema

        Returns:
            User: Created user instance
        """
        if hasattr(obj_in, 'model_dump'):
            obj_data = obj_in.model_dump()
        else:
            obj_data = obj_in.dict()

        # Hash the password
        if 'password' in obj_data:
            obj_data['hashed_password'] = get_password_hash(obj_data.pop('password'))

        # Remove role_ids as it's not a User model field
        obj_data.pop('role_ids', None)

        db_obj = User(**obj_data)
        self.db.add(db_obj)
        await self.db.flush()
        await self.db.refresh(db_obj)
        return db_obj
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """
        Get user by email address.
        
        Args:
            email: User email address
            
        Returns:
            Optional[User]: User instance or None
        """
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_with_roles(self, user_id: int) -> Optional[User]:
        """
        Get user with roles loaded.
        
        Args:
            user_id: User ID
            
        Returns:
            Optional[User]: User instance with roles or None
        """
        result = await self.db.execute(
            select(User)
            .options(selectinload(User.roles))
            .where(User.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """
        Get active users.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of active users
        """
        result = await self.db.execute(
            select(User)
            .where(and_(User.is_active == True, User.locked_until.is_(None)))
            .order_by(User.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return list(result.scalars().all())
    
    async def get_users_by_role(self, role_name: str) -> List[User]:
        """
        Get users by role name.
        
        Args:
            role_name: Role name
            
        Returns:
            List[User]: List of users with the specified role
        """
        result = await self.db.execute(
            select(User)
            .join(User.roles)
            .where(User.roles.any(name=role_name))
            .options(selectinload(User.roles))
        )
        return list(result.scalars().all())
    
    async def search_users(
        self,
        query: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """
        Search users by email or full name.
        
        Args:
            query: Search query
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of matching users
        """
        search_filter = f"%{query}%"
        result = await self.db.execute(
            select(User)
            .where(
                or_(
                    User.email.ilike(search_filter),
                    User.full_name.ilike(search_filter)
                )
            )
            .order_by(User.full_name)
            .offset(skip)
            .limit(limit)
        )
        return list(result.scalars().all())
    
    async def get_user_stats(self) -> dict:
        """
        Get user statistics.
        
        Returns:
            dict: User statistics
        """
        # Total users
        total_result = await self.db.execute(select(func.count(User.id)))
        total_users = total_result.scalar()
        
        # Active users
        active_result = await self.db.execute(
            select(func.count(User.id)).where(User.is_active == True)
        )
        active_users = active_result.scalar()
        
        # Verified users
        verified_result = await self.db.execute(
            select(func.count(User.id)).where(User.is_verified == True)
        )
        verified_users = verified_result.scalar()
        
        # Superusers
        superuser_result = await self.db.execute(
            select(func.count(User.id)).where(User.is_superuser == True)
        )
        superusers = superuser_result.scalar()
        
        # Locked users
        locked_result = await self.db.execute(
            select(func.count(User.id)).where(User.locked_until > datetime.utcnow())
        )
        locked_users = locked_result.scalar()
        
        # Recent registrations (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_result = await self.db.execute(
            select(func.count(User.id)).where(User.created_at >= thirty_days_ago)
        )
        recent_registrations = recent_result.scalar()
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "verified_users": verified_users,
            "superusers": superusers,
            "locked_users": locked_users,
            "recent_registrations": recent_registrations,
        }
    
    async def update_last_login(self, user_id: int) -> None:
        """
        Update user's last login timestamp.
        
        Args:
            user_id: User ID
        """
        user = await self.get(user_id)
        if user:
            user.last_login = datetime.utcnow()
            user.failed_login_attempts = 0
            user.locked_until = None
            await self.db.flush()
    
    async def increment_failed_login(self, user_id: int) -> None:
        """
        Increment failed login attempts for user.
        
        Args:
            user_id: User ID
        """
        user = await self.get(user_id)
        if user:
            user.failed_login_attempts += 1
            if user.failed_login_attempts >= 5:  # Lock after 5 failed attempts
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
            await self.db.flush()
    
    async def unlock_user(self, user_id: int) -> None:
        """
        Unlock user account.
        
        Args:
            user_id: User ID
        """
        user = await self.get(user_id)
        if user:
            user.failed_login_attempts = 0
            user.locked_until = None
            await self.db.flush()
