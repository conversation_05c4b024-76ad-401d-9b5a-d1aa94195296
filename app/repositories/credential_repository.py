"""
Repository for Credential model database operations.
"""

import sys
import traceback
from typing import List, Optional, Dict, Any
import uuid
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTTPException

from app.models.credential import Credential
from app.repositories.base_repository import BaseRepository
from app.credential.base.credential_model import C<PERSON>entialRequestModel
from app.credential.utils.credential_registry import CredentialRegistry
from app.core.security import encrypt_credential_data
from app.schemas.credential import CredentialCreate, CredentialUpdate


class CredentialRepository(BaseRepository[Credential, CredentialCreate, CredentialUpdate]):
    """Repository for Credential model database operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(Credential, session)
    
    async def get_by_name(self, name: str) -> Optional[Credential]:
        """Get a credential by name."""
        query = select(Credential).where(
            Credential.name == name,
            Credential.is_deleted == False
        )
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_by_id(self, id: uuid.UUID) -> Optional[Credential]:
        """Get a credential by ID."""
        query = select(Credential).where(
            Credential.id == id,
            Credential.is_deleted == False
        )
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def get_by_type(self, type: str) -> List[Credential]:
        """Get all credentials for a specific type."""
        query = select(Credential).where(
            Credential.type == type,
            Credential.is_deleted == False
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_active_credentials(self) -> List[Credential]:
        """Get all active credentials."""
        query = select(Credential).where(
            Credential.status == "active",
            Credential.is_deleted == False
        )
        # result = await self.session.execute(query)
        result = await self.db.execute(query)
        return list(result.scalars().all())
        
    async def get_credentials_for_node(self, node_id: str) -> List[Credential]:
        """Get all credentials accessible by a specific node."""
        # This query is more complex as we need to check if the node is in the allowed_nodes array
        # or if the allowed_nodes array is empty (which means all nodes can access)
        query = select(Credential).where(
            Credential.is_deleted == False,
            Credential.status == "active",
            (
                # (node_id.in_(Credential.allowed_nodes)) |
                # (Credential.allowed_nodes == [])
                (Credential.allowed_nodes.contains([node_id])) |
                (Credential.allowed_nodes == [])
            )
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
        
    async def soft_delete(self, credential_id: uuid.UUID) -> Optional[Credential]:
        """Soft delete a credential."""
        credential = await self.get_by_id(credential_id)
        if credential:
            credential.is_deleted = True
            await self.db.commit()
            await self.db.refresh(credential)
        return credential
    
    async def add_credential(self, credential_request: CredentialRequestModel, created_by: str, status: str = "pending") -> Dict[str, Any]:
        """
        Add a new credential to the database after validation.
        
        Args:
            credential_request: Credential request model with all required parameters
            created_by: User or system that created the credential
            status: Initial status ("active" by default)
            
        Returns:
            Dict with credential information and metadata
            
        Raises:
            ValueError: If validation fails
            HTTPException: If credential cannot be saved
        """
        try:

            from app.credential.utils.credential_manager import CredentialManager

            credential_manager = CredentialManager()
            # Validate the credential
            credential_manager.validate_credential(credential_request)
            
            # Get the credential provider to determine auth method
            credential_provider = CredentialRegistry.get(credential_request.name)
            if not credential_provider:
                raise ValueError(f"Credential type '{credential_request.name}' not found")
                
            auth_method = credential_provider.authentication().method.value
            allowed_nodes = credential_provider.allowed_nodes or []
            
            # Format parameters for encryption
            formatted_data = {}
            for param_name, param_value in credential_request.parameters.items():
                # Find parameter definition to check if it's sensitive
                param_def = next(
                    (p for p in (credential_provider.parameters or []) if p.name == param_name), None
                )
                
                is_sensitive = False
                if param_def and param_def.sensitive:
                    is_sensitive = True
                
                # Format with value and is_sensitive flag
                formatted_data[param_name] = {
                    "value": param_value,
                    "is_sensitive": is_sensitive
                }
            
            # Encrypt sensitive data before storing
            # encrypted_data = json.dumps(formatted_data)
            # If you have an encryption function, use it like:
            encrypted_data = encrypt_credential_data(formatted_data)
            
            # Create the credential in the database
            new_credential = Credential(
                type=credential_request.name,
                name=credential_request.display_name,
                auth_method=auth_method,
                data=encrypted_data,
                allowed_nodes=allowed_nodes,
                created_by=created_by,
                is_deleted=False
            )
            
            # Save to database
            self.db.add(new_credential)
            await self.db.commit()
            await self.db.refresh(new_credential)
            
            # Return response with metadata
            return {
                "id": str(new_credential.id),
                "name": new_credential.name,
                "type": new_credential.type,
                "auth_method": new_credential.auth_method,
                "status": new_credential.status,
                "created_at": new_credential.created_at,
                "allowed_nodes": new_credential.allowed_nodes,
                "message": "Credential added successfully"
            }
            
        except ValueError as e:
            # Validation error
            await self.db.rollback()
            raise ValueError(f"Credential validation failed: {str(e)}")
        except Exception as e:
            # Database or other error
            await self.db.rollback()
            exc_type, exc_value, exc_tb = sys.exc_info()
            traceback.print_exception(exc_type, exc_value, exc_tb)
            raise HTTPException(
                status_code=500,
                detail=f"Failed to add credential: {str(e)}"
            )
