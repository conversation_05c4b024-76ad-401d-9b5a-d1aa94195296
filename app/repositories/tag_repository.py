"""
Repository for Tag model database operations.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import select, func, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.tag import Tag
from app.repositories.base_repository import BaseRepository
from app.schemas.tag import TagCreate, TagUpdate


class TagRepository(BaseRepository[Tag, TagCreate, TagUpdate]):
    """Repository for Tag model database operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(Tag, session)
    
    async def get_by_name(self, name: str) -> Optional[Tag]:
        """
        Get tag by name.
        
        Args:
            name: Tag name
            
        Returns:
            Optional[Tag]: Tag instance or None
        """
        query = select(Tag).where(Tag.name == name.lower().strip())
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_with_workflows(self, tag_id: int) -> Optional[Tag]:
        """
        Get tag with its associated workflows.
        
        Args:
            tag_id: Tag ID
            
        Returns:
            Optional[Tag]: Tag with workflows or None
        """
        query = (
            select(Tag)
            .options(selectinload(Tag.workflows))
            .where(Tag.id == tag_id)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_popular_tags(
        self,
        limit: int = 10,
        min_usage: int = 1
    ) -> List[Tag]:
        """
        Get most popular tags by usage count.
        
        Args:
            limit: Maximum number of tags to return
            min_usage: Minimum usage count
            
        Returns:
            List[Tag]: List of popular tags
        """
        query = (
            select(Tag)
            .where(Tag.usage_count >= min_usage)
            .order_by(desc(Tag.usage_count), Tag.name)
            .limit(limit)
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def search_by_name(
        self,
        search_term: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Tag]:
        """
        Search tags by name pattern.
        
        Args:
            search_term: Search term
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tag]: List of matching tags
        """
        search_pattern = f"%{search_term.lower().strip()}%"
        query = (
            select(Tag)
            .where(Tag.name.ilike(search_pattern))
            .order_by(desc(Tag.usage_count), Tag.name)
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_unused_tags(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[Tag]:
        """
        Get tags that are not being used.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tag]: List of unused tags
        """
        query = (
            select(Tag)
            .where(Tag.usage_count == 0)
            .order_by(Tag.created_at)
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def get_tags_by_usage_range(
        self,
        min_usage: int = 0,
        max_usage: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Tag]:
        """
        Get tags within a usage count range.
        
        Args:
            min_usage: Minimum usage count
            max_usage: Maximum usage count (None for no limit)
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tag]: List of tags within usage range
        """
        query = select(Tag).where(Tag.usage_count >= min_usage)
        
        if max_usage is not None:
            query = query.where(Tag.usage_count <= max_usage)
        
        query = (
            query
            .order_by(desc(Tag.usage_count), Tag.name)
            .offset(skip)
            .limit(limit)
        )
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def count_by_usage_range(
        self,
        min_usage: int = 0,
        max_usage: Optional[int] = None
    ) -> int:
        """
        Count tags within a usage count range.
        
        Args:
            min_usage: Minimum usage count
            max_usage: Maximum usage count (None for no limit)
            
        Returns:
            int: Number of tags within usage range
        """
        query = select(func.count(Tag.id)).where(Tag.usage_count >= min_usage)
        
        if max_usage is not None:
            query = query.where(Tag.usage_count <= max_usage)
        
        result = await self.db.execute(query)
        return result.scalar() or 0
    
    async def get_tags_by_creator(
        self,
        created_by: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Tag]:
        """
        Get tags created by a specific user.
        
        Args:
            created_by: User ID who created the tags
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tag]: List of tags created by the user
        """
        query = (
            select(Tag)
            .where(Tag.created_by == created_by)
            .order_by(desc(Tag.created_at))
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def exists_by_name(self, name: str) -> bool:
        """
        Check if a tag with the given name exists.
        
        Args:
            name: Tag name
            
        Returns:
            bool: True if tag exists, False otherwise
        """
        query = select(func.count(Tag.id)).where(Tag.name == name.lower().strip())
        result = await self.db.execute(query)
        count = result.scalar() or 0
        return count > 0
    
    async def bulk_increment_usage(self, tag_ids: List[int]) -> None:
        """
        Bulk increment usage count for multiple tags.
        
        Args:
            tag_ids: List of tag IDs
        """
        if tag_ids:
            tags_query = select(Tag).where(Tag.id.in_(tag_ids))
            result = await self.db.execute(tags_query)
            tags = list(result.scalars().all())
            
            for tag in tags:
                tag.increment_usage()
            
            await self.db.commit()
    
    async def bulk_decrement_usage(self, tag_ids: List[int]) -> None:
        """
        Bulk decrement usage count for multiple tags.
        
        Args:
            tag_ids: List of tag IDs
        """
        if tag_ids:
            tags_query = select(Tag).where(Tag.id.in_(tag_ids))
            result = await self.db.execute(tags_query)
            tags = list(result.scalars().all())
            
            for tag in tags:
                tag.decrement_usage()
            
            await self.db.commit()
