"""
MongoDB repository for dynamic record management.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import ASCENDING, DESCENDING, IndexModel
from pymongo.errors import Duplicate<PERSON>eyError

from app.schemas.dynamic_record import DynamicRecordCreate, DynamicRecordUpdate, DynamicRecordQuery
from app.utils.exceptions import ConflictError, NotFoundError, DatabaseError, ValidationError
from app.utils.logging import get_logger

logger = get_logger("repositories.dynamic_record")


class DynamicRecordRepository:
    """Repository for dynamic record MongoDB operations."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize repository with MongoDB database.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.collection = db.dynamic_records
    
    async def ensure_indexes(self) -> None:
        """Create necessary indexes for optimal performance."""
        try:
            indexes = [
                IndexModel([("schema_name", ASCENDING)]),
                IndexModel([("created_by", ASCENDING)]),
                IndexModel([("created_at", ASCENDING)]),
                IndexModel([("updated_at", ASCENDING)]),
                IndexModel([("schema_name", ASCENDING), ("created_at", DESCENDING)]),
                IndexModel([("schema_name", ASCENDING), ("data.email", ASCENDING)], sparse=True),
                IndexModel([("schema_name", ASCENDING), ("data.phone", ASCENDING)], sparse=True),
            ]
            await self.collection.create_indexes(indexes)
            logger.info("Dynamic record indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            raise DatabaseError(f"Failed to create indexes: {e}")
    
    async def create(self, record_data: DynamicRecordCreate) -> Dict[str, Any]:
        """
        Create a new dynamic record.
        
        Args:
            record_data: Record creation data
            
        Returns:
            Dict[str, Any]: Created record document
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            record_id = str(uuid4())
            
            document = {
                "record_id": record_id,
                "schema_name": record_data.schema_name,
                "data": record_data.data,
                "version": 1,
                "created_by": record_data.created_by,
                "updated_by": None,
                "created_at": now,
                "updated_at": now,
            }
            
            result = await self.collection.insert_one(document)
            
            # Retrieve the created document
            created_doc = await self.collection.find_one({"_id": result.inserted_id})
            if not created_doc:
                raise DatabaseError("Failed to retrieve created record")
            
            # Convert ObjectId to string and use record_id as id
            created_doc["id"] = created_doc["record_id"]
            del created_doc["_id"]
            del created_doc["record_id"]
            
            logger.info(f"Created record in schema: {record_data.schema_name}")
            return created_doc
            
        except Exception as e:
            logger.error(f"Failed to create record: {e}")
            raise DatabaseError(f"Failed to create record: {e}")
    
    async def get_by_id(self, record_id: str, schema_name: str) -> Optional[Dict[str, Any]]:
        """
        Get record by ID and schema name.
        
        Args:
            record_id: Record ID
            schema_name: Schema name
            
        Returns:
            Optional[Dict[str, Any]]: Record document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            document = await self.collection.find_one({
                "record_id": record_id,
                "schema_name": schema_name
            })
            
            if document:
                document["id"] = document["record_id"]
                del document["_id"]
                del document["record_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get record by ID {record_id}: {e}")
            raise DatabaseError(f"Failed to get record: {e}")
    
    async def get_multi(
        self,
        schema_name: str,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[List[Dict[str, Any]]] = None,
        sort_by: str = "created_at",
        sort_order: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Get multiple records with pagination and filtering.
        
        Args:
            schema_name: Schema name
            skip: Number of documents to skip
            limit: Maximum number of documents to return
            filters: Optional filters to apply
            sort_by: Field to sort by
            sort_order: Sort order (1 for ascending, -1 for descending)
            
        Returns:
            List[Dict[str, Any]]: List of record documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query: Dict[str, Any] = {"schema_name": schema_name}
            
            # Apply filters
            if filters:
                filter_conditions = []
                for filter_item in filters:
                    field = filter_item.get("field")
                    operator = filter_item.get("operator")
                    value = filter_item.get("value")
                    
                    if not all([field, operator, value is not None]):
                        continue
                    
                    # Build MongoDB query based on operator
                    if field is not None and isinstance(field, str):
                        field_path = f"data.{field}" if not field.startswith("data.") else field
                    else:
                        continue

                    if operator == "eq":
                        filter_conditions.append({field_path: value})
                    elif operator == "ne":
                        filter_conditions.append({field_path: {"$ne": value}})
                    elif operator == "gt":
                        filter_conditions.append({field_path: {"$gt": value}})
                    elif operator == "gte":
                        filter_conditions.append({field_path: {"$gte": value}})
                    elif operator == "lt":
                        filter_conditions.append({field_path: {"$lt": value}})
                    elif operator == "lte":
                        filter_conditions.append({field_path: {"$lte": value}})
                    elif operator == "in":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$in": value}})
                    elif operator == "nin":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$nin": value}})
                    elif operator == "contains":
                        filter_conditions.append({field_path: {"$regex": str(value), "$options": "i"}})
                    elif operator == "regex":
                        filter_conditions.append({field_path: {"$regex": str(value)}})
                
                if filter_conditions:
                    query["$and"] = filter_conditions
            
            # Handle sorting
            sort_field = f"data.{sort_by}" if not sort_by.startswith(("data.", "created_at", "updated_at")) else sort_by
            
            cursor = self.collection.find(query).sort(sort_field, sort_order).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            # Convert documents
            for doc in documents:
                doc["id"] = doc["record_id"]
                del doc["_id"]
                del doc["record_id"]
            
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get records for schema {schema_name}: {e}")
            raise DatabaseError(f"Failed to get records: {e}")
    
    async def count(self, schema_name: str, filters: Optional[List[Dict[str, Any]]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            schema_name: Schema name
            filters: Optional filters to apply
            
        Returns:
            int: Number of matching documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = {"schema_name": schema_name}
            
            # Apply filters (same logic as get_multi)
            if filters:
                filter_conditions = []
                for filter_item in filters:
                    field = filter_item.get("field")
                    operator = filter_item.get("operator")
                    value = filter_item.get("value")
                    
                    if not all([field, operator, value is not None]):
                        continue
                    
                    if field is not None and isinstance(field, str):
                        field_path = f"data.{field}" if not field.startswith("data.") else field
                    else:
                        continue
            
                    if operator == "eq":
                        filter_conditions.append({field_path: value})
                    elif operator == "ne":
                        filter_conditions.append({field_path: {"$ne": value}})
                    elif operator == "gt":
                        filter_conditions.append({field_path: {"$gt": value}})
                    elif operator == "gte":
                        filter_conditions.append({field_path: {"$gte": value}})
                    elif operator == "lt":
                        filter_conditions.append({field_path: {"$lt": value}})
                    elif operator == "lte":
                        filter_conditions.append({field_path: {"$lte": value}})
                    elif operator == "in":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$in": value}})
                    elif operator == "nin":
                        if isinstance(value, list):
                            filter_conditions.append({field_path: {"$nin": value}})
                    elif operator == "contains":
                        filter_conditions.append({field_path: {"$regex": str(value), "$options": "i"}})
                    elif operator == "regex":
                        filter_conditions.append({field_path: {"$regex": str(value)}})
                
                if filter_conditions:
                    query["$and"] = filter_conditions
            
            return await self.collection.count_documents(query)
            
        except Exception as e:
            logger.error(f"Failed to count records for schema {schema_name}: {e}")
            raise DatabaseError(f"Failed to count records: {e}")
    
    async def update(self, record_id: str, schema_name: str, update_data: DynamicRecordUpdate) -> Optional[Dict[str, Any]]:
        """
        Update an existing record.
        
        Args:
            record_id: Record ID
            schema_name: Schema name
            update_data: Update data
            
        Returns:
            Optional[Dict[str, Any]]: Updated record document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            # Build update document
            set_fields = {
                "data": update_data.data,
                "updated_at": datetime.now(timezone.utc),
                "updated_by": update_data.updated_by
            }
            
            # Build update operation
            update_operation = {
                "$set": set_fields,
                "$inc": {"version": 1}
            }
            
            # Perform update
            result = await self.collection.find_one_and_update(
                {"record_id": record_id, "schema_name": schema_name},
                update_operation,
                return_document=True
            )
            
            if result:
                result["id"] = result["record_id"]
                del result["_id"]
                del result["record_id"]
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to update record {record_id}: {e}")
            raise DatabaseError(f"Failed to update record: {e}")
    
    async def delete(self, record_id: str, schema_name: str) -> bool:
        """
        Delete a record.
        
        Args:
            record_id: Record ID
            schema_name: Schema name
            
        Returns:
            bool: True if deleted, False if not found
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            result = await self.collection.delete_one({
                "record_id": record_id,
                "schema_name": schema_name
            })
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete record {record_id}: {e}")
            raise DatabaseError(f"Failed to delete record: {e}")
    
    async def exists(self, record_id: str, schema_name: str) -> bool:
        """
        Check if record exists.
        
        Args:
            record_id: Record ID
            schema_name: Schema name
            
        Returns:
            bool: True if exists, False otherwise
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            count = await self.collection.count_documents({
                "record_id": record_id,
                "schema_name": schema_name
            }, limit=1)
            return count > 0
            
        except Exception as e:
            logger.error(f"Failed to check record existence {record_id}: {e}")
            raise DatabaseError(f"Failed to check record existence: {e}")
    
    async def bulk_create(self, schema_name: str, records_data: List[Dict[str, Any]], created_by: UUID) -> List[Dict[str, Any]]:
        """
        Create multiple records in bulk.
        
        Args:
            schema_name: Schema name
            records_data: List of record data
            created_by: User ID who created the records
            
        Returns:
            List[Dict[str, Any]]: List of created record documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            documents = []
            
            for data in records_data:
                record_id = str(uuid4())
                document = {
                    "record_id": record_id,
                    "schema_name": schema_name,
                    "data": data,
                    "version": 1,
                    "created_by": created_by,
                    "updated_by": None,
                    "created_at": now,
                    "updated_at": now,
                }
                documents.append(document)
            
            result = await self.collection.insert_many(documents)
            
            # Retrieve created documents
            created_docs = await self.collection.find({
                "_id": {"$in": result.inserted_ids}
            }).to_list(length=len(result.inserted_ids))
            
            # Convert documents
            for doc in created_docs:
                doc["id"] = doc["record_id"]
                del doc["_id"]
                del doc["record_id"]
            
            logger.info(f"Bulk created {len(created_docs)} records in schema: {schema_name}")
            return created_docs
            
        except Exception as e:
            logger.error(f"Failed to bulk create records: {e}")
            raise DatabaseError(f"Failed to bulk create records: {e}")
    
    async def get_schema_stats(self, schema_name: str) -> Dict[str, Any]:
        """
        Get statistics for records in a schema.
        
        Args:
            schema_name: Schema name
            
        Returns:
            Dict[str, Any]: Statistics data
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            today = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Aggregate statistics
            pipeline = [
                {"$match": {"schema_name": schema_name}},
                {
                    "$group": {
                        "_id": None,
                        "total_records": {"$sum": 1},
                        "created_today": {
                            "$sum": {
                                "$cond": [{"$gte": ["$created_at", today]}, 1, 0]
                            }
                        },
                        "updated_today": {
                            "$sum": {
                                "$cond": [{"$gte": ["$updated_at", today]}, 1, 0]
                            }
                        }
                    }
                }
            ]
            
            result = await self.collection.aggregate(pipeline).to_list(length=1)
            
            if result:
                stats = result[0]
                del stats["_id"]
                stats["schema_name"] = schema_name
                return stats
            else:
                return {
                    "schema_name": schema_name,
                    "total_records": 0,
                    "created_today": 0,
                    "updated_today": 0
                }
            
        except Exception as e:
            logger.error(f"Failed to get schema stats for {schema_name}: {e}")
            raise DatabaseError(f"Failed to get schema stats: {e}")
    
    async def delete_all_by_schema(self, schema_name: str) -> int:
        """
        Delete all records for a schema.
        
        Args:
            schema_name: Schema name
            
        Returns:
            int: Number of deleted records
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            result = await self.collection.delete_many({"schema_name": schema_name})
            logger.info(f"Deleted {result.deleted_count} records for schema: {schema_name}")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Failed to delete records for schema {schema_name}: {e}")
            raise DatabaseError(f"Failed to delete records for schema: {e}")
