"""
API dependencies for authentication, authorization, and database access.
"""

from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import verify_token
from app.models.user import User
from app.repositories.user_repository import UserRepository
from app.utils.exceptions import AuthenticationError, AuthorizationError

# Security scheme
security = HTTPBearer()


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Get current authenticated user from JWT token.
    
    Args:
        db: Database session
        token: JWT token from Authorization header
        
    Returns:
        User: Current authenticated user
        
    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        # Verify token
        user_id = verify_token(token.credentials, "access")
        if user_id is None:
            raise credentials_exception
            
        # Get user from database
        user_repo = UserRepository(db)
        user = await user_repo.get_with_roles(int(user_id))
        
        if user is None:
            raise credentials_exception
            
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
            
        if user.is_locked:
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="Account is locked"
            )
            
        return user
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise credentials_exception


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active user.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current active user
        
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current superuser.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User: Current superuser
        
    Raises:
        HTTPException: If user is not a superuser
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user


def require_permission(permission: str):
    """
    Dependency factory for requiring specific permissions.
    
    Args:
        permission: Required permission (format: "resource:action")
        
    Returns:
        Dependency function
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """
        Check if current user has required permission.
        
        Args:
            current_user: Current authenticated user
            
        Returns:
            User: Current user if permission check passes
            
        Raises:
            HTTPException: If user doesn't have required permission
        """
        if current_user.is_superuser:
            return current_user
            
        # Parse permission
        try:
            resource, action = permission.split(":", 1)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Invalid permission format"
            )
        
        # Check permission
        if not current_user.has_permission(resource, action):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {permission}"
            )
            
        return current_user
    
    return permission_checker


def require_role(role: str):
    """
    Dependency factory for requiring specific roles.
    
    Args:
        role: Required role name
        
    Returns:
        Dependency function
    """
    async def role_checker(
        current_user: User = Depends(get_current_active_user),
    ) -> User:
        """
        Check if current user has required role.
        
        Args:
            current_user: Current authenticated user
            
        Returns:
            User: Current user if role check passes
            
        Raises:
            HTTPException: If user doesn't have required role
        """
        if current_user.is_superuser:
            return current_user
            
        if not current_user.has_role(role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role required: {role}"
            )
            
        return current_user
    
    return role_checker


# Optional authentication (for endpoints that work with or without auth)
async def get_current_user_optional(
    db: AsyncSession = Depends(get_db),
    token: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    Get current user if authenticated, None otherwise.
    
    Args:
        db: Database session
        token: Optional JWT token from Authorization header
        
    Returns:
        Optional[User]: Current user or None
    """
    if not token:
        return None
        
    try:
        user_id = verify_token(token.credentials, "access")
        if user_id is None:
            return None
            
        user_repo = UserRepository(db)
        user = await user_repo.get_with_roles(int(user_id))
        
        if user and user.is_active and not user.is_locked:
            return user
            
    except Exception:
        pass
        
    return None
