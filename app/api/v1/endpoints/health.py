"""
Health check endpoints for monitoring application and service status.
"""

from datetime import datetime, timezone
from typing import Dict

from fastapi import APIRouter, Depends, status
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.connections import get_temporal_service
from app.core.database import get_db, get_mongodb, get_redis
from app.schemas.common import HealthCheck
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.health")


@router.get("/health", response_model=HealthCheck, status_code=status.HTTP_200_OK)
async def health_check(
    db: AsyncSession = Depends(get_db),
) -> HealthCheck:
    """
    Comprehensive health check endpoint.
    
    Returns:
        HealthCheck: Application and service health status
    """
    services = {}
    overall_status = "healthy"
    
    # Check PostgreSQL
    try:
        await db.execute(text("SELECT 1"))
        services["postgresql"] = "healthy"
        logger.debug("PostgreSQL health check passed")
    except Exception as e:
        services["postgresql"] = "unhealthy"
        overall_status = "unhealthy"
        logger.error("PostgreSQL health check failed", error=str(e))
    
    # Check MongoDB
    try:
        mongodb = await get_mongodb()
        if mongodb is not None:
            await mongodb.command("ping")
            services["mongodb"] = "healthy"
            logger.debug("MongoDB health check passed")
        else:
            services["mongodb"] = "unavailable"
            overall_status = "degraded"
    except Exception as e:
        services["mongodb"] = "unhealthy"
        overall_status = "unhealthy"
        logger.error("MongoDB health check failed", error=str(e))
    
    # Check Redis
    try:
        redis = await get_redis()
        if redis is not None:
            await redis.ping()
            services["redis"] = "healthy"
            logger.debug("Redis health check passed")
        else:
            services["redis"] = "unavailable"
            overall_status = "degraded"
    except Exception as e:
        services["redis"] = "unhealthy"
        overall_status = "unhealthy"
        logger.error("Redis health check failed", error=str(e))
    
    # Check RabbitMQ (basic connectivity)
    try:
        # This is a simplified check - in production you might want to use aio-pika
        services["rabbitmq"] = "healthy"  # Placeholder
        logger.debug("RabbitMQ health check passed")
    except Exception as e:
        services["rabbitmq"] = "unhealthy"
        overall_status = "unhealthy"
        logger.error("RabbitMQ health check failed", error=str(e))

    # Check Temporal
    try:
        # This is a simplified check - in production you might want to make HTTP request
        temporal = await get_temporal_service()
        services["temporal"] = temporal.is_connected() and "healthy"  or "unhealthy"
        logger.debug("Temporal health check passed")
    except Exception as e:
        services["temporal"] = "unhealthy"
        overall_status = "unhealthy"
        logger.error("Temporal health check failed", error=str(e))
    
    return HealthCheck(
        status=overall_status,
        timestamp=datetime.now(timezone.utc),
        version=settings.APP_VERSION,
        services=services
    )


@router.get("/health/live", status_code=status.HTTP_200_OK)
async def liveness_probe() -> Dict[str, str]:
    """
    Kubernetes liveness probe endpoint.
    
    Returns:
        Dict[str, str]: Simple alive status
    """
    return {"status": "alive"}


@router.get("/health/ready", status_code=status.HTTP_200_OK)
async def readiness_probe(
    db: AsyncSession = Depends(get_db),
) -> Dict[str, str]:
    """
    Kubernetes readiness probe endpoint.
    
    Args:
        db: Database session
        
    Returns:
        Dict[str, str]: Readiness status
    """
    try:
        # Check if database is accessible
        await db.execute(text("SELECT 1"))
        return {"status": "ready"}
    except Exception as e:
        logger.error("Readiness probe failed", error=str(e))
        return {"status": "not ready", "error": str(e)}


@router.get("/health/startup", status_code=status.HTTP_200_OK)
async def startup_probe() -> Dict[str, str]:
    """
    Kubernetes startup probe endpoint.
    
    Returns:
        Dict[str, str]: Startup status
    """
    return {"status": "started"}
