"""
Tag API endpoints for tag management.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.common import PaginationParams
from app.schemas.tag import (
    TagCreate,
    TagUpdate,
    Tag as TagSchema,
    TagDetail,
    TagList
)
from app.services.tag_service import TagService
from app.utils.exceptions import (
    NotFoundError,
    ValidationError,
    ConflictError
)
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.tags")


@router.post("/", response_model=TagSchema, status_code=status.HTTP_201_CREATED)
async def create_tag(
    tag_data: TagCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> TagSchema:
    """
    Create a new tag.
    
    This endpoint creates a new tag with proper validation to ensure
    unique tag names and proper formatting.
    
    Args:
        tag_data: Tag creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TagSchema: Created tag
        
    Raises:
        ConflictError: If tag name already exists
        ValidationError: If tag data is invalid
    """
    try:
        # Set created_by from current user
        tag_data.created_by = current_user.id
        
        tag_service = TagService(db)
        tag = await tag_service.create_tag(tag_data)
        
        logger.info(
            "Tag created via API",
            tag_id=tag.id,
            tag_name=tag.name,
            created_by=current_user.id
        )
        
        return TagSchema.model_validate(tag)
        
    except ConflictError as e:
        logger.warning(f"Tag creation conflict: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Tag creation validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating tag: {str(e)}")
        raise


@router.get("/{tag_id}", response_model=TagDetail, status_code=status.HTTP_200_OK)
async def get_tag(
    tag_id: int,
    include_workflows: bool = Query(False, description="Include associated workflows"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> TagDetail:
    """
    Get a tag by ID.
    
    This endpoint retrieves a tag with optional inclusion of
    associated workflows for detailed analysis.
    
    Args:
        tag_id: Tag ID
        include_workflows: Whether to include associated workflows
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TagDetail: Tag data with optional workflow associations
        
    Raises:
        NotFoundError: If tag not found
    """
    try:
        tag_service = TagService(db)
        
        if include_workflows:
            tag = await tag_service.get_tag_with_workflows(tag_id)
        else:
            tag = await tag_service.get_tag_by_id(tag_id)
        
        logger.info(
            "Tag retrieved via API",
            tag_id=tag_id,
            include_workflows=include_workflows,
            requested_by=current_user.id
        )
        
        return TagDetail.model_validate(tag)
        
    except NotFoundError as e:
        logger.warning(f"Tag not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving tag {tag_id}: {str(e)}")
        raise


@router.get("/", response_model=TagList, status_code=status.HTTP_200_OK)
async def get_tags(
    pagination: PaginationParams = Depends(),
    search: Optional[str] = Query(None, description="Search term for tag names"),
    min_usage: Optional[int] = Query(None, ge=0, description="Minimum usage count"),
    max_usage: Optional[int] = Query(None, ge=0, description="Maximum usage count"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> TagList:
    """
    Get paginated list of tags with filtering options.
    
    This endpoint supports:
    - Pagination with skip/limit
    - Search by tag name
    - Filtering by usage count range
    - Filtering by creator
    
    Args:
        pagination: Pagination parameters (skip, limit)
        search: Search term for tag names
        min_usage: Minimum usage count filter
        max_usage: Maximum usage count filter
        created_by: Filter by creator user ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TagList: Paginated tag list with metadata
    """
    try:
        # Validate usage range
        if min_usage is not None and max_usage is not None and min_usage > max_usage:
            raise ValidationError("min_usage cannot be greater than max_usage")
        
        tag_service = TagService(db)
        tag_list = await tag_service.get_tags_paginated(
            skip=pagination.skip,
            limit=pagination.limit,
            search=search,
            min_usage=min_usage,
            max_usage=max_usage,
            created_by=created_by
        )
        
        logger.info(
            "Tags list retrieved via API",
            total_count=tag_list.total,
            page=tag_list.page,
            search_term=search,
            requested_by=current_user.id
        )
        
        return tag_list
        
    except ValidationError as e:
        logger.warning(f"Tag list validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving tags: {str(e)}")
        raise


@router.put("/{tag_id}", response_model=TagSchema, status_code=status.HTTP_200_OK)
async def update_tag(
    tag_id: int,
    tag_update: TagUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> TagSchema:
    """
    Update an existing tag.
    
    This endpoint allows updating tag properties while maintaining
    data integrity and preventing name conflicts.
    
    Args:
        tag_id: Tag ID
        tag_update: Tag update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TagSchema: Updated tag
        
    Raises:
        NotFoundError: If tag not found
        ConflictError: If new name already exists
        ValidationError: If update data is invalid
    """
    try:
        # Set edited_by from current user
        tag_update.edited_by = current_user.id
        
        tag_service = TagService(db)
        tag = await tag_service.update_tag(tag_id, tag_update)
        
        logger.info(
            "Tag updated via API",
            tag_id=tag_id,
            tag_name=tag.name,
            updated_by=current_user.id
        )
        
        return TagSchema.model_validate(tag)
        
    except NotFoundError as e:
        logger.warning(f"Tag update failed - not found: {str(e)}")
        raise
    except ConflictError as e:
        logger.warning(f"Tag update conflict: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Tag update validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating tag {tag_id}: {str(e)}")
        raise


@router.delete("/{tag_id}", response_model=TagSchema, status_code=status.HTTP_200_OK)
async def delete_tag(
    tag_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> TagSchema:
    """
    Delete a tag with proper cascade handling.
    
    This endpoint safely deletes a tag only if it's not currently
    in use by any workflows. This prevents data integrity issues.
    
    Args:
        tag_id: Tag ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        TagSchema: Deleted tag data
        
    Raises:
        NotFoundError: If tag not found
        ValidationError: If tag is still in use
    """
    try:
        tag_service = TagService(db)
        tag = await tag_service.delete_tag(tag_id)
        
        logger.info(
            "Tag deleted via API",
            tag_id=tag_id,
            tag_name=tag.name,
            deleted_by=current_user.id
        )
        
        return TagSchema.model_validate(tag)
        
    except NotFoundError as e:
        logger.warning(f"Tag deletion failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Tag deletion validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting tag {tag_id}: {str(e)}")
        raise


@router.get("/popular/list", response_model=List[TagSchema], status_code=status.HTTP_200_OK)
async def get_popular_tags(
    limit: int = Query(10, ge=1, le=50, description="Maximum number of tags to return"),
    min_usage: int = Query(1, ge=0, description="Minimum usage count"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> List[TagSchema]:
    """
    Get most popular tags by usage count.
    
    This endpoint returns the most frequently used tags,
    useful for tag suggestions and analytics.
    
    Args:
        limit: Maximum number of tags to return
        min_usage: Minimum usage count
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[TagSchema]: List of popular tags ordered by usage
    """
    try:
        tag_service = TagService(db)
        tags = await tag_service.get_popular_tags(limit, min_usage)
        
        logger.info(
            "Popular tags retrieved via API",
            count=len(tags),
            limit=limit,
            min_usage=min_usage,
            requested_by=current_user.id
        )
        
        return [TagSchema.model_validate(tag) for tag in tags]
        
    except Exception as e:
        logger.error(f"Unexpected error retrieving popular tags: {str(e)}")
        raise


@router.get("/unused/list", response_model=List[TagSchema], status_code=status.HTTP_200_OK)
async def get_unused_tags(
    pagination: PaginationParams = Depends(),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> List[TagSchema]:
    """
    Get tags that are not being used.
    
    This endpoint returns tags with zero usage count,
    useful for cleanup and maintenance operations.
    
    Args:
        pagination: Pagination parameters
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[TagSchema]: List of unused tags
    """
    try:
        tag_service = TagService(db)
        tags = await tag_service.get_unused_tags(pagination.skip, pagination.limit)
        
        logger.info(
            "Unused tags retrieved via API",
            count=len(tags),
            requested_by=current_user.id
        )
        
        return [TagSchema.model_validate(tag) for tag in tags]
        
    except Exception as e:
        logger.error(f"Unexpected error retrieving unused tags: {str(e)}")
        raise
