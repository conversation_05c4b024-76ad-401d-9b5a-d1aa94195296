"""
User management endpoints with CRUD operations and role management.
"""

from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user, get_current_superuser, require_permission
from app.core.database import get_db
from app.models.user import User
from app.schemas.auth import UserRoleAssignment
from app.schemas.common import PaginatedResponse, PaginationParams, SuccessResponse
from app.schemas.user import User as UserSchema, UserCreate, UserList, UserStats, UserUpdate
from app.services.user_service import UserService
from app.utils.exceptions import ConflictError, NotFoundError, ValidationError
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.users")


@router.get("/", response_model=PaginatedResponse[UserList], status_code=status.HTTP_200_OK)
async def get_users(
    pagination: PaginationParams = Depends(),
    current_user: User = Depends(require_permission("user:read")),
    db: AsyncSession = Depends(get_db)
) -> PaginatedResponse[UserList]:
    """
    Get paginated list of users.
    
    Args:
        pagination: Pagination parameters
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        PaginatedResponse[UserList]: Paginated user list
    """
    try:
        user_service = UserService(db)
        
        users = await user_service.get_multi(
            skip=pagination.skip,
            limit=pagination.limit,
            order_by="created_at"
        )
        
        total = await user_service.count()
        
        user_list = [
            UserList(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                is_active=user.is_active,
                is_verified=user.is_verified,
                is_superuser=user.is_superuser,
                avatar_url=user.avatar_url,
                last_login=user.last_login,
                created_at=user.created_at,
                roles=user.get_roles_list(),
            )
            for user in users
        ]
        
        return PaginatedResponse.create(
            items=user_list,
            total=total,
            skip=pagination.skip,
            limit=pagination.limit
        )
        
    except Exception as e:
        logger.error("Error getting users", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get users"
        )


@router.get("/search", response_model=List[UserList], status_code=status.HTTP_200_OK)
async def search_users(
    q: str = Query(..., min_length=2, description="Search query"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    current_user: User = Depends(require_permission("user:read")),
    db: AsyncSession = Depends(get_db)
) -> List[UserList]:
    """
    Search users by email or full name.
    
    Args:
        q: Search query
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[UserList]: List of matching users
    """
    try:
        user_service = UserService(db)
        users = await user_service.search_users(q, skip=skip, limit=limit)
        
        return [
            UserList(
                id=user.id,
                email=user.email,
                full_name=user.full_name,
                is_active=user.is_active,
                is_verified=user.is_verified,
                is_superuser=user.is_superuser,
                avatar_url=user.avatar_url,
                last_login=user.last_login,
                created_at=user.created_at,
                roles=user.get_roles_list(),
            )
            for user in users
        ]
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error searching users", query=q, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search users"
        )


@router.get("/stats", response_model=UserStats, status_code=status.HTTP_200_OK)
async def get_user_statistics(
    current_user: User = Depends(require_permission("admin:read")),
    db: AsyncSession = Depends(get_db)
) -> UserStats:
    """
    Get user statistics.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserStats: User statistics
    """
    try:
        user_service = UserService(db)
        stats = await user_service.get_user_statistics()
        
        logger.info("User statistics retrieved", user_id=current_user.id)
        return stats
        
    except Exception as e:
        logger.error("Error getting user statistics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user statistics"
        )


@router.get("/{user_id}", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def get_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:read")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Get user by ID.
    
    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserSchema: User data
    """
    try:
        user_service = UserService(db)
        user = await user_service.get_user_with_roles(user_id)
        
        return UserSchema.model_validate(user)
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error getting user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user"
        )


@router.post("/", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Create a new user.
    
    Args:
        user_data: User creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserSchema: Created user data
    """
    try:
        user_service = UserService(db)
        user = await user_service.create(user_data)
        
        logger.info("User created", user_id=user.id, created_by=current_user.id)
        return UserSchema.model_validate(user)
        
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error creating user", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user"
        )


@router.put("/{user_id}", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Update user by ID.
    
    Args:
        user_id: User ID
        user_data: User update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserSchema: Updated user data
    """
    try:
        user_service = UserService(db)
        user = await user_service.update(user_id, user_data)
        
        logger.info("User updated", user_id=user.id, updated_by=current_user.id)
        return UserSchema.model_validate(user)
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error updating user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


# Role management endpoints
@router.post("/{user_id}/roles", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def assign_roles_to_user(
    user_id: int,
    assignment: UserRoleAssignment,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Assign roles to user.

    Args:
        user_id: User ID
        assignment: Role assignment data
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user with new roles
    """
    try:
        # Override user_id from URL
        assignment.user_id = user_id

        user_service = UserService(db)
        user = await user_service.assign_roles_to_user(assignment)

        logger.info("Roles assigned to user", user_id=user.id, assigned_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error assigning roles to user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign roles to user"
        )


@router.post("/{user_id}/roles/{role_id}", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def add_role_to_user(
    user_id: int,
    role_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Add a single role to user.

    Args:
        user_id: User ID
        role_id: Role ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.add_role_to_user(user_id, role_id)

        logger.info("Role added to user", user_id=user.id, role_id=role_id, added_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error adding role to user", user_id=user_id, role_id=role_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add role to user"
        )


@router.delete("/{user_id}/roles/{role_id}", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def remove_role_from_user(
    user_id: int,
    role_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Remove a role from user.

    Args:
        user_id: User ID
        role_id: Role ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.remove_role_from_user(user_id, role_id)

        logger.info("Role removed from user", user_id=user.id, role_id=role_id, removed_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error removing role from user", user_id=user_id, role_id=role_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove role from user"
        )


# User status management endpoints
@router.post("/{user_id}/activate", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def activate_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Activate user account.

    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.activate_user(user_id)

        logger.info("User activated", user_id=user.id, activated_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error activating user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate user"
        )


@router.post("/{user_id}/deactivate", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def deactivate_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Deactivate user account.

    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.deactivate_user(user_id)

        logger.info("User deactivated", user_id=user.id, deactivated_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error deactivating user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user"
        )


@router.post("/{user_id}/verify", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def verify_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Verify user account.

    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.verify_user(user_id)

        logger.info("User verified", user_id=user.id, verified_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error verifying user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify user"
        )


@router.post("/{user_id}/unlock", response_model=UserSchema, status_code=status.HTTP_200_OK)
async def unlock_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:write")),
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Unlock user account.

    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        UserSchema: Updated user
    """
    try:
        user_service = UserService(db)
        user = await user_service.unlock_user(user_id)

        logger.info("User unlocked", user_id=user.id, unlocked_by=current_user.id)
        return UserSchema.model_validate(user)

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error unlocking user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unlock user"
        )


@router.delete("/{user_id}", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_permission("user:delete")),
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse:
    """
    Delete user by ID.
    
    Args:
        user_id: User ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Success message
    """
    try:
        user_service = UserService(db)
        await user_service.delete(user_id)
        
        logger.info("User deleted", user_id=user_id, deleted_by=current_user.id)
        return SuccessResponse(message="User deleted successfully")
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error deleting user", user_id=user_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )
