import asyncio
from typing import Any
from fastapi import APIRouter, HTTPException
from fastapi.params import Depends
from app.core.database import get_db
from app.node.node_base.node_models import NodeFunctionRequest
from app.node.node_utils.registry import NodeRegistry, node_registry
from app.schemas.common import PaginatedResponse, PaginationParams
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()

@router.get("/", response_model=list[dict])
async def get_nodes():
    nodes: list[dict] = []

    try:
        # Get registered node descriptions
        descriptions = node_registry.list_node_descriptions()
        
        # Use the to_api_format method to properly handle child class properties
        for desc in descriptions:
            nodes.append(desc.dict())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving nodes: {str(e)}")

    return nodes

@router.post("/options", response_model=Any)
async def get_node_options(
        node_request: NodeFunctionRequest
):
    """
    Get options for a specific node type.
    
    Args:
        node_request: NodeFunctionRequest containing the node type and parameters.
        
    Returns:
        List of options for the specified node type.
    """
    try:
        # Use the registry to get the node description
        node = node_registry.get(node_request.node_request.node_type)
        
        if not node:
            raise HTTPException(status_code=404, detail="Node not found")
        
        fn = getattr(node, node_request.function, None)
        if not fn or not callable(fn):
            raise HTTPException(status_code=404, detail="Function not found")

        try:
            # return await fn(node_request.node_request)
            if asyncio.iscoroutinefunction(fn):
                return await fn(node_request.node_request)
            else:
                return fn(node_request.node_request)
        except TypeError as exc:
            raise HTTPException(status_code=400, detail=str(exc))

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))