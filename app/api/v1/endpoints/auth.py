"""
Authentication endpoints for login, registration, and password management.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.auth import (
    ChangePasswordRequest,
    LoginRequest,
    PasswordResetConfirm,
    PasswordResetRequest,
    RefreshTokenRequest,
    Token,
)
from app.schemas.common import SuccessResponse
from app.schemas.user import User as UserSchema, UserCreate, UserProfile
from app.services.auth_service import AuthService
from app.utils.exceptions import (
    AuthenticationError,
    ConflictError,
    NotFoundError,
    ValidationError,
)
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.auth")


@router.post("/login", response_model=Token, status_code=status.HTTP_200_OK)
async def login(
    login_data: LoginRequest,
    db: AsyncSession = Depends(get_db)
) -> Token:
    """
    Login user with email and password.
    
    Args:
        login_data: Login credentials
        db: Database session
        
    Returns:
        Token: JWT access and refresh tokens
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        auth_service = AuthService(db)
        token = await auth_service.login(login_data)
        
        logger.info("User login successful", email=login_data.email)
        return token
        
    except AuthenticationError as e:
        logger.warning("Login failed", email=login_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error("Login error", email=login_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/register", response_model=UserSchema, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
) -> UserSchema:
    """
    Register a new user.
    
    Args:
        user_data: User registration data
        db: Database session
        
    Returns:
        UserSchema: Created user data
        
    Raises:
        HTTPException: If registration fails
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.register(user_data)
        
        logger.info("User registration successful", email=user_data.email, user_id=user.id)
        return UserSchema.model_validate(user)
        
    except ConflictError as e:
        logger.warning("Registration failed - conflict", email=user_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning("Registration failed - validation", email=user_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Registration error", email=user_data.email, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/refresh", response_model=Token, status_code=status.HTTP_200_OK)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
) -> Token:
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_data: Refresh token data
        db: Database session
        
    Returns:
        Token: New JWT tokens
        
    Raises:
        HTTPException: If token refresh fails
    """
    try:
        auth_service = AuthService(db)
        token = await auth_service.refresh_token(refresh_data.refresh_token)
        
        logger.info("Token refresh successful")
        return token
        
    except AuthenticationError as e:
        logger.warning("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error("Token refresh error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/password-reset/request", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def request_password_reset(
    reset_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse:
    """
    Request password reset for user.
    
    Args:
        reset_data: Password reset request data
        db: Database session
        
    Returns:
        SuccessResponse: Success message
        
    Note:
        Always returns success to prevent email enumeration attacks.
    """
    try:
        auth_service = AuthService(db)
        reset_token = await auth_service.request_password_reset(reset_data)
        
        logger.info("Password reset requested", email=reset_data.email)
        
        # In production, send email with reset_token
        # For now, we just return success
        return SuccessResponse(
            message="If an account with this email exists, a password reset link has been sent."
        )
        
    except Exception as e:
        logger.error("Password reset request error", email=reset_data.email, error=str(e))
        # Still return success to prevent email enumeration
        return SuccessResponse(
            message="If an account with this email exists, a password reset link has been sent."
        )


@router.post("/password-reset/confirm", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse:
    """
    Confirm password reset with token and new password.
    
    Args:
        reset_data: Password reset confirmation data
        db: Database session
        
    Returns:
        SuccessResponse: Success message
        
    Raises:
        HTTPException: If password reset fails
    """
    try:
        auth_service = AuthService(db)
        success = await auth_service.reset_password(reset_data)
        
        if success:
            logger.info("Password reset successful")
            return SuccessResponse(message="Password has been reset successfully.")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password reset failed"
            )
            
    except AuthenticationError as e:
        logger.warning("Password reset failed - authentication", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except NotFoundError as e:
        logger.warning("Password reset failed - not found", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning("Password reset failed - validation", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Password reset error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset failed"
        )


@router.post("/change-password", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def change_password(
    change_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse:
    """
    Change user password.
    
    Args:
        change_data: Password change data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Success message
        
    Raises:
        HTTPException: If password change fails
    """
    try:
        auth_service = AuthService(db)
        success = await auth_service.change_password(current_user, change_data)
        
        if success:
            logger.info("Password change successful", user_id=current_user.id)
            return SuccessResponse(message="Password has been changed successfully.")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password change failed"
            )
            
    except AuthenticationError as e:
        logger.warning("Password change failed", user_id=current_user.id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Password change error", user_id=current_user.id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.post("/logout", response_model=SuccessResponse, status_code=status.HTTP_200_OK)
async def logout(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> SuccessResponse:
    """
    Logout current user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Success message
    """
    try:
        auth_service = AuthService(db)
        success = await auth_service.logout(current_user)
        
        if success:
            logger.info("User logout successful", user_id=current_user.id)
            return SuccessResponse(message="Logged out successfully.")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Logout failed"
            )
            
    except Exception as e:
        logger.error("Logout error", user_id=current_user.id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me", response_model=UserProfile, status_code=status.HTTP_200_OK)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> UserProfile:
    """
    Get current user profile.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserProfile: Current user profile data
    """
    return UserProfile(
        id=current_user.id,
        email=current_user.email,
        full_name=current_user.full_name,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        is_superuser=current_user.is_superuser,
        avatar_url=current_user.avatar_url,
        bio=current_user.bio,
        timezone=current_user.timezone,
        language=current_user.language,
        last_login=current_user.last_login,
        created_at=current_user.created_at,
        updated_at=current_user.updated_at,
        roles=current_user.get_roles_list(),
        permissions=current_user.get_permissions_list(),
    )
