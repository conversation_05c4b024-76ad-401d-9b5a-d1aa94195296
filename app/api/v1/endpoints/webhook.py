"""
Webhook API endpoints for handling incoming webhook requests.

This module provides endpoints for webhook functionality including
webhook registration, handling incoming requests, and authentication.
"""

import json
import logging
import base64
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Request, Response, HTTPException, Depends, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.core.database import get_db
from app.utils.exceptions import CerebroException
from app.utils.logging import get_logger


# Set up logging
logger = get_logger("app.api.webhook")

router = APIRouter()


class WebhookResponse(BaseModel):
    """Response model for webhook operations."""
    message: str
    webhook_id: Optional[str] = None
    timestamp: datetime


class WebhookRegistration(BaseModel):
    """Model for webhook registration."""
    webhook_path: str
    node_id: str
    workflow_id: str
    http_methods: list[str]
    authentication_required: bool = False


# In-memory webhook registry (in production, this would be in a database)
webhook_registry: Dict[str, Dict[str, Any]] = {
    "example_webhook": {
        "webhook_id": "example_123",
        "node_id": "node_1",
        "workflow_id": "1efc6abc-96fb-46fb-8d27-b521720b5b93",
        "http_methods": ["POST"],
        "authentication_required": True,
        "authentication": {
            "method": "basic",
            "username": "webhook_user",
            "password": "secure_password",
        },
        "created_at": datetime.utcnow(),
        "active": True
    }
}


def get_client_ip(request: Request) -> str:
    """
    Extract client IP address from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: Client IP address
    """
    # Check for forwarded headers first (for reverse proxies)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fall back to direct connection IP
    return request.client.host if request.client else "unknown"


def parse_request_body(request: Request, body: bytes) -> Dict[str, Any]:
    """
    Parse request body based on content type.
    
    Args:
        request: FastAPI request object
        body: Raw request body
        
    Returns:
        Dict containing parsed body data
    """
    content_type = request.headers.get("content-type", "").lower()
    
    try:
        if "application/json" in content_type:
            return json.loads(body.decode("utf-8"))
        elif "application/x-www-form-urlencoded" in content_type:
            from urllib.parse import parse_qs
            body_str = body.decode("utf-8")
            return dict(parse_qs(body_str, keep_blank_values=True))
        elif "text/" in content_type:
            return {"text": body.decode("utf-8")}
        else:
            # For binary data, return base64 encoded
            return {"binary": base64.b64encode(body).decode("utf-8")}
    except Exception as e:
        logger.warning(f"Failed to parse request body: {str(e)}")
        return {"raw": body.decode("utf-8", errors="ignore")}


async def authenticate_webhook(request: Request, webhook_config: Dict[str, Any]) -> bool:
    """
    Authenticate webhook request based on configuration.
    
    Args:
        request: FastAPI request object
        webhook_config: Webhook configuration
        
    Returns:
        bool: True if authentication successful
    """
    auth_config = webhook_config.get("authentication", {})
    auth_method = auth_config.get("method", "none")
    
    if auth_method == "none":
        return True
    
    elif auth_method == "basic":
        auth_header = request.headers.get("authorization", "")
        if not auth_header.startswith("Basic "):
            return False
        
        try:
            encoded_credentials = auth_header[6:]  # Remove "Basic "
            decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
            username, password = decoded_credentials.split(":", 1)
            
            expected_username = auth_config.get("username", "")
            expected_password = auth_config.get("password", "")
            
            return username == expected_username and password == expected_password
        except Exception:
            return False
    
    elif auth_method == "header":
        header_name = auth_config.get("header_name", "").lower()
        expected_value = auth_config.get("header_value", "")
        actual_value = request.headers.get(header_name, "")
        
        return actual_value == expected_value
    
    elif auth_method == "jwt":
        auth_header = request.headers.get("authorization", "")
        if not auth_header.startswith("Bearer "):
            return False
        
        token = auth_header[7:]  # Remove "Bearer "
        
        try:
            import jwt
            secret = auth_config.get("secret", "")
            algorithm = auth_config.get("algorithm", "HS256")
            
            jwt.decode(token, secret, algorithms=[algorithm])
            return True
        except Exception:
            return False
    
    return False


@router.post("/register")
async def register_webhook(
    registration: WebhookRegistration,
    db=Depends(get_db)
) -> WebhookResponse:
    """
    Register a new webhook endpoint.
    
    Args:
        registration: Webhook registration data
        db: Database session
        
    Returns:
        WebhookResponse: Registration confirmation
    """
    try:
        webhook_id = f"{registration.workflow_id}_{registration.node_id}"
        
        # Store webhook configuration
        webhook_registry[registration.webhook_path] = {
            "webhook_id": webhook_id,
            "node_id": registration.node_id,
            "workflow_id": registration.workflow_id,
            "http_methods": registration.http_methods,
            "authentication_required": registration.authentication_required,
            "created_at": datetime.utcnow(),
            "active": True
        }
        
        logger.info(f"Webhook registered: {registration.webhook_path} -> {webhook_id}")
        
        return WebhookResponse(
            message="Webhook registered successfully",
            webhook_id=webhook_id,
            timestamp=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"Failed to register webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to register webhook"
        )


@router.delete("/unregister/{webhook_path}")
async def unregister_webhook(
    webhook_path: str,
    db=Depends(get_db)
) -> WebhookResponse:
    """
    Unregister a webhook endpoint.
    
    Args:
        webhook_path: Path of the webhook to unregister
        db: Database session
        
    Returns:
        WebhookResponse: Unregistration confirmation
    """
    try:
        if webhook_path in webhook_registry:
            del webhook_registry[webhook_path]
            logger.info(f"Webhook unregistered: {webhook_path}")
            
            return WebhookResponse(
                message="Webhook unregistered successfully",
                timestamp=datetime.utcnow()
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Webhook not found"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to unregister webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unregister webhook"
        )


@router.api_route("/trigger/{webhook_path}", methods=["GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"])
async def handle_webhook(
    webhook_path: str,
    request: Request,
    db=Depends(get_db)
):
    """
    Handle incoming webhook requests.
    
    Args:
        webhook_path: The webhook path
        request: FastAPI request object
        db: Database session
        
    Returns:
        Response based on webhook configuration
    """
    try:
        # Check if webhook is registered
        if webhook_path not in webhook_registry:
            logger.warning(f"Webhook not found: {webhook_path}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Webhook not found"
            )
        
        webhook_config = webhook_registry[webhook_path]
        
        # Check if webhook is active
        if not webhook_config.get("active", True):
            logger.warning(f"Webhook inactive: {webhook_path}")
            raise HTTPException(
                status_code=status.HTTP_410_GONE,
                detail="Webhook is no longer active"
            )
        
        # Validate HTTP method
        allowed_methods = webhook_config.get("http_methods", ["GET", "POST"])
        if request.method not in allowed_methods:
            logger.warning(f"Method not allowed for webhook {webhook_path}: {request.method}")
            raise HTTPException(
                status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
                detail=f"Method {request.method} not allowed"
            )
        
        # Authenticate request if required
        if webhook_config.get("authentication_required", False):
            if not await authenticate_webhook(request, webhook_config):
                logger.warning(f"Authentication failed for webhook: {webhook_path}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication failed"
                )
        
        # Read request body
        body = await request.body()
        
        # Prepare webhook data
        webhook_data = {
            "webhook_path": webhook_path,
            "method": request.method,
            "headers": dict(request.headers),
            "query": dict(request.query_params),
            "body": parse_request_body(request, body),
            "client_ip": get_client_ip(request),
            "timestamp": datetime.utcnow().isoformat(),
            "webhook_id": webhook_config["webhook_id"],
            "node_id": webhook_config["node_id"],
            "workflow_id": webhook_config["workflow_id"]
        }
        
        # Trigger workflow execution with webhook data
        from app.services.workflow_service import WorkFlowService
        
        try:
            # Get workflow ID from webhook config
            workflow_id = webhook_config["workflow_id"]
            
            # Initialize workflow service and start the workflow
            workflow_service = WorkFlowService(db)
            execution_result = await workflow_service.start_workflow_by_id(
                workflow_id=workflow_id,
                input_data={
                    "webhook_data": webhook_data,
                    "node_id": webhook_config["node_id"]
                }
            )
            
            logger.info(
                f"Webhook triggered workflow execution",
                webhook_path=webhook_path,
                client_ip=webhook_data['client_ip'],
                workflow_id=workflow_id,
                execution_id=execution_result.get("execution_id")
            )
            
            # Return success response
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Webhook received and workflow triggered successfully",
                    "webhook_id": webhook_config["webhook_id"],
                    "workflow_execution_id": execution_result.get("execution_id"),
                    "timestamp": webhook_data["timestamp"]
                }
            )
            
        except Exception as e:
            logger.error(
                f"Failed to trigger workflow from webhook: {str(e)}",
                webhook_path=webhook_path,
                workflow_id=webhook_config.get("workflow_id"),
                error=str(e)
            )
            
            # Return success for the webhook but include error about workflow
            return JSONResponse(
                status_code=202,
                content={
                    "message": "Webhook received but workflow execution failed",
                    "webhook_id": webhook_config["webhook_id"],
                    "error": str(e),
                    "timestamp": webhook_data["timestamp"]
                }
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling webhook {webhook_path}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
