"""
Main API router for version 1 endpoints.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, credential, health, node, users, workflows, tags, event_types, webhook, schemas, records

api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    health.router,
    tags=["health"]
)

api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    node.router,
    prefix="/nodes",
    tags=["nodes"]
)

api_router.include_router(
    credential.router,
    prefix="/credentials",
    tags=["credentials"]
)

api_router.include_router(
    workflows.router,
    prefix="/workflows",
    tags=["workflows"]
)

api_router.include_router(
    tags.router,
    prefix="/tags",
    tags=["tags"]
)

api_router.include_router(
    webhook.router,
    prefix="/webhooks",
    tags=["webhooks"]
)

api_router.include_router(
    event_types.router,
    prefix="/event-types",
    tags=["event-types"]
)

api_router.include_router(
    schemas.router,
    prefix="/schemas",
    tags=["schemas"]
)

api_router.include_router(
    records.router,
    prefix="/records",
    tags=["records"]
)
