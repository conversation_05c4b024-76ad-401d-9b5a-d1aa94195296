 
from typing import Any
from app.node.node_base.node import Node<PERSON><PERSON>ult
from app.node.node_base.node_models import <PERSON>deData
from temporalio import activity

from app.node.node_utils.registry import node_registry

@activity.defn(name="workflow_activity")
async def workflow_activity(node_data: NodeData) ->  NodeResult:
    """
    Wrapper function to run a workflow activity.
    This function is registered as an activity in Temporal and can be called from workflows.
    :param callable: The function to execute as an activity.
    :return: The result of the activity execution.
    """    
    try:
        node = node_registry.get_node_class(node_data.type)
        return await node().run(node_data)  # Run the node with the provided NodeData
    except Exception as e:
        # Handle exceptions and log them if necessary
        raise RuntimeError(f"Error executing workflow activity: {str(e)}")
