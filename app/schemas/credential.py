"""
Credential-related Pydantic schemas for request/response validation.
"""

from typing import List, Optional

from pydantic import Field, field_validator

from app.schemas.common import BaseSchema, IDMixin, TimestampMixin


class CredentialBase(BaseSchema):
    """Base credential schema with common fields."""
    
    name: str = Field(..., min_length=1, max_length=255)
    type: str = Field(..., min_length=1, max_length=100)
    auth_method: str = Field(..., min_length=1, max_length=50)
    status: str = Field(default="pending", min_length=1, max_length=50)
    allowed_nodes: List[str] = Field(default_factory=list)
    created_by: str = Field(..., min_length=1, max_length=255)


class CredentialCreate(CredentialBase):
    """Schema for creating a new credential."""
    
    data: str = Field(...) # Unencrypted data that will be encrypted before storage
    
    @field_validator("auth_method")
    def validate_auth_method(cls, v):
        """Validate credential auth method."""
        valid_auth_methods = ["bearer", "jwt", "oauth2", "basic", "api_key", "custom"]
        if v not in valid_auth_methods:
            raise ValueError(f"Invalid credential auth method. Must be one of: {', '.join(valid_auth_methods)}")
        return v
    
    @field_validator("status")
    def validate_status(cls, v):
        """Validate credential status."""
        valid_statuses = ["active", "inactive", "expired", "failed", "pending"]
        if v not in valid_statuses:
            raise ValueError(f"Invalid credential status. Must be one of: {', '.join(valid_statuses)}")
        return v


class CredentialUpdate(BaseSchema):
    """Schema for updating an existing credential."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    type: Optional[str] = Field(None, min_length=1, max_length=100)
    auth_method: Optional[str] = Field(None, min_length=1, max_length=50)
    status: Optional[str] = Field(None, min_length=1, max_length=50)
    data: Optional[str] = None
    allowed_nodes: Optional[List[str]] = None
    is_deleted: Optional[bool] = None
    
    @field_validator("auth_method")
    def validate_auth_method(cls, v):
        """Validate credential auth method."""
        if v is None:
            return v
        valid_auth_methods = ["bearer", "jwt", "oauth2", "basic", "api_key", "custom"]
        if v not in valid_auth_methods:
            raise ValueError(f"Invalid credential auth method. Must be one of: {', '.join(valid_auth_methods)}")
        return v
    
    @field_validator("status")
    def validate_status(cls, v):
        """Validate credential status."""
        if v is None:
            return v
        valid_statuses = ["active", "inactive", "expired", "failed", "pending"]
        if v not in valid_statuses:
            raise ValueError(f"Invalid credential status. Must be one of: {', '.join(valid_statuses)}")
        return v


class CredentialInDBBase(CredentialBase, IDMixin, TimestampMixin):
    """Schema for credential as stored in the database (without sensitive data)."""
    is_deleted: bool = False


class Credential(CredentialInDBBase):
    """Schema for credential response (public representation)."""
    pass


class CredentialDetail(CredentialInDBBase):
    """Schema for credential detail response (including sensitive data)."""
    data: str  # This would be the decrypted data in responses where appropriate
