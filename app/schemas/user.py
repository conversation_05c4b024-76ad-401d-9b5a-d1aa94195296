"""
User-related Pydantic schemas for request/response validation.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import EmailStr, Field, validator

from app.schemas.auth import RoleSchema
from app.schemas.common import BaseSchema, IDMixin, TimestampMixin


class UserBase(BaseSchema):
    """Base user schema with common fields."""
    
    email: EmailStr
    full_name: str = Field(..., min_length=1, max_length=255)
    is_active: bool = True
    is_verified: bool = False
    avatar_url: Optional[str] = Field(None, max_length=500)
    bio: Optional[str] = Field(None, max_length=1000)
    timezone: str = Field(default="UTC", max_length=50)
    language: str = Field(default="en", max_length=10)


class UserCreate(UserBase):
    """Schema for creating a new user."""
    
    password: str = Field(..., min_length=8, max_length=100)
    is_superuser: bool = False
    role_ids: Optional[List[int]] = []
    
    @validator("password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )
        
        return v
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "password": "SecurePass123!",
                "is_active": True,
                "is_verified": False,
                "timezone": "UTC",
                "language": "en"
            }
        }
    }


class UserUpdate(BaseSchema):
    """Schema for updating user information."""
    
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=1, max_length=255)
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    avatar_url: Optional[str] = Field(None, max_length=500)
    bio: Optional[str] = Field(None, max_length=1000)
    timezone: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)
    role_ids: Optional[List[int]] = None


class UserInDB(UserBase, IDMixin, TimestampMixin):
    """Schema for user data stored in database."""
    
    is_superuser: bool = False
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None


class User(UserInDB):
    """Public user schema (excludes sensitive information)."""
    
    roles: List[RoleSchema] = []
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "id": 1,
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "is_active": True,
                "is_verified": True,
                "is_superuser": False,
                "avatar_url": "https://example.com/avatar.jpg",
                "bio": "Software developer",
                "timezone": "UTC",
                "language": "en",
                "last_login": "2024-01-01T12:00:00Z",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z",
                "roles": [
                    {
                        "id": 1,
                        "name": "editor",
                        "description": "Can read and write content",
                        "is_default": False,
                        "permissions": []
                    }
                ]
            }
        }
    }


class UserProfile(BaseSchema):
    """User profile schema for current user."""
    
    id: int
    email: EmailStr
    full_name: str
    is_active: bool
    is_verified: bool
    is_superuser: bool
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    timezone: str
    language: str
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    roles: List[str] = []  # Role names
    permissions: List[str] = []  # Permission names


class UserList(BaseSchema):
    """Schema for user list items."""
    
    id: int
    email: EmailStr
    full_name: str
    is_active: bool
    is_verified: bool
    is_superuser: bool
    avatar_url: Optional[str] = None
    last_login: Optional[datetime] = None
    created_at: datetime
    roles: List[str] = []  # Role names only


class UserStats(BaseSchema):
    """User statistics schema."""
    
    total_users: int
    active_users: int
    verified_users: int
    superusers: int
    locked_users: int
    users_by_role: dict[str, int]
    recent_registrations: int  # Last 30 days
