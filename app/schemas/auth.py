"""
Authentication and authorization schemas.
"""

from typing import List, Optional

from pydantic import BaseModel, EmailStr, Field, validator

from app.schemas.common import BaseSchema


class Token(BaseSchema):
    """JWT token response schema."""
    
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # seconds


class TokenData(BaseSchema):
    """Token payload data schema."""
    
    sub: Optional[str] = None
    type: Optional[str] = None


class LoginRequest(BaseSchema):
    """User login request schema."""
    
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=100)
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123"
            }
        }
    }


class RefreshTokenRequest(BaseSchema):
    """Refresh token request schema."""
    
    refresh_token: str


class PasswordResetRequest(BaseSchema):
    """Password reset request schema."""
    
    email: EmailStr
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>"
            }
        }
    }


class PasswordResetConfirm(BaseSchema):
    """Password reset confirmation schema."""
    
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )
        
        return v


class ChangePasswordRequest(BaseSchema):
    """Change password request schema."""
    
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)
    
    @validator("new_password")
    def validate_password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one digit, and one special character"
            )
        
        return v


class PermissionSchema(BaseSchema):
    """Permission schema."""
    
    id: int
    name: str
    description: Optional[str] = None
    resource: str
    action: str


class RoleSchema(BaseSchema):
    """Role schema."""
    
    id: int
    name: str
    description: Optional[str] = None
    is_default: bool = False
    permissions: List[PermissionSchema] = []


class RoleCreate(BaseSchema):
    """Role creation schema."""
    
    name: str = Field(..., min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    is_default: bool = False
    permission_ids: List[int] = []


class RoleUpdate(BaseSchema):
    """Role update schema."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=50)
    description: Optional[str] = Field(None, max_length=255)
    is_default: Optional[bool] = None
    permission_ids: Optional[List[int]] = None


class UserRoleAssignment(BaseSchema):
    """User role assignment schema."""
    
    user_id: int
    role_ids: List[int]
