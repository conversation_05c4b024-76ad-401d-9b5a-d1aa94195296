"""
Tag-related Pydantic schemas for request/response validation.
"""

from typing import List, Optional

from pydantic import Field, field_validator

from app.schemas.common import BaseSchema, IDMixin, TimestampMixin


class TagBase(BaseSchema):
    """Base tag schema with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Tag name")
    created_by: int = Field(..., description="ID of the user who created this tag")
    edited_by: Optional[int] = Field(None, description="ID of the user who last edited this tag")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate tag name format."""
        if not v.strip():
            raise ValueError('Tag name cannot be empty or whitespace only')
        # Remove extra whitespace and convert to lowercase for consistency
        return v.strip().lower()


class TagCreate(BaseSchema):
    """Schema for creating a new tag."""

    name: str = Field(..., min_length=1, max_length=100, description="Tag name")
    created_by: Optional[int] = Field(None, description="ID of the user who created this tag (set automatically)")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate tag name format."""
        if not v.strip():
            raise ValueError('Tag name cannot be empty or whitespace only')
        # Remove extra whitespace and convert to lowercase for consistency
        return v.strip().lower()

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "production",
                "created_by": 1
            }
        }
    }


class TagUpdate(BaseSchema):
    """Schema for updating an existing tag."""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Tag name")
    edited_by: Optional[int] = Field(None, description="ID of the user who is editing this tag (set automatically)")

    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate tag name format."""
        if v is not None:
            if not v.strip():
                raise ValueError('Tag name cannot be empty or whitespace only')
            # Remove extra whitespace and convert to lowercase for consistency
            return v.strip().lower()
        return v


class TagInDB(TagBase, IDMixin, TimestampMixin):
    """Schema for tag as stored in the database."""
    
    usage_count: int = Field(default=0, ge=0, description="Number of times this tag is used")


class Tag(TagInDB):
    """Schema for tag response."""
    pass


class TagDetail(TagInDB):
    """Schema for detailed tag response with usage statistics."""
    
    # Could include additional computed fields like related workflows count
    pass


class TagList(BaseSchema):
    """Schema for tag list response."""
    
    tags: List[Tag]
    total: int
    page: int
    size: int
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "tags": [
                    {
                        "id": 1,
                        "name": "production",
                        "usage_count": 5,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T00:00:00Z"
                    }
                ],
                "total": 1,
                "page": 1,
                "size": 10
            }
        }
    }


class TagUsageStats(BaseSchema):
    """Schema for tag usage statistics."""
    
    tag_id: int
    tag_name: str
    workflow_count: int = Field(description="Number of workflows using this tag")
    version_count: int = Field(description="Number of workflow versions using this tag")
    total_usage: int = Field(description="Total usage count")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "tag_id": 1,
                "tag_name": "production",
                "workflow_count": 3,
                "version_count": 2,
                "total_usage": 5
            }
        }
    }
