# API Response Middleware System

## Overview

The API Response Middleware system provides comprehensive standardization of all API responses across the entire FastAPI application. It automatically wraps all endpoint responses in a consistent JSON structure and sets appropriate HTTP status codes.

## Features

- **Automatic Response Wrapping**: All API responses are automatically wrapped in BaseResponse format
- **HTTP Status Code Mapping**: Intelligent mapping of response types to appropriate HTTP status codes
- **Error Code Standardization**: Consistent error codes across all endpoints
- **Content-Type Filtering**: Only applies to JSON API responses, not static files
- **Backward Compatibility**: Existing endpoint code remains unchanged
- **Performance Optimized**: Minimal overhead for API performance
- **Exception Handling**: Automatic catching and formatting of unhandled exceptions

## Response Structure

### Success Responses
```json
{
  "status": true,
  "message": "Operation successful",
  "data": {
    "user_id": 123,
    "email": "<EMAIL>"
  }
}
```

### Error Responses
```json
{
  "status": false,
  "error": "Could not validate credentials",
  "error_code": "AUTHENTICATION_ERROR"
}
```

## Standardized Error Codes

| Error Code | HTTP Status | Description |
|------------|-------------|-------------|
| `VALIDATION_ERROR` | 422 | Input validation failures |
| `NOT_FOUND` | 404 | Resource not found errors |
| `AUTHENTICATION_ERROR` | 401 | Authentication failures |
| `AUTHORIZATION_ERROR` | 403 | Permission/access errors |
| `CONFLICT_ERROR` | 409 | Resource conflicts |
| `HTTP_ERROR` | 500 | Default fallback for generic errors |

## Implementation

### Middleware Configuration

The middleware is configured in `app/main.py`:

```python
from app.middleware.response_middleware import APIResponseMiddleware

# Add API response standardization middleware
app.add_middleware(
    APIResponseMiddleware,
    api_prefix=settings.API_V1_STR
)
```

### BaseResponse Class

The `BaseResponse` class in `app/schemas/common.py` provides the foundation:

```python
# Success response
response = BaseResponse.success("User created successfully", data=user_data)

# Error response (new style)
response = BaseResponse.error("Authentication failed", "AUTHENTICATION_ERROR")

# Error response (backward compatible)
response = BaseResponse.error("User not found")  # auto-infers "NOT_FOUND"
```

## Middleware Behavior

### Request Processing

1. **Route Filtering**: Only processes requests under `/api/v1/` prefix
2. **Content-Type Filtering**: Only applies to JSON responses
3. **Skip Paths**: Automatically skips documentation, static files, and other non-API endpoints

### Response Processing

1. **BaseResponse Detection**: Checks if response is already in BaseResponse format
2. **Automatic Wrapping**: Wraps non-BaseResponse data in standardized format
3. **Status Code Mapping**: Maps response types to appropriate HTTP status codes
4. **Error Extraction**: Intelligently extracts error messages from various response formats

### Exception Handling

The middleware includes comprehensive exception handling:

- **Unhandled Exceptions**: Automatically caught and formatted as standardized error responses
- **Validation Errors**: FastAPI validation errors are converted to standardized format
- **HTTP Exceptions**: Starlette HTTP exceptions are mapped to appropriate error codes
- **Custom Exceptions**: CerebroException instances are handled with proper error codes

## Integration with Existing Code

### No Code Changes Required

Existing endpoints continue to work without modification:

```python
# This endpoint code remains unchanged
@router.post("/login")
async def login(login_data: LoginRequest):
    # ... existing logic ...
    return BaseResponse.success("Login successful", data=user_data)
```

### Automatic Standardization

Non-BaseResponse returns are automatically standardized:

```python
# This endpoint returns raw data
@router.get("/users/{user_id}")
async def get_user(user_id: int):
    return {"user_id": user_id, "name": "John Doe"}

# Middleware automatically wraps it as:
# {
#   "status": true,
#   "message": "Operation successful",
#   "data": {"user_id": 123, "name": "John Doe"}
# }
```

## Performance Considerations

- **Minimal Overhead**: Middleware adds negligible latency to API responses
- **Efficient Processing**: Only processes relevant API requests
- **Memory Optimized**: Efficient response data extraction and transformation
- **Logging**: Structured logging for debugging and monitoring

## Testing

The middleware includes comprehensive test coverage:

```python
# Test middleware functionality
from app.middleware.response_middleware import APIResponseMiddleware
from fastapi.testclient import TestClient

client = TestClient(app)

# Test standardized error response
response = client.get("/api/v1/nonexistent")
assert response.status_code == 404
assert response.json()["status"] == False
assert response.json()["error_code"] == "NOT_FOUND"

# Test standardized success response
response = client.get("/api/v1/health")
assert response.status_code == 200
assert response.json()["status"] == True
```

## Configuration Options

### API Prefix

Configure which routes are processed by the middleware:

```python
app.add_middleware(
    APIResponseMiddleware,
    api_prefix="/api/v1"  # Only process routes starting with /api/v1
)
```

### Skip Paths

The middleware automatically skips certain paths:

- `/api/v1/docs` - API documentation
- `/api/v1/redoc` - ReDoc documentation
- `/api/v1/openapi.json` - OpenAPI schema
- `/static/` - Static files
- `/favicon.ico` - Favicon

## Monitoring and Debugging

### Structured Logging

The middleware provides detailed logging:

```python
logger.debug(
    "Response standardized",
    path=request.url.path,
    method=request.method,
    original_status=response.status_code,
    final_status=final_status_code,
    response_type="success" if standardized_response.get("status") else "error"
)
```

### Error Tracking

All exceptions are logged with full context:

```python
logger.error(
    "Unhandled exception in response middleware",
    path=request.url.path,
    method=request.method,
    error=str(exc),
    traceback=traceback.format_exc() if settings.DEBUG else None
)
```

## Migration Notes

### Backward Compatibility

- **100% Compatible**: All existing BaseResponse.success() and BaseResponse.error() calls work unchanged
- **Auto-Inference**: Error codes are automatically inferred when not provided
- **Data Handling**: Existing data parameters continue to work as expected

### Benefits

- **Consistency**: All API responses follow the same structure
- **Error Handling**: Standardized error codes and messages
- **Documentation**: Automatic OpenAPI schema generation
- **Client Integration**: Simplified client-side response handling
- **Debugging**: Better error tracking and logging

## Troubleshooting

### Common Issues

1. **Response Not Standardized**: Check if endpoint is under configured API prefix
2. **Wrong Status Code**: Verify error code mapping in middleware
3. **Missing Data**: Ensure BaseResponse.success() includes data parameter
4. **Performance Issues**: Check middleware ordering and request filtering

### Debug Mode

Enable debug logging to see middleware processing:

```python
import logging
logging.getLogger("middleware.response").setLevel(logging.DEBUG)
```
