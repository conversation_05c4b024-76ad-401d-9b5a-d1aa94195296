# Development overrides for docker-compose.yml
services:
  app:
    environment:
      - DEBUG=true
      - LOG_LEVEL=debug
      - RELOAD=true
    volumes:
      - ./app:/app/app
      - ./tests:/app/tests
      - ./alembic:/app/alembic
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

  postgres:
    environment:
      - POSTGRES_HOST_AUTH_METHOD=trust
    ports:
      - "5434:5432"

  mongodb:
    ports:
      - "27017:27017"

  redis:
    ports:
      - "6379:6379"

  rabbitmq:
    ports:
      - "5672:5672"
      - "15672:15672"
