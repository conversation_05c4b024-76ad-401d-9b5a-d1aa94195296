services:
  # PostgreSQL Database
  postgres:
    image: postgres:13
    container_name: cerebro_postgres
    environment:
      POSTGRES_DB: cerebro
      POSTGRES_USER: cerebro_user
      POSTGRES_PASSWORD: cerebro_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_data_v2:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB} -h localhost || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    networks:
      - cerebro_network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:8.0
    container_name: cerebro_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: cerebro_user
      MONGO_INITDB_ROOT_PASSWORD: cerebro_password
      MONGO_INITDB_DATABASE: cerebro
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: ["<PERSON><PERSON>", "mongosh", "--quiet", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - cerebro_network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7.4-alpine
    container_name: cerebro_redis
    command: redis-server --appendonly yes --requirepass cerebro_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "cerebro_password", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - cerebro_network
    restart: unless-stopped

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:4.0-management-alpine
    container_name: cerebro_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: cerebro_user
      RABBITMQ_DEFAULT_PASS: cerebro_password
      RABBITMQ_DEFAULT_VHOST: cerebro
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - cerebro_network
    restart: unless-stopped

  # Temporal Server
  temporal:
    image: temporalio/auto-setup:latest
    container_name: temporal-server-1
    ports:
      # Temporal server gRPC endpoint
      - "7234:7233"
      # Temporal web UI
      - "8085:8080"
      # Temporal frontend service
      - "8234:8233"
    environment:
      # Database configuration for Temporal (use postgres12 for auto-setup)
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=cerebro_user
      - POSTGRES_PWD=cerebro_password
      - POSTGRES_SEEDS=postgres
      # Skip schema setup if database already exists
      - SKIP_SCHEMA_SETUP=false
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD", "echo", "ok"]
      # test: ["CMD", "sh", "-c", "nc -z localhost 7234 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Temporal Web UI
  temporal-ui:
    image: temporalio/ui:latest
    container_name: temporal-ui1
    ports:
      - "8086:8080"
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    depends_on:
      - temporal
    networks:
      - cerebro_network

  # FastAPI Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cerebro_app
    environment:
      - DATABASE_URL=postgresql+asyncpg://cerebro_user:cerebro_password@postgres:5432/cerebro
      - MONGODB_URL=******************************************************************************
      - REDIS_URL=redis://:cerebro_password@redis:6379/0
      - RABBITMQ_URL=amqp://cerebro_user:cerebro_password@rabbitmq:5672/cerebro
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=true
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_REGION=us-east-1
      - LOCALSTACK_URL=http://localstack:4566
    ports:
      - "8008:8000"
    volumes:
      - ./app:/app/app
      - ./tests:/app/tests
      - ./alembic:/app/alembic
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      temporal:
        condition: service_healthy
      localstack:
        condition: service_healthy
    networks:
      - cerebro_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8008 --reload
    restart: unless-stopped

  # LocalStack
  localstack:
    build:
      context: ./localstack-init
      dockerfile: Dockerfile
    container_name: cerebro_localstack
    ports:
      - "4566:4566"            # LocalStack Gateway
      - "4571:4571"            # LocalStack S3
      - "4572:4572"            # LocalStack API Gateway
      - "4574:4574"            # LocalStack Lambda
    environment:
      - SERVICES=s3,iam,apigateway,lambda
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD-SHELL", "awslocal s3 ls"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped

volumes:
  postgres_data_v2:
  mongodb_data:
  redis_data:
  rabbitmq_data:

networks:
  cerebro_network:
    driver: bridge
