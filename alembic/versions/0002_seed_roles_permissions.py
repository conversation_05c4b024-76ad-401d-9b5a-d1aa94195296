"""Seed initial roles and permissions

Revision ID: 0002
Revises: 0001
Create Date: 2024-01-01 00:01:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column


# revision identifiers, used by Alembic.
revision = '0002'
down_revision = '0001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Define table structures for data insertion
    permissions_table = table('permissions',
        column('id', sa.Integer),
        column('name', sa.String),
        column('description', sa.String),
        column('resource', sa.String),
        column('action', sa.String),
        column('created_at', sa.DateTime),
        column('updated_at', sa.DateTime)
    )
    
    roles_table = table('roles',
        column('id', sa.Integer),
        column('name', sa.String),
        column('description', sa.String),
        column('is_default', sa.Bo<PERSON>an),
        column('created_at', sa.DateTime),
        column('updated_at', sa.DateTime)
    )
    
    role_permissions_table = table('role_permissions',
        column('role_id', sa.Integer),
        column('permission_id', sa.Integer)
    )
    
    # Insert permissions
    permissions_data = [
        # User permissions
        {'id': 1, 'name': 'user:read', 'description': 'Read user data', 'resource': 'user', 'action': 'read'},
        {'id': 2, 'name': 'user:write', 'description': 'Write user data', 'resource': 'user', 'action': 'write'},
        {'id': 3, 'name': 'user:delete', 'description': 'Delete user data', 'resource': 'user', 'action': 'delete'},
        
        # Admin permissions
        {'id': 4, 'name': 'admin:read', 'description': 'Read admin data', 'resource': 'admin', 'action': 'read'},
        {'id': 5, 'name': 'admin:write', 'description': 'Write admin data', 'resource': 'admin', 'action': 'write'},
        {'id': 6, 'name': 'admin:delete', 'description': 'Delete admin data', 'resource': 'admin', 'action': 'delete'},
        
        # Role permissions
        {'id': 7, 'name': 'role:read', 'description': 'Read role data', 'resource': 'role', 'action': 'read'},
        {'id': 8, 'name': 'role:write', 'description': 'Write role data', 'resource': 'role', 'action': 'write'},
        {'id': 9, 'name': 'role:delete', 'description': 'Delete role data', 'resource': 'role', 'action': 'delete'},
        
        # Permission permissions
        {'id': 10, 'name': 'permission:read', 'description': 'Read permission data', 'resource': 'permission', 'action': 'read'},
        {'id': 11, 'name': 'permission:write', 'description': 'Write permission data', 'resource': 'permission', 'action': 'write'},
        {'id': 12, 'name': 'permission:delete', 'description': 'Delete permission data', 'resource': 'permission', 'action': 'delete'},
        
        # System permissions
        {'id': 13, 'name': 'system:read', 'description': 'Read system data', 'resource': 'system', 'action': 'read'},
        {'id': 14, 'name': 'system:write', 'description': 'Write system data', 'resource': 'system', 'action': 'write'},
        {'id': 15, 'name': 'system:admin', 'description': 'System administration', 'resource': 'system', 'action': 'admin'},
    ]
    
    # Add timestamps
    from datetime import datetime
    now = datetime.utcnow()
    for perm in permissions_data:
        perm['created_at'] = now
        perm['updated_at'] = now
    
    op.bulk_insert(permissions_table, permissions_data)
    
    # Insert roles
    roles_data = [
        {'id': 1, 'name': 'owner', 'description': 'System owner with full access', 'is_default': False},
        {'id': 2, 'name': 'admin', 'description': 'Administrator with management access', 'is_default': False},
        {'id': 3, 'name': 'editor', 'description': 'Editor with read and write access', 'is_default': False},
        {'id': 4, 'name': 'viewer', 'description': 'Viewer with read-only access', 'is_default': True},
    ]
    
    # Add timestamps
    for role in roles_data:
        role['created_at'] = now
        role['updated_at'] = now
    
    op.bulk_insert(roles_table, roles_data)
    
    # Assign permissions to roles
    role_permission_assignments = [
        # Owner - all permissions
        {'role_id': 1, 'permission_id': 1},   # user:read
        {'role_id': 1, 'permission_id': 2},   # user:write
        {'role_id': 1, 'permission_id': 3},   # user:delete
        {'role_id': 1, 'permission_id': 4},   # admin:read
        {'role_id': 1, 'permission_id': 5},   # admin:write
        {'role_id': 1, 'permission_id': 6},   # admin:delete
        {'role_id': 1, 'permission_id': 7},   # role:read
        {'role_id': 1, 'permission_id': 8},   # role:write
        {'role_id': 1, 'permission_id': 9},   # role:delete
        {'role_id': 1, 'permission_id': 10},  # permission:read
        {'role_id': 1, 'permission_id': 11},  # permission:write
        {'role_id': 1, 'permission_id': 12},  # permission:delete
        {'role_id': 1, 'permission_id': 13},  # system:read
        {'role_id': 1, 'permission_id': 14},  # system:write
        {'role_id': 1, 'permission_id': 15},  # system:admin
        
        # Admin - management permissions
        {'role_id': 2, 'permission_id': 1},   # user:read
        {'role_id': 2, 'permission_id': 2},   # user:write
        {'role_id': 2, 'permission_id': 3},   # user:delete
        {'role_id': 2, 'permission_id': 4},   # admin:read
        {'role_id': 2, 'permission_id': 5},   # admin:write
        {'role_id': 2, 'permission_id': 7},   # role:read
        {'role_id': 2, 'permission_id': 8},   # role:write
        {'role_id': 2, 'permission_id': 10},  # permission:read
        {'role_id': 2, 'permission_id': 13},  # system:read
        
        # Editor - read and write permissions
        {'role_id': 3, 'permission_id': 1},   # user:read
        {'role_id': 3, 'permission_id': 2},   # user:write
        {'role_id': 3, 'permission_id': 7},   # role:read
        {'role_id': 3, 'permission_id': 10},  # permission:read
        
        # Viewer - read-only permissions
        {'role_id': 4, 'permission_id': 1},   # user:read
        {'role_id': 4, 'permission_id': 7},   # role:read
        {'role_id': 4, 'permission_id': 10},  # permission:read
    ]
    
    op.bulk_insert(role_permissions_table, role_permission_assignments)


def downgrade() -> None:
    # Remove all role-permission assignments
    op.execute("DELETE FROM role_permissions")
    
    # Remove all roles
    op.execute("DELETE FROM roles")
    
    # Remove all permissions
    op.execute("DELETE FROM permissions")
