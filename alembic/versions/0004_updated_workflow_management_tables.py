"""Add workflow management tables (WorkFlow, WorkFlowVersion, Tag) with updated schema

Revision ID: 0004
Revises: 0003
Create Date: 2025-07-07 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB


# revision identifiers, used by Alembic.
revision = '0004'
down_revision = '0003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Ensure the uuid-ossp extension is enabled for UUID generation
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    # Create tag table first (referenced by other tables)
    op.create_table('tag',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('usage_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('edited_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['edited_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    
    # Create indexes for tag table
    op.create_index(op.f('ix_tag_created_by'), 'tag', ['created_by'], unique=False)
    op.create_index(op.f('ix_tag_edited_by'), 'tag', ['edited_by'], unique=False)
    op.create_index(op.f('ix_tag_name'), 'tag', ['name'], unique=False)
    op.create_index(op.f('ix_tag_usage_count'), 'tag', ['usage_count'], unique=False)
    
    # Create work_flow table with UUID primary key and updated schema
    op.create_table('work_flow',
        sa.Column('uid', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('status', sa.Enum('DRAFT', 'PUBLISHED', 'ARCHIVED', 'DEPRECATED', name='workflowstatus'), nullable=False, server_default='DRAFT'),
        sa.Column('active_version_id', sa.Integer(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('edited_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['edited_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('uid')
    )
    
    # Create indexes for work_flow table
    op.create_index(op.f('ix_work_flow_uid'), 'work_flow', ['uid'], unique=False)
    op.create_index(op.f('ix_work_flow_name'), 'work_flow', ['name'], unique=False)
    op.create_index(op.f('ix_work_flow_is_active'), 'work_flow', ['is_active'], unique=False)
    op.create_index(op.f('ix_work_flow_status'), 'work_flow', ['status'], unique=False)
    op.create_index(op.f('ix_work_flow_active_version_id'), 'work_flow', ['active_version_id'], unique=False)
    op.create_index(op.f('ix_work_flow_created_by'), 'work_flow', ['created_by'], unique=False)
    op.create_index(op.f('ix_work_flow_edited_by'), 'work_flow', ['edited_by'], unique=False)
    
    # Create work_flow_version table with updated schema
    op.create_table('work_flow_version',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('version_no', sa.Integer(), nullable=False),
        sa.Column('work_flow', JSONB(), nullable=False),
        sa.Column('workflow_id', UUID(as_uuid=True), nullable=False),
        sa.Column('version_name', sa.String(length=255), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('edited_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['edited_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['workflow_id'], ['work_flow.uid'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('workflow_id', 'version_no', name='uq_workflow_version')
    )
    
    # Create indexes for work_flow_version table
    op.create_index(op.f('ix_work_flow_version_version_no'), 'work_flow_version', ['version_no'], unique=False)
    op.create_index(op.f('ix_work_flow_version_workflow_id'), 'work_flow_version', ['workflow_id'], unique=False)
    op.create_index(op.f('ix_work_flow_version_version_name'), 'work_flow_version', ['version_name'], unique=False)
    op.create_index(op.f('ix_work_flow_version_created_by'), 'work_flow_version', ['created_by'], unique=False)
    op.create_index(op.f('ix_work_flow_version_edited_by'), 'work_flow_version', ['edited_by'], unique=False)
    
    # Create workflow_tags association table for many-to-many relationship
    op.create_table('workflow_tags',
        sa.Column('workflow_id', UUID(as_uuid=True), nullable=False),
        sa.Column('tag_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], ),
        sa.ForeignKeyConstraint(['workflow_id'], ['work_flow.uid'], ),
        sa.PrimaryKeyConstraint('workflow_id', 'tag_id')
    )
    
    # Add foreign key constraint for active_version_id after work_flow_version table is created
    op.create_foreign_key(
        'fk_work_flow_active_version_id',
        'work_flow',
        'work_flow_version',
        ['active_version_id'],
        ['id']
    )


def downgrade() -> None:
    # Drop foreign key constraint first
    op.drop_constraint('fk_work_flow_active_version_id', 'work_flow', type_='foreignkey')

    # Drop workflow_tags association table
    op.drop_table('workflow_tags')

    # Drop work_flow_version table and its indexes
    op.drop_index(op.f('ix_work_flow_version_edited_by'), table_name='work_flow_version')
    op.drop_index(op.f('ix_work_flow_version_created_by'), table_name='work_flow_version')
    
    # op.drop_index(op.f('ix_work_flow_version_version_name'), table_name='work_flow_version')
    op.drop_index(op.f('ix_work_flow_version_workflow_id'), table_name='work_flow_version')
    op.drop_index(op.f('ix_work_flow_version_version_no'), table_name='work_flow_version')
    op.drop_table('work_flow_version')

    # Drop work_flow table and its indexes
    op.drop_index(op.f('ix_work_flow_edited_by'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_created_by'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_active_version_id'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_status'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_is_active'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_name'), table_name='work_flow')
    op.drop_index(op.f('ix_work_flow_uid'), table_name='work_flow')
    op.drop_table('work_flow')

    # Drop the enum type
    op.execute('DROP TYPE IF EXISTS workflowstatus')

    # Drop tag table and its indexes
    op.drop_index(op.f('ix_tag_usage_count'), table_name='tag')
    op.drop_index(op.f('ix_tag_name'), table_name='tag')
    op.drop_index(op.f('ix_tag_edited_by'), table_name='tag')
    op.drop_index(op.f('ix_tag_created_by'), table_name='tag')
    op.drop_table('tag')
