"""Add credentials table for storing authentication credentials

Revision ID: 0003
Revises: 0002
Create Date: 2025-07-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, ARRAY


# revision identifiers, used by Alembic.
revision = '0003'
down_revision = '0002'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # First, ensure the uuid-ossp extension is enabled for UUID generation
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    # Create credentials table with UUID primary key
    op.create_table('credentials',
        sa.Column('id', UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), onupdate=sa.text('now()'), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('type', sa.String(length=100), nullable=False),
        sa.Column('auth_method', sa.String(length=50), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=False, server_default='active'),
        sa.Column('data', sa.Text(), nullable=False),
        sa.Column('allowed_nodes', ARRAY(sa.String(length=100)), nullable=False, server_default='{}'),
        sa.Column('created_by', sa.String(length=255), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for better query performance
    op.create_index(op.f('ix_credentials_id'), 'credentials', ['id'], unique=False)
    op.create_index(op.f('ix_credentials_type'), 'credentials', ['type'], unique=False)
    op.create_index(op.f('ix_credentials_name'), 'credentials', ['name'], unique=False)
    op.create_index(op.f('ix_credentials_status'), 'credentials', ['status'], unique=False)


def downgrade() -> None:
    # Drop all indexes first
    op.drop_index(op.f('ix_credentials_status'), table_name='credentials')
    op.drop_index(op.f('ix_credentials_name'), table_name='credentials')
    op.drop_index(op.f('ix_credentials_type'), table_name='credentials')
    op.drop_index(op.f('ix_credentials_id'), table_name='credentials')
    
    # Drop the table
    op.drop_table('credentials')
