# Testing
pytest>=8.4.0,<8.5.0
pytest-asyncio>=0.25.0,<0.26.0
pytest-cov>=6.1.0,<6.2.0
pytest-mock>=3.14.0,<3.15.0
pytest-xdist>=3.7.0,<3.8.0
httpx>=0.28.0,<0.29.0
faker>=34.0.0,<35.0.0

# Code Quality
black>=25.0.0,<26.0.0
isort>=5.13.0,<5.14.0
flake8>=7.2.0,<7.3.0
mypy>=1.14.0,<1.15.0
pre-commit>=4.1.0,<4.2.0

# Development Tools
watchdog>=6.0.0,<6.1.0
ipython>=8.31.0,<8.32.0
ipdb>=0.13.0,<0.14.0

# Documentation
mkdocs>=1.6.0,<1.7.0
mkdocs-material>=9.6.0,<9.7.0

# Database Testing
pytest-postgresql>=6.1.0,<6.2.0
pytest-mongodb>=2.4.0,<2.5.0

# Performance Testing
locust>=2.33.0,<2.34.0